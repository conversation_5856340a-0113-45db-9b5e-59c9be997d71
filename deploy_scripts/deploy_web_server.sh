#!/bin/bash

# =============================================================================
# Web Server 完整部署脚本
# =============================================================================
# 
# 功能说明：
# 1. 离线安装 point_cloud 先进算法所需的核心 Python 模块
# 2. 验证模块安装和功能
# 3. 停止并重启 web_server 服务
# 4. 验证服务状态
#
# 作者: Augment Agent
# 版本: 1.0
# 日期: 2025-06-30
# 架构: aarch64 (ARM64)
# Python: 3.7-3.10
# =============================================================================

set -e  # 遇到错误立即退出

# =============================================================================
# 配置变量
# =============================================================================

# 项目路径
PROJECT_ROOT="/home/<USER>/web_server"
OFFLINE_PACKAGES_DIR="$PROJECT_ROOT/offline_packages"
LOG_DIR="$PROJECT_ROOT/logs"
DEPLOY_LOG="$LOG_DIR/deploy_$(date +%Y%m%d_%H%M%S).log"

# 服务配置
SERVICE_NAME="web_server"
UWSGI_CONFIG="$PROJECT_ROOT/uwsgi.ini"
UWSGI_PID_FILE="$PROJECT_ROOT/uwsgi/uwsgi.pid"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# =============================================================================
# 日志函数
# =============================================================================

# 确保日志目录存在
mkdir -p "$LOG_DIR"

# 日志函数
log_message() {
    local level="$1"
    local message="$2"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    echo "[$timestamp] [$level] $message" | tee -a "$DEPLOY_LOG"
}

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
    log_message "INFO" "$1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
    log_message "SUCCESS" "$1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
    log_message "WARNING" "$1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    log_message "ERROR" "$1"
}

log_step() {
    echo -e "${PURPLE}[STEP]${NC} $1"
    log_message "STEP" "$1"
}

log_debug() {
    echo -e "${CYAN}[DEBUG]${NC} $1"
    log_message "DEBUG" "$1"
}

# =============================================================================
# 工具函数
# =============================================================================

# 打印分隔线
print_separator() {
    echo "=============================================================="
}

# 打印标题
print_header() {
    print_separator
    echo -e "${CYAN}  $1${NC}"
    print_separator
}

# 检查命令是否存在
check_command() {
    if ! command -v "$1" &> /dev/null; then
        log_error "命令 '$1' 未找到，请先安装"
        return 1
    fi
    return 0
}

# 检查文件是否存在
check_file() {
    if [ ! -f "$1" ]; then
        log_error "文件不存在: $1"
        return 1
    fi
    return 0
}

# 检查目录是否存在
check_directory() {
    if [ ! -d "$1" ]; then
        log_error "目录不存在: $1"
        return 1
    fi
    return 0
}

# 等待用户确认
wait_for_confirmation() {
    local message="$1"
    echo -e "${YELLOW}$message${NC}"
    read -p "按 Enter 继续，或 Ctrl+C 取消..."
}

# =============================================================================
# 系统检查函数
# =============================================================================

check_system_requirements() {
    log_step "检查系统要求"
    
    # 检查操作系统
    if [[ "$OSTYPE" != "linux-gnu"* ]]; then
        log_error "此脚本仅支持 Linux 系统"
        return 1
    fi
    
    # 检查架构
    local arch=$(uname -m)
    if [[ "$arch" != "aarch64" ]]; then
        log_warning "当前架构: $arch，离线包针对 aarch64 优化"
    fi
    log_info "系统架构: $arch"
    
    # 检查 Python
    if ! check_command python3; then
        return 1
    fi
    
    local python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    log_info "Python 版本: $python_version"
    
    # 检查 Python 版本兼容性
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 7) and sys.version_info < (3, 11) else 1)"; then
        log_success "Python 版本兼容"
    else
        log_error "需要 Python 3.7-3.10，当前版本: $python_version"
        return 1
    fi
    
    # 检查 pip
    if ! check_command pip3; then
        return 1
    fi
    
    local pip_version=$(pip3 --version | cut -d' ' -f2)
    log_info "pip 版本: $pip_version"
    
    # 检查磁盘空间
    local available_space=$(df "$PROJECT_ROOT" | awk 'NR==2 {print $4}')
    local required_space=262144  # 256MB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        log_error "磁盘空间不足，需要至少 256MB，可用: $(($available_space/1024))MB"
        return 1
    fi
    log_info "磁盘空间检查通过: $(($available_space/1024))MB 可用"
    
    log_success "系统要求检查完成"
    return 0
}

# =============================================================================
# 项目检查函数
# =============================================================================

check_project_structure() {
    log_step "检查项目结构"

    # 检查项目根目录
    if ! check_directory "$PROJECT_ROOT"; then
        return 1
    fi
    log_info "项目根目录: $PROJECT_ROOT"

    # 检查离线包目录
    if ! check_directory "$OFFLINE_PACKAGES_DIR"; then
        log_error "离线包目录不存在，请先创建离线安装包"
        return 1
    fi
    log_info "离线包目录: $OFFLINE_PACKAGES_DIR"

    # 检查 wheels 目录
    local wheels_dir="$OFFLINE_PACKAGES_DIR/wheels"
    if ! check_directory "$wheels_dir"; then
        return 1
    fi

    # 检查必需的 wheel 文件
    local required_wheels=(
        "kaleido-0.2.1-py2.py3-none-manylinux2014_aarch64.whl"
        "plotly-5.18.0-py3-none-any.whl"
        "scikit_learn-1.0.2-cp37-cp37m-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"
        "scipy-1.7.3-cp37-cp37m-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"
        "numpy-1.21.6-cp37-cp37m-manylinux_2_17_aarch64.manylinux2014_aarch64.whl"
    )

    local missing_wheels=()
    for wheel in "${required_wheels[@]}"; do
        if [ ! -f "$wheels_dir/$wheel" ]; then
            missing_wheels+=("$wheel")
        fi
    done

    if [ ${#missing_wheels[@]} -gt 0 ]; then
        log_error "缺少必需的 wheel 文件:"
        for wheel in "${missing_wheels[@]}"; do
            log_error "  - $wheel"
        done
        return 1
    fi

    log_info "找到 $(ls "$wheels_dir"/*.whl | wc -l) 个 wheel 文件"

    # 检查安装脚本
    local install_script="$OFFLINE_PACKAGES_DIR/scripts/install.sh"
    if check_file "$install_script"; then
        log_info "安装脚本: $install_script"
    fi

    # 检查主应用文件
    if check_file "$PROJECT_ROOT/manage.py"; then
        log_info "主应用文件: manage.py"
    fi

    log_success "项目结构检查完成"
    return 0
}

# =============================================================================
# 离线安装函数
# =============================================================================

install_offline_packages() {
    log_step "开始离线安装 Python 模块"

    local wheels_dir="$OFFLINE_PACKAGES_DIR/wheels"

    # 显示将要安装的模块
    log_info "将要安装的核心模块:"
    echo "  📊 kaleido (0.2.1)     - Plotly 静态图像导出"
    echo "  📈 plotly (5.18.0)     - 高质量 3D 可视化"
    echo "  🎯 scikit-learn (1.0.2) - DBSCAN 聚类算法"
    echo "  🔬 scipy (1.7.3)       - 科学计算"
    echo "  📐 numpy (1.21.6)      - 数值计算基础"

    wait_for_confirmation "确认开始安装离线包？"

    # 检查当前已安装的包
    log_info "检查当前已安装的包..."
    local installed_packages=$(pip3 list --format=freeze | grep -E "(kaleido|plotly|scikit-learn|scipy|numpy)" || true)
    if [ -n "$installed_packages" ]; then
        log_info "已安装的相关包:"
        echo "$installed_packages" | while read line; do
            log_info "  $line"
        done
    fi

    # 执行安装
    log_info "开始安装离线包..."

    # 使用 pip 安装，显示详细进度
    if pip3 install --no-index --find-links "$wheels_dir" \
        kaleido==0.2.1 \
        plotly==5.18.0 \
        scikit-learn==1.0.2 \
        scipy==1.7.3 \
        numpy==1.21.6 \
        --verbose 2>&1 | tee -a "$DEPLOY_LOG"; then

        log_success "离线包安装完成"
    else
        log_error "离线包安装失败"
        return 1
    fi

    return 0
}

# =============================================================================
# 安装验证函数
# =============================================================================

verify_installation() {
    log_step "验证模块安装"

    # 基本导入测试
    log_info "执行基本导入测试..."

    local test_script=$(cat << 'EOF'
import sys
import traceback

# 测试包列表
packages = {
    'numpy': '1.21.6',
    'scipy': '1.7.3',
    'sklearn': '1.0.2',
    'plotly': '5.18.0',
    'kaleido': '0.2.1'
}

print("🔍 模块导入测试")
print("-" * 40)

failed = []
for package, expected_version in packages.items():
    try:
        if package == 'sklearn':
            import sklearn
            version = sklearn.__version__
            module_name = 'scikit-learn'
        else:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            module_name = package

        print(f"✅ {module_name:15} {version:10} (期望: {expected_version})")

    except ImportError as e:
        print(f"❌ {package:15} 导入失败: {e}")
        failed.append(package)
    except Exception as e:
        print(f"❌ {package:15} 错误: {e}")
        failed.append(package)

print("-" * 40)
if failed:
    print(f"❌ 失败的包: {failed}")
    sys.exit(1)
else:
    print("🎉 所有包导入成功！")
    sys.exit(0)
EOF
)

    if python3 -c "$test_script" 2>&1 | tee -a "$DEPLOY_LOG"; then
        log_success "基本导入测试通过"
    else
        log_error "基本导入测试失败"
        return 1
    fi

    # Point Cloud 功能测试
    log_info "执行 Point Cloud 功能测试..."

    local pointcloud_test="$OFFLINE_PACKAGES_DIR/scripts/test_installation.py"
    if [ -f "$pointcloud_test" ]; then
        if python3 "$pointcloud_test" 2>&1 | tee -a "$DEPLOY_LOG"; then
            log_success "Point Cloud 功能测试通过"
        else
            log_warning "Point Cloud 功能测试失败，但基本功能可用"
        fi
    else
        log_warning "Point Cloud 测试脚本不存在，跳过详细测试"
    fi

    log_success "模块安装验证完成"
    return 0
}

# =============================================================================
# 服务管理函数
# =============================================================================

stop_web_server() {
    log_step "停止 Web Server 服务"

    # 检查是否有运行的 uWSGI 进程
    local uwsgi_pids=$(pgrep -f "uwsgi.*$PROJECT_ROOT" || true)

    if [ -z "$uwsgi_pids" ]; then
        log_info "没有发现运行中的 Web Server 进程"
        return 0
    fi

    log_info "发现运行中的进程: $uwsgi_pids"

    # 尝试使用 PID 文件停止
    if [ -f "$UWSGI_PID_FILE" ]; then
        local pid=$(cat "$UWSGI_PID_FILE")
        log_info "使用 PID 文件停止服务: $pid"

        if kill -TERM "$pid" 2>/dev/null; then
            log_info "发送 TERM 信号到进程 $pid"

            # 等待进程停止
            local count=0
            while [ $count -lt 10 ] && kill -0 "$pid" 2>/dev/null; do
                sleep 1
                count=$((count + 1))
                log_debug "等待进程停止... ($count/10)"
            done

            if kill -0 "$pid" 2>/dev/null; then
                log_warning "进程未正常停止，强制终止"
                kill -KILL "$pid" 2>/dev/null || true
            fi
        fi

        # 清理 PID 文件
        rm -f "$UWSGI_PID_FILE"
    fi

    # 强制停止所有相关进程
    uwsgi_pids=$(pgrep -f "uwsgi.*$PROJECT_ROOT" || true)
    if [ -n "$uwsgi_pids" ]; then
        log_info "强制停止剩余进程: $uwsgi_pids"
        echo "$uwsgi_pids" | xargs -r kill -KILL 2>/dev/null || true
    fi

    # 检查端口占用
    local port_usage=$(netstat -tlnp 2>/dev/null | grep ":500[0-9]" || true)
    if [ -n "$port_usage" ]; then
        log_info "端口使用情况:"
        echo "$port_usage" | while read line; do
            log_info "  $line"
        done
    fi

    log_success "Web Server 服务已停止"
    return 0
}

start_web_server() {
    log_step "启动 Web Server 服务"

    # 检查配置文件
    if ! check_file "$UWSGI_CONFIG"; then
        log_error "uWSGI 配置文件不存在: $UWSGI_CONFIG"
        return 1
    fi

    # 确保必要目录存在
    mkdir -p "$(dirname "$UWSGI_PID_FILE")"
    mkdir -p "$LOG_DIR"

    # 切换到项目目录
    cd "$PROJECT_ROOT"

    log_info "使用配置文件: $UWSGI_CONFIG"
    log_info "工作目录: $(pwd)"

    # 启动 uWSGI
    if uwsgi --ini "$UWSGI_CONFIG" --daemonize "$LOG_DIR/uwsgi.log" 2>&1 | tee -a "$DEPLOY_LOG"; then
        log_info "uWSGI 启动命令执行完成"
    else
        log_error "uWSGI 启动失败"
        return 1
    fi

    # 等待服务启动
    log_info "等待服务启动..."
    sleep 3

    # 检查进程是否启动
    local uwsgi_pids=$(pgrep -f "uwsgi.*$PROJECT_ROOT" || true)
    if [ -n "$uwsgi_pids" ]; then
        log_success "Web Server 进程已启动: $uwsgi_pids"
    else
        log_error "Web Server 进程未启动"

        # 显示错误日志
        if [ -f "$LOG_DIR/uwsgi.log" ]; then
            log_error "uWSGI 错误日志:"
            tail -20 "$LOG_DIR/uwsgi.log" | while read line; do
                log_error "  $line"
            done
        fi
        return 1
    fi

    return 0
}

verify_service_status() {
    log_step "验证服务状态"

    # 检查进程状态
    local uwsgi_pids=$(pgrep -f "uwsgi.*$PROJECT_ROOT" || true)
    if [ -z "$uwsgi_pids" ]; then
        log_error "Web Server 进程未运行"
        return 1
    fi

    log_info "运行中的进程:"
    echo "$uwsgi_pids" | while read pid; do
        local cmd=$(ps -p "$pid" -o cmd --no-headers 2>/dev/null || echo "进程不存在")
        log_info "  PID $pid: $cmd"
    done

    # 检查端口监听
    log_info "检查端口监听状态..."
    local listening_ports=$(netstat -tlnp 2>/dev/null | grep -E ":(500[0-9]|8000)" || true)

    if [ -n "$listening_ports" ]; then
        log_info "监听的端口:"
        echo "$listening_ports" | while read line; do
            log_info "  $line"
        done
    else
        log_warning "未发现监听的端口"
    fi

    # 检查日志文件
    if [ -f "$LOG_DIR/uwsgi.log" ]; then
        local log_size=$(stat -f%z "$LOG_DIR/uwsgi.log" 2>/dev/null || stat -c%s "$LOG_DIR/uwsgi.log" 2>/dev/null || echo "0")
        log_info "uWSGI 日志文件: $LOG_DIR/uwsgi.log (${log_size} 字节)"

        # 显示最近的日志
        log_info "最近的日志内容:"
        tail -10 "$LOG_DIR/uwsgi.log" | while read line; do
            log_info "  $line"
        done
    fi

    # 简单的健康检查
    log_info "执行应用健康检查..."
    if python3 -c "
import sys
sys.path.insert(0, '$PROJECT_ROOT')
try:
    from manage import app
    print('✅ 应用模块加载成功')
except Exception as e:
    print(f'❌ 应用模块加载失败: {e}')
    sys.exit(1)
" 2>&1 | tee -a "$DEPLOY_LOG"; then
        log_success "应用健康检查通过"
    else
        log_warning "应用健康检查失败"
    fi

    log_success "服务状态验证完成"
    return 0
}

# =============================================================================
# 清理和回滚函数
# =============================================================================

cleanup_on_failure() {
    log_step "执行失败清理"

    # 停止可能启动的服务
    stop_web_server || true

    # 清理临时文件
    rm -f /tmp/deploy_*.tmp 2>/dev/null || true

    log_info "清理完成"
}

# 错误处理
handle_error() {
    local exit_code=$?
    local line_number=$1

    log_error "脚本在第 $line_number 行发生错误 (退出码: $exit_code)"
    log_error "部署失败，执行清理操作..."

    cleanup_on_failure

    log_error "部署失败！请检查日志: $DEPLOY_LOG"
    exit $exit_code
}

# 设置错误处理
trap 'handle_error $LINENO' ERR

# =============================================================================
# 使用说明函数
# =============================================================================

show_usage() {
    cat << EOF
使用说明: $0 [选项]

Web Server 完整部署脚本

选项:
    -h, --help              显示此帮助信息
    -c, --check-only        仅执行系统检查，不进行安装
    -i, --install-only      仅执行离线安装，不重启服务
    -r, --restart-only      仅重启服务，不进行安装
    -v, --verify-only       仅验证安装，不进行其他操作
    -f, --force             强制执行，跳过确认提示
    --no-backup             不创建备份（默认会备份）

示例:
    $0                      # 完整部署（检查+安装+重启）
    $0 -c                   # 仅检查系统要求
    $0 -i                   # 仅安装离线包
    $0 -r                   # 仅重启服务
    $0 -f                   # 强制执行，无需确认

功能说明:
    1. 系统要求检查 (Python, pip, 磁盘空间等)
    2. 项目结构验证 (离线包, 配置文件等)
    3. 离线安装 Python 模块 (kaleido, plotly, scikit-learn, scipy, numpy)
    4. 安装验证 (导入测试, 功能测试)
    5. 服务重启 (停止旧服务, 启动新服务)
    6. 状态验证 (进程检查, 端口监听, 健康检查)

日志文件: $DEPLOY_LOG

EOF
}

# =============================================================================
# 主函数
# =============================================================================

main() {
    # 解析命令行参数
    local check_only=false
    local install_only=false
    local restart_only=false
    local verify_only=false
    local force_mode=false
    local no_backup=false

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_usage
                exit 0
                ;;
            -c|--check-only)
                check_only=true
                shift
                ;;
            -i|--install-only)
                install_only=true
                shift
                ;;
            -r|--restart-only)
                restart_only=true
                shift
                ;;
            -v|--verify-only)
                verify_only=true
                shift
                ;;
            -f|--force)
                force_mode=true
                shift
                ;;
            --no-backup)
                no_backup=true
                shift
                ;;
            *)
                log_error "未知选项: $1"
                show_usage
                exit 1
                ;;
        esac
    done

    # 显示欢迎信息
    print_header "Web Server 完整部署脚本"

    log_info "部署开始时间: $(date)"
    log_info "脚本版本: 1.0"
    log_info "目标架构: aarch64 (ARM64)"
    log_info "Python 支持: 3.7-3.10"
    log_info "日志文件: $DEPLOY_LOG"

    # 显示运行模式
    if [ "$check_only" = true ]; then
        log_info "运行模式: 仅检查系统"
    elif [ "$install_only" = true ]; then
        log_info "运行模式: 仅安装离线包"
    elif [ "$restart_only" = true ]; then
        log_info "运行模式: 仅重启服务"
    elif [ "$verify_only" = true ]; then
        log_info "运行模式: 仅验证安装"
    else
        log_info "运行模式: 完整部署"
    fi

    if [ "$force_mode" = true ]; then
        log_info "强制模式: 已启用"
    fi

    # 执行部署步骤
    local start_time=$(date +%s)

    # 1. 系统检查
    if ! check_system_requirements; then
        log_error "系统检查失败"
        exit 1
    fi

    if ! check_project_structure; then
        log_error "项目结构检查失败"
        exit 1
    fi

    if [ "$check_only" = true ]; then
        log_success "系统检查完成，所有要求满足"
        exit 0
    fi

    # 2. 离线安装
    if [ "$restart_only" = false ] && [ "$verify_only" = false ]; then
        if [ "$force_mode" = false ]; then
            wait_for_confirmation "确认开始离线安装？这将安装/更新 Python 模块。"
        fi

        if ! install_offline_packages; then
            log_error "离线安装失败"
            exit 1
        fi

        if ! verify_installation; then
            log_error "安装验证失败"
            exit 1
        fi
    fi

    if [ "$install_only" = true ]; then
        log_success "离线安装完成"
        exit 0
    fi

    if [ "$verify_only" = true ]; then
        if ! verify_installation; then
            log_error "安装验证失败"
            exit 1
        fi
        log_success "安装验证完成"
        exit 0
    fi

    # 3. 服务重启
    if [ "$force_mode" = false ]; then
        wait_for_confirmation "确认重启 Web Server 服务？这将中断当前服务。"
    fi

    if ! stop_web_server; then
        log_error "停止服务失败"
        exit 1
    fi

    if ! start_web_server; then
        log_error "启动服务失败"
        exit 1
    fi

    if ! verify_service_status; then
        log_error "服务状态验证失败"
        exit 1
    fi

    # 计算部署时间
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))

    # 部署完成
    print_header "部署完成"

    log_success "Web Server 部署成功完成！"
    log_info "部署耗时: ${duration} 秒"
    log_info "部署日志: $DEPLOY_LOG"

    echo ""
    log_info "🎉 部署摘要:"
    echo "  ✅ 系统检查通过"
    echo "  ✅ 离线包安装成功"
    echo "  ✅ 模块验证通过"
    echo "  ✅ 服务重启成功"
    echo "  ✅ 状态验证通过"

    echo ""
    log_info "🚀 Point Cloud 先进算法功能:"
    echo "  📊 高精度作物高度识别 (DBSCAN)"
    echo "  📈 高质量 3D 可视化 (Plotly)"
    echo "  🎬 360度旋转 GIF 动画"
    echo "  🖼️  高分辨率静态图像导出"

    echo ""
    log_info "📋 后续操作:"
    echo "  • 检查服务状态: ps aux | grep uwsgi"
    echo "  • 查看服务日志: tail -f $LOG_DIR/uwsgi.log"
    echo "  • 测试 API 接口: curl http://localhost:5001/api/v1.0/health"
    echo "  • 查看部署日志: cat $DEPLOY_LOG"

    return 0
}

# =============================================================================
# 脚本入口
# =============================================================================

# 检查是否以 root 用户运行
if [ "$EUID" -eq 0 ]; then
    log_warning "不建议以 root 用户运行此脚本"
    read -p "确认继续？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "部署已取消"
        exit 0
    fi
fi

# 执行主函数
main "$@"
