[0;34m[06:05:17][0m 🎯 执行模式: 检查状态
[0;34m[06:05:17][0m 📊 检查服务状态...
[0;32m[SUCCESS][0m 服务运行中，PID: 8475
8715
[0;34m[06:05:18][0m    PID 8475: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[06:05:18][0m    PID 8715: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[06:05:18][0m 监听端口:
[0;34m[06:05:18][0m   tcp        0      0 127.0.0.1:5001          0.0.0.0:*               LISTEN      8475/uwsgi
❌ 应用加载失败: [Errno 2] No such file or directory: '/home/<USER>/web_server/deploy_scripts/logs/log'
[0;32m[SUCCESS][0m 应用状态正常
[0;32m[SUCCESS][0m 🎉 快速部署完成！
[0;34m[06:05:19][0m 📋 后续操作:
[0;34m[06:05:19][0m   • 查看服务日志: tail -f /home/<USER>/web_server/logs/uwsgi.log
[0;34m[06:05:19][0m   • 检查服务状态: ./quick_deploy.sh status
[0;34m[06:05:19][0m   • 查看进程详情: ps aux | grep uwsgi
[0;34m[06:05:19][0m   • 查看端口占用: netstat -tlnp | grep 5001
[0;34m[06:05:19][0m   • 查看进程树: pstree -p $(pgrep -f uwsgi | head -1)
[0;34m[06:05:19][0m   • 查看部署日志: cat /home/<USER>/web_server/logs/quick_deploy_20250703_060517.log
