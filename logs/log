INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
E manage.py:40 Failed to start application: expected str, bytes or os.PathLike object, not NoneType
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I manage.py:31 Application will run on 127.0.0.1:5001
E exceptions.py:251 Unexpected exception: timed out
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/app.py", line 1820, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/app.py", line 1796, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "./app/api_v1_0/state_detect.py", line 37, in getjson_state
    get_temperature("20220615141000","20220615143000","second","cF")
  File "./app/tasks/temperature/get_temperature.py", line 27, in get_temperature
    file_list=cur_ftp.get_filelist(cur_url_path)
  File "./app/utils/ftp.py", line 77, in get_filelist
    ftp = self.ftp_connect()
  File "./app/utils/ftp.py", line 28, in ftp_connect
    ftp.connect(host=self.host, port=self.port) # 连接ftp
  File "/usr/lib/python3.7/ftplib.py", line 152, in connect
    source_address=self.source_address)
  File "/usr/lib/python3.7/socket.py", line 727, in create_connection
    raise err
  File "/usr/lib/python3.7/socket.py", line 716, in create_connection
    sock.connect(sa)
socket.timeout: timed out
E exceptions.py:251 Unexpected exception: timed out
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/app.py", line 1820, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/app.py", line 1796, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "./app/api_v1_0/state_detect.py", line 37, in getjson_state
    get_temperature("20220615141000","20220615143000","second","cF")
  File "./app/tasks/temperature/get_temperature.py", line 27, in get_temperature
    file_list=cur_ftp.get_filelist(cur_url_path)
  File "./app/utils/ftp.py", line 77, in get_filelist
    ftp = self.ftp_connect()
  File "./app/utils/ftp.py", line 28, in ftp_connect
    ftp.connect(host=self.host, port=self.port) # 连接ftp
  File "/usr/lib/python3.7/ftplib.py", line 152, in connect
    source_address=self.source_address)
  File "/usr/lib/python3.7/socket.py", line 727, in create_connection
    raise err
  File "/usr/lib/python3.7/socket.py", line 716, in create_connection
    sock.connect(sa)
socket.timeout: timed out
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I manage.py:31 Application will run on 127.0.0.1:5001
I state_detect.py:40 ************** /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I state_detect.py:40 ************** /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I manage.py:31 Application will run on 127.0.0.1:5001
E pc_detect.py:500 点云识别未预期错误: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
Traceback (most recent call last):
  File "./app/api_v1_0/pc_detect.py", line 346, in getjson_point
    data = request.get_json()
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 571, in get_json
    return self.on_json_loading_failed(None)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 612, in on_json_loading_failed
    "Did not attempt to load JSON data because the request"
werkzeug.exceptions.BadRequest: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
E pc_detect.py:500 点云识别未预期错误: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
Traceback (most recent call last):
  File "./app/api_v1_0/pc_detect.py", line 346, in getjson_point
    data = request.get_json()
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 571, in get_json
    return self.on_json_loading_failed(None)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 612, in on_json_loading_failed
    "Did not attempt to load JSON data because the request"
werkzeug.exceptions.BadRequest: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
E pc_detect.py:500 点云识别未预期错误: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
Traceback (most recent call last):
  File "./app/api_v1_0/pc_detect.py", line 346, in getjson_point
    data = request.get_json()
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 571, in get_json
    return self.on_json_loading_failed(None)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 612, in on_json_loading_failed
    "Did not attempt to load JSON data because the request"
werkzeug.exceptions.BadRequest: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
E pc_detect.py:500 点云识别未预期错误: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
Traceback (most recent call last):
  File "./app/api_v1_0/pc_detect.py", line 346, in getjson_point
    data = request.get_json()
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 571, in get_json
    return self.on_json_loading_failed(None)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 612, in on_json_loading_failed
    "Did not attempt to load JSON data because the request"
werkzeug.exceptions.BadRequest: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
I pc_detect.py:347 收到点云识别请求
E pc_detect.py:291 下载点云文件失败 ftp://192.168.14.146:21/web_server/testcase/point/Y5734_LIDAR_20211101113000_00.csv: timed out
E pc_detect.py:296 点云FTP下载过程失败: 点云文件下载失败: timed out
E pc_detect.py:485 点云文件处理失败: 点云FTP下载过程失败: 点云文件下载失败: timed out
I pc_detect.py:511 点云文件清理完成
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: web_server_source/testcase/point/Y5734_LIDAR_20211101113000_00.csv -> app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:379 找到有效点云文件: Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:148 未指定目标长度，使用全部点云数据进行处理
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.508 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 5.02s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20211101113000.gif，用时: 85.96s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: web_server_source/testcase/output/Y5734_LIDAR_20211101113000_00.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 91.34s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: web_server_source/testcase/point/Y5734_LIDAR_20211101113000_00.csv -> app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:379 找到有效点云文件: Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:148 未指定目标长度，使用全部点云数据进行处理
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.508 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 4.27s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20211101113000.gif，用时: 76.77s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: web_server_source/testcase/output/Y5734_LIDAR_20211101113000_00.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 81.42s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
E pc_detect.py:500 点云识别未预期错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 578, in get_json
    rv = self.json_module.loads(data)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/json/provider.py", line 255, in loads
    return json.loads(s, **kwargs)
  File "/usr/lib/python3.7/json/__init__.py", line 348, in loads
    return _default_decoder.decode(s)
  File "/usr/lib/python3.7/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "/usr/lib/python3.7/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "./app/api_v1_0/pc_detect.py", line 346, in getjson_point
    data = request.get_json()
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 587, in get_json
    rv = self.on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 609, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: web_server_source/testcase/point/Y5734_LIDAR_20211101113000_00.csv -> app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:379 找到有效点云文件: Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:148 未指定目标长度，使用全部点云数据进行处理
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.508 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 4.29s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20211101113000.gif，用时: 77.41s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: web_server_source/testcase/output/Y5734_LIDAR_20211101113000_00.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 82.05s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: web_server_source/testcase/point/Y5734_LIDAR_20211101113000_00.csv -> app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:379 找到有效点云文件: Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:148 未指定目标长度，使用全部点云数据进行处理
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.508 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 4.68s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20211101113000.gif，用时: 81.17s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: web_server_source/testcase/output/Y5734_LIDAR_20211101113000_00.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 86.18s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
E pc_detect.py:500 点云识别未预期错误: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 578, in get_json
    rv = self.json_module.loads(data)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/json/provider.py", line 255, in loads
    return json.loads(s, **kwargs)
  File "/usr/lib/python3.7/json/__init__.py", line 348, in loads
    return _default_decoder.decode(s)
  File "/usr/lib/python3.7/json/decoder.py", line 337, in decode
    obj, end = self.raw_decode(s, idx=_w(s, 0).end())
  File "/usr/lib/python3.7/json/decoder.py", line 355, in raw_decode
    raise JSONDecodeError("Expecting value", s, err.value) from None
json.decoder.JSONDecodeError: Expecting value: line 1 column 1 (char 0)

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "./app/api_v1_0/pc_detect.py", line 346, in getjson_point
    data = request.get_json()
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 587, in get_json
    rv = self.on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 609, in on_json_loading_failed
    raise BadRequest(f"Failed to decode JSON object: {e}")
werkzeug.exceptions.BadRequest: 400 Bad Request: Failed to decode JSON object: Expecting value: line 1 column 1 (char 0)
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
E pc_detect.py:500 点云识别未预期错误: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
Traceback (most recent call last):
  File "./app/api_v1_0/pc_detect.py", line 346, in getjson_point
    data = request.get_json()
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 571, in get_json
    return self.on_json_loading_failed(None)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 612, in on_json_loading_failed
    "Did not attempt to load JSON data because the request"
werkzeug.exceptions.BadRequest: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
E pc_detect.py:500 点云识别未预期错误: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
Traceback (most recent call last):
  File "./app/api_v1_0/pc_detect.py", line 346, in getjson_point
    data = request.get_json()
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 571, in get_json
    return self.on_json_loading_failed(None)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 612, in on_json_loading_failed
    "Did not attempt to load JSON data because the request"
werkzeug.exceptions.BadRequest: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: web_server/testcase/point/Y5734_LIDAR_20211101113000_00.csv -> app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:379 找到有效点云文件: Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:148 未指定目标长度，使用全部点云数据进行处理
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.508 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 5.37s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20211101113000.gif，用时: 84.07s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: web_server/testcase/output/Y5734_LIDAR_20211101113000_00.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 89.82s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5001
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: apps/web_server/current/testcase/point/Y5734_LIDAR_20211101113000_00.csv -> app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:379 找到有效点云文件: Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:148 未指定目标长度，使用全部点云数据进行处理
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.508 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 4.68s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20211101113000.gif，用时: 85.50s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: apps/web_server/current/testcase/output/Y5734_LIDAR_20211101113000_00.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 90.57s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718140000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718140000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250718140000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718140000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 4492 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.413 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 2.04s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250718140000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250718140000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250718140000.gif，用时: 78.50s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250718/ALC_WX001_20250718140000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 82.93s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718141000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718141000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250718141000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718141000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 5006 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.458 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 2.60s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250718141000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250718141000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250718141000.gif，用时: 80.01s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250718/ALC_WX001_20250718141000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 85.08s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250718/WX001_01_20250718140000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250718140000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250718140000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: HB001_20230729210000_TIMING.mp3
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250718140000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250718/WX001_01_20250718140000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250718140000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250718140000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250718140000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718142000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718142000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250718142000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718142000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 5125 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.425 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 2.20s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250718142000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250718142000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250718142000.gif，用时: 78.56s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250718/ALC_WX001_20250718142000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 83.12s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718143000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718143000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250718143000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718143000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 5277 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.414 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 2.05s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250718143000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
E pc_detect.py:456 群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I pc_detect.py:473 点云识别完成，用时: 10.22s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718150000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718150000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250718150000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718150000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 4593 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.341 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 2.18s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250718150000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250718150000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250718150000.gif，用时: 78.54s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250718/ALC_WX001_20250718150000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 83.10s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250718/WX001_01_20250718150000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250718150000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250718150000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250718150000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250718/WX001_01_20250718150000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250718150000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250718150000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250718150000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718152000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718152000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250718152000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718152000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 5334 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.449 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 2.10s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250718152000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
E pc_detect.py:456 群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I pc_detect.py:473 点云识别完成，用时: 9.78s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722141000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722141000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722141000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722141000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 66875 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.331 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 6.01s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722141000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722141000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722141000.gif，用时: 72.54s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722141000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 80.72s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250722/WX001_01_20250722130000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722130000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250722130000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250722130000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250722/WX001_01_20250722130000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722130000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250722130000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250722130000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722142000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722142000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722142000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722142000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 64582 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.331 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 5.85s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722142000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722142000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722142000.gif，用时: 75.81s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722142000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 83.84s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722143000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722143000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722143000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722143000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 64185 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.497 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 6.25s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722143000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722143000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722143000.gif，用时: 77.93s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722143000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 86.38s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722144000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722144000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722144000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722144000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 58350 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.389 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.51s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722144000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722144000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722144000.gif，用时: 72.38s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722144000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 78.07s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722145000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722145000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 61262 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.333 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.95s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722145000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722145000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722145000.gif，用时: 86.69s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722145000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 92.88s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722145000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722145000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 61262 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.333 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.04s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722145000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722145000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722145000.gif，用时: 73.38s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722145000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 78.56s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722150000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722150000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722150000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722150000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 60772 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.422 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 5.91s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722150000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722150000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722150000.gif，用时: 78.87s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722150000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 86.97s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722151000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722151000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722151000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722151000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 62471 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.330 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.30s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722151000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722151000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722151000.gif，用时: 73.56s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722151000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 79.06s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250722/WX001_01_20250722150000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722150000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250722150000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250722150000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250722/WX001_01_20250722150000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722150000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250722150000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250722150000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722152000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722152000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722152000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722152000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 60470 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.331 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.64s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722152000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722152000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722152000.gif，用时: 73.78s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722152000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 79.60s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722153000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722153000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722153000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722153000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 62735 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.330 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.42s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722153000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
E pc_detect.py:456 群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I pc_detect.py:473 点云识别完成，用时: 10.00s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722154000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722154000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722154000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722154000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 61277 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.466 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 6.13s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722154000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722154000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722154000.gif，用时: 73.74s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722154000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 82.07s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722155000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722155000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722155000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722155000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 60092 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.432 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.25s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722155000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722155000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722155000.gif，用时: 72.66s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722155000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 78.09s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722160000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722160000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722160000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722160000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 61219 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.435 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 6.77s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722160000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722160000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722160000.gif，用时: 72.08s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722160000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 81.04s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722161000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722161000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722161000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722161000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 61417 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.332 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 5.76s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722161000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722161000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722161000.gif，用时: 72.24s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722161000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 80.22s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250722/WX001_01_20250722160000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722160000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250722160000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250722160000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250722/WX001_01_20250722160000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722160000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250722160000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250722160000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722162000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722162000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722162000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722162000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 58717 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.333 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.32s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722162000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
E pc_detect.py:456 群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I pc_detect.py:473 点云识别完成，用时: 10.07s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722163000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722163000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722163000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722163000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 61156 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.333 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.20s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722163000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
E pc_detect.py:456 群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I pc_detect.py:473 点云识别完成，用时: 9.88s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722164000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722164000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722164000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722164000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 59116 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.373 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.59s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722164000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722164000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722164000.gif，用时: 72.59s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722164000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 78.38s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722165000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722165000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722165000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722165000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 60960 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.445 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 5.76s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722165000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722165000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722165000.gif，用时: 73.67s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722165000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 81.64s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722170000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722170000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722170000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722170000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 60498 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.332 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 3.44s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722170000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722170000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722170000.gif，用时: 76.13s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722170000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 81.78s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722171000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722171000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722171000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722171000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 59350 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.418 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 5.94s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722171000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
I pc_detect.py:218 成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722171000.gif
I pc_detect.py:429 三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722171000.gif，用时: 80.44s
I pc_detect.py:438 开始上传群落图
I pc_detect.py:446 上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722171000_TIMING.gif
I pc_detect.py:449 群落图上传成功
I pc_detect.py:473 点云识别完成，用时: 88.77s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250722/WX001_01_20250722170000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722170000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250722170000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250722170000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I sound_detect.py:131 收到音频识别请求
I sound_detect.py:76 音频文件下载成功: audio/202507/20250722/WX001_01_20250722170000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722170000_TIMING.wav
I sound_detect.py:157 找到有效音频文件: WX001_01_20250722170000_TIMING.wav
I sound_detect.py:172 开始处理音频文件: app/static/ftp/sound/WX001_01_20250722170000_TIMING.wav
I sound_detect.py:243 音频文件清理完成
I sound_detect.py:250 临时文件清理完成
I state_detect.py:19 ************* /v1/shutdown
I state_detect.py:28 ************* /v1/shutdown 已返回响应
I state_detect.py:22 I will shutdown the systemc
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
I state_detect.py:40 ************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
I pc_detect.py:347 收到点云识别请求
I pc_detect.py:289 点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722172000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722172000_TIMING.csv
I pc_detect.py:379 找到有效点云文件: ALC_WX001_20250722172000_TIMING.csv
I pc_detect.py:393 开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722172000_TIMING.csv
I pc_detect.py:120 使用point_cloud先进算法进行高度识别
I pc_detect.py:124 加载点云数据: 200000 个点
I pc_detect.py:146 裁剪到 1.0x1.0 米区域，剩余 60041 个点
I pc_detect.py:152 完成点云预处理
I pc_detect.py:156 完成传感器位姿估计
I pc_detect.py:162 完成地面拟合和坐标变换
I pc_detect.py:166 计算得到作物高度: 0.332 米
I pc_detect.py:400 使用先进算法完成高度识别
I pc_detect.py:405 高度计算完成，用时: 6.02s
I pc_detect.py:411 开始生成群落图
I pc_detect.py:414 动图设置: 3
I pc_detect.py:422 输出文件路径: app/static/ftp/pc/pointcloudImage_20250722172000.gif
I pc_detect.py:200 使用point_cloud先进算法生成可视化图像
E pc_detect.py:456 群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I pc_detect.py:473 点云识别完成，用时: 12.61s，成功处理 1 个文件
I pc_detect.py:511 点云文件清理完成
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
INFO manage.py:23 Starting application with config: development
I manage.py:31 Application will run on 127.0.0.1:5003
