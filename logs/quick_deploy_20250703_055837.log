[0;34m[05:58:37][0m 🎯 执行模式: 仅重启服务
[0;34m[05:58:37][0m 🔄 开始快速重启服务...
[0;34m[05:58:37][0m ⏹️  停止现有服务...
[0;34m[05:58:37][0m 🔍 识别运行中的 uWSGI 进程...
[0;34m[05:58:37][0m 📋 发现以下 uWSGI 进程需要停止:  7112 7350
[0;34m[05:58:37][0m    PID 7112: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[05:58:37][0m    PID 7350: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[05:58:37][0m 🛑 第一阶段：优雅停止进程 (SIGTERM)...
[0;34m[05:58:37][0m    发送 SIGTERM 到进程 7112
[0;34m[05:58:37][0m    发送 SIGTERM 到进程 7350
[0;34m[05:58:38][0m ⏳ 等待进程优雅退出 (5秒)...
[0;34m[05:58:43][0m ⚠️  以下进程仍在运行:  7112 7350
[0;34m[05:58:43][0m 🔍 检查端口 5001 占用情况...
[0;34m[05:58:43][0m ⚠️  端口 5001 仍被以下进程占用: 7112
[0;34m[05:58:43][0m    发送 SIGTERM 到端口占用进程 7112
[0;34m[05:58:46][0m 🔍 检查残留的 uWSGI 进程...
[0;34m[05:58:46][0m 💀 第二阶段：强制终止残留进程 (SIGKILL)...
[0;34m[05:58:46][0m    发送 SIGKILL 到进程 7112
[0;34m[05:58:48][0m 🔍 最终验证清理结果...
[0;32m[SUCCESS][0m ✅ 所有 uWSGI 进程已停止，端口 5001 已完全释放
[0;34m[05:58:49][0m ▶️  启动新服务...
[0;34m[05:58:49][0m 🚀 执行启动命令: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[uWSGI] getting INI configuration from uwsgi.ini
[0;34m[05:58:49][0m ✅ 启动命令执行完成
[0;34m[05:58:49][0m ⏳ 等待服务启动 (最多15秒)...
[0;34m[05:58:50][0m ✅ 第 1 秒: 发现 uWSGI 进程 (PID: 7677) 并且端口 5001 已监听
[0;34m[05:58:50][0m 🔍 最终状态检查...
[0;32m[SUCCESS][0m 🎉 服务启动成功！
[0;34m[05:58:51][0m    进程ID: 7677
[0;34m[05:58:51][0m    监听端口: 127.0.0.1:5001
[0;34m[05:58:51][0m    配置文件: /home/<USER>/web_server/uwsgi.ini
[0;34m[05:58:51][0m    日志文件: /home/<USER>/web_server/logs/uwsgi.log
[0;34m[05:58:51][0m 📋 最新日志 (最后5行):
[0;34m[05:58:51][0m    your server socket listen backlog is limited to 128 connections
[0;34m[05:58:51][0m    your mercy for graceful operations on workers is 60 seconds
[0;34m[05:58:51][0m    mapped 145840 bytes (142 KB) for 1 cores
[0;34m[05:58:51][0m    *** Operational MODE: single process ***
[0;34m[05:58:51][0m    INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
[0;34m[05:58:51][0m 📊 检查服务状态...
[0;32m[SUCCESS][0m 服务运行中，PID: 7677
[0;34m[05:58:51][0m    PID 7677: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[05:58:51][0m 监听端口:
[0;34m[05:58:51][0m   tcp        0      0 127.0.0.1:5001          0.0.0.0:*               LISTEN      7677/uwsgi
INFO:manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
I:manage:Application will run on 127.0.0.1:5001
✅ 应用加载正常
Rga built version:7bf9abf 
[0;32m[SUCCESS][0m 应用状态正常
[0;32m[SUCCESS][0m 🎉 快速部署完成！
[0;34m[05:59:08][0m 📋 后续操作:
[0;34m[05:59:08][0m   • 查看服务日志: tail -f /home/<USER>/web_server/logs/uwsgi.log
[0;34m[05:59:08][0m   • 检查服务状态: ./quick_deploy.sh status
[0;34m[05:59:08][0m   • 查看部署日志: cat /home/<USER>/web_server/logs/quick_deploy_20250703_055837.log
