[0;34m[05:41:26][0m 🎯 执行模式: 完整快速部署
[0;34m[05:41:26][0m 🚀 开始快速安装离线模块...
[0;34m[05:41:26][0m 📦 安装核心模块: kaleido, plotly, scikit-learn, scipy, numpy
[0;32m[SUCCESS][0m 模块安装完成
[0;34m[05:41:31][0m 🔍 验证安装...
✅ 所有模块导入成功
[0;32m[SUCCESS][0m 模块验证通过
[0;34m[05:41:34][0m 🔄 开始快速重启服务...
[0;34m[05:41:34][0m ⏹️  停止现有服务...
[0;34m[05:41:36][0m ▶️  启动新服务...
[uWSGI] getting INI configuration from uwsgi.ini
[0;32m[SUCCESS][0m 服务启动命令执行完成
[0;31m[ERROR][0m 服务启动失败
