[0;34m[05:54:35][0m 🎯 执行模式: 完整快速部署
[0;34m[05:54:35][0m 🚀 开始快速安装离线模块...
[0;34m[05:54:35][0m 📦 安装核心模块: kaleido, plotly, scikit-learn, scipy, numpy
[0;32m[SUCCESS][0m 模块安装完成
[0;34m[05:54:40][0m 🔍 验证安装...
✅ 所有模块导入成功
[0;32m[SUCCESS][0m 模块验证通过
[0;34m[05:54:43][0m 🔄 开始快速重启服务...
[0;34m[05:54:43][0m ⏹️  停止现有服务...
[0;34m[05:54:43][0m 🔍 识别运行中的 uWSGI 进程...
[0;34m[05:54:43][0m 📋 发现以下 uWSGI 进程需要停止:  6499 6730
[0;34m[05:54:43][0m    PID 6499: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[05:54:43][0m    PID 6730: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[05:54:43][0m 🛑 第一阶段：优雅停止进程 (SIGTERM)...
[0;34m[05:54:43][0m    发送 SIGTERM 到进程 6499
[0;34m[05:54:43][0m    发送 SIGTERM 到进程 6730
[0;34m[05:54:43][0m ⏳ 等待进程优雅退出 (5秒)...
[0;34m[05:54:48][0m    ✅ 进程 6730 已优雅退出
[0;34m[05:54:48][0m ⚠️  以下进程仍在运行:  6499
[0;34m[05:54:48][0m 🔍 检查端口 5001 占用情况...
[0;34m[05:54:49][0m ⚠️  端口 5001 仍被以下进程占用: 6499
[0;34m[05:54:49][0m    发送 SIGTERM 到端口占用进程 6499
[0;34m[05:54:52][0m 🔍 检查残留的 uWSGI 进程...
[0;34m[05:54:52][0m 🔍 最终验证清理结果...
[0;32m[SUCCESS][0m ✅ 所有 uWSGI 进程已停止，端口 5001 已完全释放
[0;34m[05:54:53][0m ▶️  启动新服务...
[0;34m[05:54:53][0m 🚀 执行启动命令: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[uWSGI] getting INI configuration from uwsgi.ini
[0;34m[05:54:53][0m ✅ 启动命令执行完成
[0;34m[05:54:53][0m ⏳ 等待服务启动 (最多15秒)...
[0;34m[05:54:54][0m ✅ 第 1 秒: 发现 uWSGI 进程 (PID: 7112) 并且端口 5001 已监听
[0;34m[05:54:54][0m 🔍 最终状态检查...
[0;32m[SUCCESS][0m 🎉 服务启动成功！
[0;34m[05:54:55][0m    进程ID: 7112
[0;34m[05:54:55][0m    监听端口: 127.0.0.1:5001
[0;34m[05:54:55][0m    配置文件: /home/<USER>/web_server/uwsgi.ini
[0;34m[05:54:55][0m    日志文件: /home/<USER>/web_server/logs/uwsgi.log
[0;34m[05:54:55][0m 📋 最新日志 (最后5行):
[0;34m[05:54:55][0m    your server socket listen backlog is limited to 128 connections
[0;34m[05:54:55][0m    your mercy for graceful operations on workers is 60 seconds
[0;34m[05:54:55][0m    mapped 145840 bytes (142 KB) for 1 cores
[0;34m[05:54:55][0m    *** Operational MODE: single process ***
[0;34m[05:54:55][0m    INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
[0;34m[05:54:55][0m 📊 检查服务状态...
[0;32m[SUCCESS][0m 服务运行中，PID: 7112
[0;34m[05:54:55][0m    PID 7112: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[05:54:55][0m 监听端口:
[0;34m[05:54:55][0m   tcp        0      0 127.0.0.1:5001          0.0.0.0:*               LISTEN      7112/uwsgi
INFO:manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
I:manage:Application will run on 127.0.0.1:5001
玉米密度模块加载完成！
棉花密度算法模块加载完成！
水稻密度算法模块加载完成！
✓ 成功初始化point_cloud先进模块
  - 高度识别使用先进的DBSCAN聚类算法
  - 可视化使用Plotly生成高质量图像和GIF
  - 系统已移除老的算法，只使用最新的先进算法
✅ 应用加载正常
Rga built version:7bf9abf 
[0;32m[SUCCESS][0m 应用状态正常
[0;32m[SUCCESS][0m 🎉 快速部署完成！
[0;34m[05:55:13][0m 📋 后续操作:
[0;34m[05:55:13][0m   • 查看服务日志: tail -f /home/<USER>/web_server/logs/uwsgi.log
[0;34m[05:55:13][0m   • 检查服务状态: ./quick_deploy.sh status
[0;34m[05:55:13][0m   • 查看部署日志: cat /home/<USER>/web_server/logs/quick_deploy_20250703_055435.log
