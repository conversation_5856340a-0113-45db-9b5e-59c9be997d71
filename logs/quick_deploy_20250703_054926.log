[0;34m[05:49:26][0m 🎯 执行模式: 完整快速部署
[0;34m[05:49:26][0m 🚀 开始快速安装离线模块...
[0;34m[05:49:26][0m 📦 安装核心模块: kaleido, plotly, scikit-learn, scipy, numpy
[0;32m[SUCCESS][0m 模块安装完成
[0;34m[05:49:31][0m 🔍 验证安装...
✅ 所有模块导入成功
[0;32m[SUCCESS][0m 模块验证通过
[0;34m[05:49:34][0m 🔄 开始快速重启服务...
[0;34m[05:49:34][0m ⏹️  停止现有服务...
[0;34m[05:49:34][0m 🔍 识别运行中的 uWSGI 进程...
[0;34m[05:49:34][0m 📋 发现以下 uWSGI 进程需要停止:  541 543 733 734 735 736 737
[0;34m[05:49:34][0m    PID 541: /bin/sh -e /home/<USER>/opt/uwsgi/boot.sh
[0;34m[05:49:35][0m    PID 543: /home/<USER>/.local/bin/uwsgi /home/<USER>/opt/uwsgi/uwsgi.ini
[0;34m[05:49:35][0m    PID 733: /home/<USER>/.local/bin/uwsgi /home/<USER>/opt/uwsgi/uwsgi.ini
[0;34m[05:49:35][0m    PID 734: /home/<USER>/.local/bin/uwsgi /home/<USER>/opt/uwsgi/uwsgi.ini
[0;34m[05:49:35][0m    PID 735: /home/<USER>/.local/bin/uwsgi /home/<USER>/opt/uwsgi/uwsgi.ini
[0;34m[05:49:35][0m    PID 736: /home/<USER>/.local/bin/uwsgi /home/<USER>/opt/uwsgi/uwsgi.ini
[0;34m[05:49:35][0m    PID 737: /home/<USER>/.local/bin/uwsgi /home/<USER>/opt/uwsgi/uwsgi.ini
[0;34m[05:49:35][0m 🛑 第一阶段：优雅停止进程 (SIGTERM)...
[0;34m[05:49:35][0m    发送 SIGTERM 到进程 541
[0;34m[05:49:35][0m    发送 SIGTERM 到进程 543
[0;34m[05:49:35][0m    发送 SIGTERM 到进程 733
[0;34m[05:49:35][0m    发送 SIGTERM 到进程 734
[0;34m[05:49:35][0m    发送 SIGTERM 到进程 735
[0;34m[05:49:35][0m    发送 SIGTERM 到进程 736
[0;34m[05:49:35][0m    发送 SIGTERM 到进程 737
[0;34m[05:49:35][0m ⏳ 等待进程优雅退出 (5秒)...
[0;34m[05:49:40][0m    ✅ 进程 541 已优雅退出
[0;34m[05:49:40][0m    ✅ 进程 733 已优雅退出
[0;34m[05:49:40][0m    ✅ 进程 734 已优雅退出
[0;34m[05:49:40][0m    ✅ 进程 735 已优雅退出
[0;34m[05:49:40][0m    ✅ 进程 736 已优雅退出
[0;34m[05:49:40][0m    ✅ 进程 737 已优雅退出
[0;34m[05:49:40][0m ⚠️  以下进程仍在运行:  543
[0;34m[05:49:40][0m 🔍 检查端口 5001 占用情况...
[0;34m[05:49:41][0m ⚠️  端口 5001 仍被以下进程占用: 543
[0;34m[05:49:41][0m    发送 SIGTERM 到端口占用进程 543
[0;34m[05:49:44][0m 🔍 检查残留的 uWSGI 进程...
[0;34m[05:49:44][0m 💀 第二阶段：强制终止残留进程 (SIGKILL)...
[0;34m[05:49:44][0m    发送 SIGKILL 到进程 543
[0;34m[05:49:46][0m 🔍 最终验证清理结果...
[0;32m[SUCCESS][0m ✅ 所有 uWSGI 进程已停止，端口 5001 已完全释放
[0;34m[05:49:47][0m ▶️  启动新服务...
[0;34m[05:49:47][0m 🚀 执行启动命令: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[uWSGI] getting INI configuration from uwsgi.ini
[0;34m[05:49:47][0m ✅ 启动命令执行完成
[0;34m[05:49:47][0m ⏳ 等待服务启动 (最多15秒)...
[0;34m[05:49:48][0m ⏳ 第 1 秒: 等待 uWSGI 进程启动...
[0;34m[05:49:50][0m ⏳ 第 2 秒: 等待 uWSGI 进程启动...
[0;34m[05:49:51][0m ⏳ 第 3 秒: 等待 uWSGI 进程启动...
[0;34m[05:49:52][0m ⏳ 第 4 秒: 等待 uWSGI 进程启动...
[0;34m[05:49:54][0m ⏳ 第 5 秒: 等待 uWSGI 进程启动...
[0;34m[05:49:55][0m ⏳ 第 6 秒: 等待 uWSGI 进程启动...
[0;34m[05:49:56][0m ⏳ 第 7 秒: 等待 uWSGI 进程启动...
[0;34m[05:49:58][0m ⏳ 第 8 秒: 等待 uWSGI 进程启动...
[0;34m[05:49:59][0m ⏳ 第 9 秒: 等待 uWSGI 进程启动...
[0;34m[05:50:01][0m ⏳ 第 10 秒: 等待 uWSGI 进程启动...
[0;34m[05:50:02][0m ⏳ 第 11 秒: 等待 uWSGI 进程启动...
[0;34m[05:50:04][0m ⏳ 第 12 秒: 等待 uWSGI 进程启动...
[0;34m[05:50:05][0m ⏳ 第 13 秒: 等待 uWSGI 进程启动...
[0;34m[05:50:07][0m ⏳ 第 14 秒: 等待 uWSGI 进程启动...
[0;34m[05:50:08][0m ⏳ 第 15 秒: 等待 uWSGI 进程启动...
[0;34m[05:50:08][0m 🔍 最终状态检查...
[0;31m[ERROR][0m ❌ 服务启动失败！
[0;31m[ERROR][0m    原因: 未发现 uWSGI 进程
[0;31m[ERROR][0m 📋 错误日志 (最后10行):
[0;31m[ERROR][0m    ✓ 成功初始化point_cloud先进模块
[0;31m[ERROR][0m    - 高度识别使用先进的DBSCAN聚类算法
[0;31m[ERROR][0m    - 可视化使用Plotly生成高质量图像和GIF
[0;31m[ERROR][0m    - 系统已移除老的算法，只使用最新的先进算法
[0;31m[ERROR][0m    I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
[0;31m[ERROR][0m    WSGI app 0 (mountpoint='') ready in 16 seconds on interpreter 0x556acb5f10 pid: 5729 (default app)
[0;31m[ERROR][0m    *** uWSGI is running in multiple interpreter mode ***
[0;31m[ERROR][0m    spawned uWSGI master process (pid: 5729)
[0;31m[ERROR][0m    spawned uWSGI worker 1 (pid: 5979, cores: 1)
[0;31m[ERROR][0m    *** Stats server enabled on /home/<USER>/web_server/uwsgi/uwsgi.status fd: 10 ***
