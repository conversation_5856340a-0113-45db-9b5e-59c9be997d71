[0;34m[06:16:48][0m 🎯 执行模式: 完整快速部署
[0;34m[06:16:48][0m 🚀 开始快速安装离线模块...
[0;34m[06:16:48][0m 📦 安装核心模块: kaleido, plotly, scikit-learn, scipy, numpy
[0;32m[SUCCESS][0m 模块安装完成
[0;34m[06:16:54][0m 🔍 验证安装...
✅ 所有模块导入成功
[0;32m[SUCCESS][0m 模块验证通过
[0;34m[06:16:57][0m 🔄 开始快速重启服务...
[0;34m[06:16:57][0m ⏹️  停止现有服务...
[0;34m[06:16:57][0m 🔍 识别运行中的 uWSGI 进程...
[0;34m[06:16:57][0m 📋 发现以下 uWSGI 进程需要停止:  8475 8715
[0;34m[06:16:57][0m    PID 8475: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[06:16:57][0m    PID 8715: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[06:16:57][0m 🛑 第一阶段：优雅停止进程 (SIGTERM)...
[0;34m[06:16:57][0m    发送 SIGTERM 到进程 8475
[0;34m[06:16:57][0m    发送 SIGTERM 到进程 8715
[0;34m[06:16:57][0m ⏳ 等待进程优雅退出 (5秒)...
[0;34m[06:17:02][0m    ✅ 进程 8715 已优雅退出
[0;34m[06:17:02][0m ⚠️  以下进程仍在运行:  8475
[0;34m[06:17:02][0m 🔍 检查端口 5001 占用情况...
[0;34m[06:17:03][0m ⚠️  端口 5001 仍被以下进程占用: 8475
[0;34m[06:17:03][0m    发送 SIGTERM 到端口占用进程 8475
[0;34m[06:17:06][0m 🔍 检查残留的 uWSGI 进程...
[0;34m[06:17:06][0m 💀 第二阶段：强制终止残留进程 (SIGKILL)...
[0;34m[06:17:06][0m    发送 SIGKILL 到进程 8475
[0;34m[06:17:08][0m 🔍 最终验证清理结果...
[0;32m[SUCCESS][0m ✅ 所有 uWSGI 进程已停止，端口 5001 已完全释放
[0;34m[06:17:09][0m ▶️  启动新服务...
[0;34m[06:17:09][0m 🚀 执行启动命令: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[uWSGI] getting INI configuration from uwsgi.ini
[0;34m[06:17:09][0m ✅ 启动命令执行完成
[0;34m[06:17:09][0m ⏳ 等待服务启动 (最多15秒)...
[0;34m[06:17:10][0m ✅ 第 1 秒: 发现 uWSGI 进程 (PID: 10209) 并且端口 5001 已监听
[0;34m[06:17:11][0m 🔍 最终状态检查...
[0;32m[SUCCESS][0m 🎉 服务启动成功！
[0;34m[06:17:11][0m    进程ID: 10209
[0;34m[06:17:11][0m    监听端口: 127.0.0.1:5001
[0;34m[06:17:11][0m    配置文件: /home/<USER>/web_server/uwsgi.ini
[0;34m[06:17:11][0m    日志文件: /home/<USER>/web_server/logs/uwsgi.log
[0;34m[06:17:11][0m 📊 进程详细信息:
[0;34m[06:17:11][0m    10209     1 uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[06:17:11][0m 📋 最新日志 (最后5行):
[0;34m[06:17:11][0m    your server socket listen backlog is limited to 128 connections
[0;34m[06:17:11][0m    your mercy for graceful operations on workers is 60 seconds
[0;34m[06:17:11][0m    mapped 145840 bytes (142 KB) for 1 cores
[0;34m[06:17:11][0m    *** Operational MODE: single process ***
[0;34m[06:17:11][0m    INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
[0;34m[06:17:11][0m 📊 检查服务状态...
[0;32m[SUCCESS][0m 服务运行中，发现 1 个进程
[0;34m[06:17:11][0m    PID 10209: uwsgi --ini uwsgi.ini --daemonize logs/uwsgi.log
[0;34m[06:17:11][0m 监听端口:
[0;34m[06:17:11][0m   tcp        0      0 127.0.0.1:5001          0.0.0.0:*               LISTEN      10209/uwsgi
[0;34m[06:17:11][0m 🔍 检查应用加载状态...
[0;32m[SUCCESS][0m 服务进程正常运行，应用状态可用
[0;32m[SUCCESS][0m 🎉 快速部署完成！
[0;34m[06:17:28][0m 📋 后续操作:
[0;34m[06:17:28][0m   • 查看服务日志: tail -f /home/<USER>/web_server/logs/uwsgi.log
[0;34m[06:17:28][0m   • 检查服务状态: ./quick_deploy.sh status
[0;34m[06:17:28][0m   • 查看进程详情: ps aux | grep uwsgi
[0;34m[06:17:28][0m   • 查看端口占用: netstat -tlnp | grep 5001
[0;34m[06:17:28][0m   • 查看进程树: pstree -p $(pgrep -f uwsgi | head -1)
[0;34m[06:17:28][0m   • 查看部署日志: cat /home/<USER>/web_server/logs/quick_deploy_20250703_061648.log
