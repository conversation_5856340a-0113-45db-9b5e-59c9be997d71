*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 05:41:36 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
writing pidfile to /home/<USER>/web_server/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5001).
bind(): Address already in use [core/socket.c line 769]
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 05:49:47 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
writing pidfile to /home/<USER>/web_server/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x556acb5f10
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
玉米密度模块加载完成！
棉花密度算法模块加载完成！
水稻密度算法模块加载完成！
✓ 成功初始化point_cloud先进模块
  - 高度识别使用先进的DBSCAN聚类算法
  - 可视化使用Plotly生成高质量图像和GIF
  - 系统已移除老的算法，只使用最新的先进算法
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 16 seconds on interpreter 0x556acb5f10 pid: 5729 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 5729)
spawned uWSGI worker 1 (pid: 5979, cores: 1)
*** Stats server enabled on /home/<USER>/web_server/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/web_server
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5001)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 05:52:50 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x556b211d70
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 05:52:55 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
writing pidfile to /home/<USER>/web_server/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5574acaf10
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
玉米密度模块加载完成！
棉花密度算法模块加载完成！
水稻密度算法模块加载完成！
✓ 成功初始化point_cloud先进模块
  - 高度识别使用先进的DBSCAN聚类算法
  - 可视化使用Plotly生成高质量图像和GIF
  - 系统已移除老的算法，只使用最新的先进算法
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x5574acaf10 pid: 6499 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 6499)
spawned uWSGI worker 1 (pid: 6730, cores: 1)
*** Stats server enabled on /home/<USER>/web_server/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/web_server
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5001)
running /home/<USER>/.local/bin/uwsgi
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 05:54:53 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
writing pidfile to /home/<USER>/web_server/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557e231f10
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
玉米密度模块加载完成！
棉花密度算法模块加载完成！
水稻密度算法模块加载完成！
✓ 成功初始化point_cloud先进模块
  - 高度识别使用先进的DBSCAN聚类算法
  - 可视化使用Plotly生成高质量图像和GIF
  - 系统已移除老的算法，只使用最新的先进算法
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 16 seconds on interpreter 0x557e231f10 pid: 7112 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 7112)
spawned uWSGI worker 1 (pid: 7350, cores: 1)
*** Stats server enabled on /home/<USER>/web_server/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/web_server
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5001)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 05:58:44 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557316fd70
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 05:58:49 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
writing pidfile to /home/<USER>/web_server/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55a0e73f10
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x55a0e73f10 pid: 7677 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 7677)
spawned uWSGI worker 1 (pid: 7914, cores: 1)
*** Stats server enabled on /home/<USER>/web_server/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
worker 1 buried after 4 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/web_server
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5001)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 06:03:26 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55b0ac8d70
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 06:03:33 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
writing pidfile to /home/<USER>/web_server/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x555cf6ef10
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 16 seconds on interpreter 0x555cf6ef10 pid: 8475 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 8475)
spawned uWSGI worker 1 (pid: 8715, cores: 1)
*** Stats server enabled on /home/<USER>/web_server/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/web_server
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5001)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 06:17:03 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55b1e55d70
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 06:17:09 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
writing pidfile to /home/<USER>/web_server/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5578139f10
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x5578139f10 pid: 10209 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 10209)
spawned uWSGI worker 1 (pid: 10469, cores: 1)
*** Stats server enabled on /home/<USER>/web_server/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/web_server
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5001)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul  3 08:27:32 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557a13ad70
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
/home/<USER>/web_server/manage.py:3: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.fernet import Fernet
Traceback (most recent call last):
  File "/home/<USER>/web_server/manage.py", line 30, in <module>
    _execute_protected_code()
  File "/home/<USER>/web_server/manage.py", line 22, in _execute_protected_code
    exec(main_code, globals())
  File "<string>", line 7
    ""_decrypt_string("Z0FBQUFBQm9aaWxRV0NKRXZrVy1kN0E0N05KTG1vY0RTZXJSYVQxUGprYzJLSm9BdUpOOHJrX0RGLU9wcjhfOVFXR0JrVUU2eFdENzdEUUUtYlgxS3RyaWtQc0lsREJIYzB2MjBkeWZVTVRNd3hCZ1FqcUJFWE5Cb3dWY1A4TG1rbFZoTWhFVVNIUmZDQzZ0SjI2S2YwSnd3TDB6NElVLTlzYVlSa0tpREJ5dVdSSkh0ckdjaEtYcDQtTUx5U3JkVjRDUFZYYU1RRnJ5")""
                    ^
SyntaxError: invalid syntax
unable to load app 0 (mountpoint='') (callable not found or import error)
*** no app loaded. going in full dynamic mode ***
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 10209)
spawned uWSGI worker 1 (pid: 22665, cores: 1)
*** Stats server enabled on /home/<USER>/web_server/uwsgi/uwsgi.status fd: 9 ***
Thu Jul  3 08:27:35 2025 - uWSGI worker 1 screams: UAAAAAAH my master disconnected: i will kill myself !!!
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul 17 06:21:49 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server_source
writing pidfile to /home/<USER>/web_server_source/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server_source
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557817b750
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_source_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_source_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x557817b750 pid: 2941 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 2941)
spawned uWSGI worker 1 (pid: 3160, cores: 1)
*** Stats server enabled on /home/<USER>/web_server_source/uwsgi/uwsgi.status fd: 10 ***
[pid: 3160|app: 0|req: 1/1] ************** () {32 vars in 374 bytes} [Thu Jul 17 06:22:18 2025] GET /v1/state => generated 186 bytes in 33 msecs (HTTP/1.1 405) 2 headers in 88 bytes (1 switches on core 0)
FTP初始化成功!
E:app.core.exceptions:Unexpected exception: timed out
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/app.py", line 1820, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/app.py", line 1796, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "./app/api_v1_0/state_detect.py", line 37, in getjson_state
    get_temperature("20220615141000","20220615143000","second","cF")
  File "./app/tasks/temperature/get_temperature.py", line 27, in get_temperature
    file_list=cur_ftp.get_filelist(cur_url_path)
  File "./app/utils/ftp.py", line 77, in get_filelist
    ftp = self.ftp_connect()
  File "./app/utils/ftp.py", line 28, in ftp_connect
    ftp.connect(host=self.host, port=self.port) # 连接ftp
  File "/usr/lib/python3.7/ftplib.py", line 152, in connect
    source_address=self.source_address)
  File "/usr/lib/python3.7/socket.py", line 727, in create_connection
    raise err
  File "/usr/lib/python3.7/socket.py", line 716, in create_connection
    sock.connect(sa)
socket.timeout: timed out
[pid: 3160|app: 0|req: 2/2] ************** () {32 vars in 375 bytes} [Thu Jul 17 06:22:29 2025] POST /v1/state => generated 138 bytes in 10111 msecs (HTTP/1.1 500) 2 headers in 91 bytes (1 switches on core 0)
FTP初始化成功!
E:app.core.exceptions:Unexpected exception: timed out
Traceback (most recent call last):
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/app.py", line 1820, in full_dispatch_request
    rv = self.dispatch_request()
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/app.py", line 1796, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)
  File "./app/api_v1_0/state_detect.py", line 37, in getjson_state
    get_temperature("20220615141000","20220615143000","second","cF")
  File "./app/tasks/temperature/get_temperature.py", line 27, in get_temperature
    file_list=cur_ftp.get_filelist(cur_url_path)
  File "./app/utils/ftp.py", line 77, in get_filelist
    ftp = self.ftp_connect()
  File "./app/utils/ftp.py", line 28, in ftp_connect
    ftp.connect(host=self.host, port=self.port) # 连接ftp
  File "/usr/lib/python3.7/ftplib.py", line 152, in connect
    source_address=self.source_address)
  File "/usr/lib/python3.7/socket.py", line 727, in create_connection
    raise err
  File "/usr/lib/python3.7/socket.py", line 716, in create_connection
    sock.connect(sa)
socket.timeout: timed out
[pid: 3160|app: 0|req: 3/3] ************** () {32 vars in 375 bytes} [Thu Jul 17 06:23:13 2025] POST /v1/state => generated 138 bytes in 10094 msecs (HTTP/1.1 500) 2 headers in 91 bytes (1 switches on core 0)
...brutally killing workers...
worker 1 buried after 4 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/web_server_source
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5001)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul 17 06:24:36 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server_source
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server_source
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55b7c27530
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_source_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul 17 06:24:43 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server_source
writing pidfile to /home/<USER>/web_server_source/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server_source
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559bc56750
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_source_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_source_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x559bc56750 pid: 3782 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 3782)
spawned uWSGI worker 1 (pid: 4001, cores: 1)
*** Stats server enabled on /home/<USER>/web_server_source/uwsgi/uwsgi.status fd: 10 ***
I:app:************** /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 4001|app: 0|req: 1/1] ************** () {32 vars in 375 bytes} [Thu Jul 17 06:25:16 2025] POST /v1/state => generated 70 bytes in 33 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:************** /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 4001|app: 0|req: 2/2] ************** () {32 vars in 375 bytes} [Thu Jul 17 06:27:19 2025] POST /v1/state => generated 70 bytes in 6 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
*** Starting uWSGI 2.0.21 (64bit) on [Thu Jul 17 06:33:32 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #25 SMP Fri Jan 6 15:44:35 CST 2023
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/web_server_source
writing pidfile to /home/<USER>/web_server_source/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server_source
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5001 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557468b760
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_source_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_source_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x557468b760 pid: 4819 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 4819)
spawned uWSGI worker 1 (pid: 5055, cores: 1)
*** Stats server enabled on /home/<USER>/web_server_source/uwsgi/uwsgi.status fd: 10 ***
E:app:点云识别未预期错误: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
Traceback (most recent call last):
  File "./app/api_v1_0/pc_detect.py", line 346, in getjson_point
    data = request.get_json()
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 571, in get_json
    return self.on_json_loading_failed(None)
  File "/home/<USER>/.local/lib/python3.7/site-packages/flask/wrappers.py", line 128, in on_json_loading_failed
    return super().on_json_loading_failed(e)
  File "/home/<USER>/.local/lib/python3.7/site-packages/werkzeug/wrappers/request.py", line 612, in on_json_loading_failed
    "Did not attempt to load JSON data because the request"
werkzeug.exceptions.BadRequest: 400 Bad Request: Did not attempt to load JSON data because the request Content-Type was not 'application/json'.
[pid: 5055|app: 0|req: 1/1] ************** () {36 vars in 471 bytes} [Thu Jul 17 06:43:26 2025] POST /v1/detect_pointcloud => generated 96 bytes in 51 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
