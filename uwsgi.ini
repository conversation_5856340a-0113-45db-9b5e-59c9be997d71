[uwsgi]
; http=192.168.14.147:5001
; socket=192.168.14.147:5001
# 这里放的是绝对引用地址！！！
chdir = /home/<USER>/apps/web_server/current
socket = 127.0.0.1:5003
; http-socket=127.0.0.1:5001
#虚拟环境中的目录，这里env后边不要/bin
; home = /home/<USER>/.local/lib/python3.7
; home=/usr/lib
#启动的文件
wsgi-file = %(chdir)/manage.py
# python 程序内用以启动的 application 变量名,不加callable=app，访问时报服务器错误Internal Server Error
callable = app
# 处理器数
; processes = 1
processes = 1   # 并发处理进程数
listen = 128  # 并发的soceket 连接数。默认为100。优化需要根据系统配
# 线程数
; threads = 1
; buffer-size = 32768
; vacuum = true
master = true
enable-threads = true

stats = %(chdir)/uwsgi/uwsgi.status
pidfile = %(chdir)/uwsgi/uwsgi.pid
