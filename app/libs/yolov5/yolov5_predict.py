# coding:utf-8

import numpy as np, cv2
from rknnlite.api import RKNNLite

class Yolov5(object):
    def __init__(self, model, class_json, imgsz=640, conf_thres=0.25, nms_thres=0.45):
        self.imgsz = imgsz
        self.conf_thres = conf_thres
        self.nms_thres = nms_thres
        self.class_json = class_json

        # 初始化npu 
        self.dnn = self.init_npu()
        # 加载模型
        self.load_model(model)
        # 运行npu
        self.npu_status = self.run_npu()

    def get_npu_status(self):
        return self.npu_status
    
    def sigmoid(self, x):
        return 1 / (1 + np.exp(-x))
        
    def parse_labels(self, labels_path):
        options = dict()
        with open(labels_path,'r') as f:
            lines = f.readlines()
        for line in lines:
            key, value = line.strip().split(' ')
            options[key.strip()] = value.strip()
        
        return options
    
    def xywh2xyxy(self, x):
        y = np.copy(x)
        y[:, 0] = x[:, 0] - x[:, 2] / 2
        y[:, 1] = x[:, 1] - x[:, 3] / 2
        y[:, 2] = x[:, 0] + x[:, 2] / 2
        y[:, 3] = x[:, 1] + x[:, 3] / 2
        return y

    def process(self, input, mask, anchors, img_size):
        anchors = [anchors[i] for i in mask]
        grid_h, grid_w = map(int, input.shape[0:2])
        box_confidence = self.sigmoid(input[(Ellipsis, 4)])
        box_confidence = np.expand_dims(box_confidence, axis=(-1))
        box_class_probs = self.sigmoid(input[..., 5:])
        box_xy = self.sigmoid(input[..., :2]) * 2 - 0.5
        col = np.tile(np.arange(0, grid_w), grid_w).reshape(-1, grid_w)
        row = np.tile(np.arange(0, grid_h).reshape(-1, 1), grid_h)
        col = col.reshape(grid_h, grid_w, 1, 1).repeat(3, axis=(-2))
        row = row.reshape(grid_h, grid_w, 1, 1).repeat(3, axis=(-2))
        grid = np.concatenate((col, row), axis=(-1))
        box_xy += grid
        box_xy *= int(img_size / grid_h)
        box_wh = pow(self.sigmoid(input[..., 2:4]) * 2, 2)
        box_wh = box_wh * anchors
        box = np.concatenate((box_xy, box_wh), axis=(-1))
        return (box, box_confidence, box_class_probs)

    def filter_boxes(self, boxes, box_confidences, box_class_probs, conf_thres):
        box_classes = np.argmax(box_class_probs, axis=(-1))
        box_class_scores = np.max(box_class_probs, axis=(-1))
        pos = np.where(box_confidences[(Ellipsis, 0)] >= conf_thres)
        boxes = boxes[pos]
        classes = box_classes[pos]
        scores = box_class_scores[pos]
        return (boxes, classes, scores)

    def nms_boxes(self, boxes, scores, nms_thres):
        x = boxes[:, 0]
        y = boxes[:, 1]
        w = boxes[:, 2] - boxes[:, 0]
        h = boxes[:, 3] - boxes[:, 1]
        areas = w * h
        order = scores.argsort()[::-1]
        keep = []
        while order.size > 0:
            i = order[0]
            keep.append(i)
            xx1 = np.maximum(x[i], x[order[1:]])
            yy1 = np.maximum(y[i], y[order[1:]])
            xx2 = np.minimum(x[i] + w[i], x[order[1:]] + w[order[1:]])
            yy2 = np.minimum(y[i] + h[i], y[order[1:]] + h[order[1:]])
            w1 = np.maximum(0.0, xx2 - xx1 + 1e-05)
            h1 = np.maximum(0.0, yy2 - yy1 + 1e-05)
            inter = w1 * h1
            ovr = inter / (areas[i] + areas[order[1:]] - inter)
            inds = np.where(ovr <= nms_thres)[0]
            order = order[inds + 1]

        keep = np.array(keep)
        return keep

    def yolov5_post_process(self, input_data):
        masks = [
        [
        0, 1, 2], [3, 4, 5], [6, 7, 8]]
        anchors = [[10, 13], [16, 30], [33, 23], [30, 61], [62, 45],
        [
        59, 119], [116, 90], [156, 198], [373, 326]]
        boxes, classes, scores = [], [], []
        for input, mask in zip(input_data, masks):
            b, c, s = self.process(input, mask, anchors, self.imgsz)
            b, c, s = self.filter_boxes(b, c, s, self.conf_thres)
            boxes.append(b)
            classes.append(c)
            scores.append(s)

        boxes = np.concatenate(boxes)
        boxes = self.xywh2xyxy(boxes)
        classes = np.concatenate(classes)
        scores = np.concatenate(scores)
        nboxes, nclasses, nscores = [], [], []
        for c in set(classes):
            inds = np.where(classes == c)
            b = boxes[inds]
            c = classes[inds]
            s = scores[inds]
            keep = self.nms_boxes(b, s, self.nms_thres)
            nboxes.append(b[keep])
            nclasses.append(c[keep])
            nscores.append(s[keep])

        if not nclasses:
            if not nscores:
                return ([], [], [])
        boxes = np.concatenate(nboxes)
        classes = np.concatenate(nclasses)
        scores = np.concatenate(nscores)
        return (boxes, classes, scores)

    def letterbox(self, im, new_shape=(640, 640), color=(0, 0, 0)):
        shape = im.shape[:2]
        if isinstance(new_shape, int):
            new_shape = (
            new_shape, new_shape)
        r = min(new_shape[0] / shape[0], new_shape[1] / shape[1])
        ratio = (
        r, r)
        new_unpad = (int(round(shape[1] * r)), int(round(shape[0] * r)))
        dw, dh = new_shape[1] - new_unpad[0], new_shape[0] - new_unpad[1]
        dw /= 2
        dh /= 2
        if shape[::-1] != new_unpad:
            im = cv2.resize(im, new_unpad, interpolation=(cv2.INTER_LINEAR))
        top, bottom = int(round(dh - 0.1)), int(round(dh + 0.1))
        left, right = int(round(dw - 0.1)), int(round(dw + 0.1))
        im = cv2.copyMakeBorder(im, top, bottom, left, right, (cv2.BORDER_CONSTANT), value=color)
        return (im, ratio, (dw, dh))


    def init_npu(self):
        rknn = RKNNLite(verbose=False)
        return rknn


    def run_npu(self):
        ret = self.dnn.init_runtime()
        return ret


    def release_npu(self):
        self.dnn.release()


    def load_model(self, model):
        ret = self.dnn.load_rknn(model)
        return ret


    def inference(self, img):
        outputs = self.dnn.inference(inputs=[img])
        # self.release_npu()
        return outputs


    def image_pre_process(self, img_path):
        img = cv2.imread(img_path)
        img, ratio, (dw, dh) = self.letterbox(img, new_shape=(self.imgsz, self.imgsz))
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        return img


    def image_post_process(self, outputs):
        class_list = []
        input0_data = outputs[0]
        input1_data = outputs[1]
        input2_data = outputs[2]
        input0_data = input0_data.reshape([3, -1] + list(input0_data.shape[-2:]))
        input1_data = input1_data.reshape([3, -1] + list(input1_data.shape[-2:]))
        input2_data = input2_data.reshape([3, -1] + list(input2_data.shape[-2:]))
        input_data = list()
        input_data.append(np.transpose(input0_data, (1, 2, 0, 3)))
        input_data.append(np.transpose(input1_data, (1, 2, 0, 3)))
        input_data.append(np.transpose(input2_data, (1, 2, 0, 3)))
        boxes, classes, scores = self.yolov5_post_process(input_data)

        classes_label = self.parse_labels(self.class_json)
        # print(len(classes))
        classes =  [] if len(classes) == 0 else classes.tolist()
        # print(classes)
        # print(classes.all()!=None)
        # print(classes.any()!=None)

            # print(classes)
        for cl in classes:
            # print(cl)
            class_list.append(classes_label[str(cl)])

        return class_list
