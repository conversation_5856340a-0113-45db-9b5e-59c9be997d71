

import os.path
import pandas as pd
import app.libs.sound_v3.melspec_generator as melspec_generator
import app.libs.sound_v3.inference_arm as inference_arm
import json
import numpy as np
from pydub import AudioSegment

# with open('config.json', 'r') as config_file:
#     config = json.load(config_file)

# machine = platform.machine()
# class_name = config["class_name"]

class SoundDetection(object):
    def __init__(self, config_json):
        with open(config_json, 'r') as config_file:
            self.config = json.load(config_file)
    
        model = self.config["rknn_model"]
        self.rknn = inference_arm.init_model(model)

    def mp3_to_ogg(self, mp3_file):
        try:
            ogg_file = mp3_file[:-3] + "ogg"
            audio = AudioSegment.from_file(mp3_file, format="mp3")
            audio.export(ogg_file, format="ogg")
            return ogg_file
        except:
            print("mp3 to wave failure")
    
    def get_config(self, item):
        return self.config.get(item)


    def normalize(self, image):  # 标准化图像
        image = image.astype("float32", copy=False) 
        image = np.stack([image, image, image])
        return image

    def Preprocss(self, image_path):
        image = np.load(image_path)[0]
        image = self.normalize(image)
        return image

    # 生成标签
    def to_std_label(self,row):
        pred_label_id = ' '
        pre_label_name = ''
        threshold = self.config["threshold"]
        class_name = self.config["class_name"]
        for i in range(len(class_name)):
            if row[i + 2] > threshold[i]:
                pred_label_id = pred_label_id + str(i) + ' '
                pre_label_name = pre_label_name + class_name[i]
        return pred_label_id, pre_label_name
    
    def release_npu(self):
        self.rknn.release()

    
    def melspec_generator(self, sound_path):
        audio_path = sound_path
        if audio_path.endswith(".mp3"):
            audio_path = self.mp3_to_ogg(audio_path)
        print(audio_path)

        segment = self.config['segment']
        debugMode = True if self.config['debugMode']=="True" else False # debug接口
        mode_singledetect = True if self.config['mode_singledetect']=="True" else False

        # 导入音频路径，【‘filename', 'filepath'】形式存储在表格中
        df = pd.DataFrame(columns=['filename', 'filepath'])
        if mode_singledetect:
            audiopath = audio_path
            filename = os.path.split(audiopath)[1]
            df.loc[len(df.index)] = [filename, audiopath]

        df, mel_time = melspec_generator.melspec_generator(df, segment=segment)

        return df
    
    def sound_detect(self, df):

        # audio_path = sound_path
        # if audio_path.endswith(".mp3"):
        #     audio_path = self.mp3_to_ogg(audio_path)
        # print(audio_path)

        # segment = self.config['segment']
        # debugMode = True if self.config['debugMode']=="True" else False # debug接口
        # mode_singledetect = True if self.config['mode_singledetect']=="True" else False

        # # 导入音频路径，【‘filename', 'filepath'】形式存储在表格中
        # df = pd.DataFrame(columns=['filename', 'filepath'])
        # if mode_singledetect:
        #     audiopath = audio_path
        #     filename = os.path.split(audiopath)[1]
        #     df.loc[len(df.index)] = [filename, audiopath]
        # # else:
        # #     for (root, dirs, files) in os.walk(path):
        # #         if len(files):
        # #             for file in files:
        # #                 audiopath = os.path.join(root, file)
        # #                 filename = file
        # #                 df.loc[len(df.index)] = [filename, audiopath]

        # # 生成mel-spec,并把生成mel-spec图片存储路径melpath附在表格上，【'filename', 'melpath'】形式存储
        # df, mel_time = melspec_generator.melspec_generator(df, segment=segment)

        # 模型推理
        # oof_columns = ["filename", "seconds"]
        # oof_columns= oof_columns + class_name
        # oof = pd.DataFrame(columns=oof_columns)
        pred_re = []
        # infer_start = time.time()
        num_audio = len(df)
        for idx, row in df.iterrows():
            filename = row['filename']
            seconds = row['seconds']
            melpath = row['melpath']
            result_row = []
            result_row.append(filename)
            result_row.append(seconds)

            print("[" + str(idx+1) + "|" + str(num_audio) + "] processing: " + filename + "[start seconds:" + str(row.seconds) + "]" )
            
            # 预处理
            image = self.Preprocss(melpath)

            # 端侧推理

            image = image.reshape((3, 128, 201)).astype("float32")
            image = image.transpose((1, 2, 0))
            prob = inference_arm.inference(image, self.rknn, self.config["class_name"])
            # result_row = result_row + prob
            # oof.loc[len(oof)] = result_row
        
            pred_re.append(prob)
    
        return pred_re
        
        # print(oof)


if __name__ == '__main__':
    sound_path = "./Dataset/Test/HB001_20230529060000_TIMING.mp3"
    sound_object = SoundDetection('config.json')
    preditct_result = sound_object.sound_detect(sound_path)
    class_name = sound_object.get_config('class_name')
    count = np.sum(preditct_result,axis=0)
    # print(len(count))
        # 降序
    count_sort = -np.sort(-count)
    max1 = count_sort[0]
    max2 = count_sort[1]
    max3 = count_sort[2]
    # max = np.max(count)
    index1 = np.where(count==max1)[0].tolist()
    class_list1 = [class_name[i] for i in index1] if max1 != 0 else []

    index2 = np.where(count==max2)[0].tolist()
    class_list2 = [class_name[i] for i in index2] if max2 != 0 else []

    index3 = np.where(count==max3)[0].tolist()
    class_list3 = [class_name[i] for i in index3] if max3 != 0 else []
    # class_list = [class_code[i] for i in index] if max != 0 else []

    class_list = class_list1 + class_list2 + class_list3
    class_three = class_list[0:3] if len(class_list)>3 else class_list
    
    print(class_three)