"""
File:     melspec_generator.py
Version:  V 1.1
"""
import numpy as np
import librosa as lb
import time
from pathlib import Path
import joblib  
import warnings
import pandas as pd
# import soundfile as sf
# import sys, getopt
import os
from app.utils.parse_ini import ParseIniFile

pif = ParseIniFile('app/config.ini')
workdir = pif.get_data("common", "static_dir")

warnings.filterwarnings('ignore')


SR = 32_000      
DURATION = 5 
row_num = 0

def mono_to_color(X, eps=1e-6, mean=None, std=None):
    mean = mean or X.mean() 
    std = std or X.std()
    X = (X - mean) / (std + eps)

    _min, _max = X.min(), X.max()

    if (_max - _min) > eps:
        V = np.clip(X, _min, _max)
        V = 255 * (V - _min) / (_max - _min)
        V = V.astype(np.uint8)
    else:
        V = np.zeros_like(X, dtype=np.uint8)

    return V

class MelSpecComputer:
    def __init__(self, sr, n_mels, fmin, fmax, **kwargs):
        self.sr = sr
        self.n_mels = n_mels
        self.fmin = fmin
        self.fmax = fmax
        kwargs["n_fft"] = kwargs.get("n_fft", self.sr//10)
        kwargs["hop_length"] = kwargs.get("hop_length", self.sr//(10*4))
        self.kwargs = kwargs

    def __call__(self, y):

        melspec = lb.feature.melspectrogram(
            y, sr=self.sr, n_mels=self.n_mels, fmin=self.fmin, fmax=self.fmax, **self.kwargs,
        )

        melspec = lb.power_to_db(melspec).astype(np.float32)
        return melspec

def crop_or_pad(y, length, is_train=True, start=None):
    if len(y) < length:
        y = np.concatenate([y, np.zeros(length - len(y))])

        n_repeats = length // len(y)
        epsilon = length % len(y)

        y = np.concatenate([y] * n_repeats + [y[:epsilon]])

    elif len(y) > length:
        if not is_train:
            start = start or 0
        else:
            start = start or np.random.randint(len(y) - length)

        y = y[start:start + length]

    return y

class AudioToImage:
    def __init__(self, saveRootPath, sr=SR, n_mels=128, fmin=0, fmax=None, duration=DURATION, step=None, res_type="kaiser_fast",
                 resample=True):

        self.saveRootPath = saveRootPath
        self.sr = sr
        self.n_mels = n_mels
        self.fmin = fmin
        self.fmax = fmax or self.sr // 2

        self.duration = duration
        self.audio_length = self.duration * self.sr
        self.step = step or self.audio_length

        self.res_type = res_type
        self.resample = resample
        self.mel_df = pd.DataFrame(columns=['filename', 'seconds', 'melpath'])

        self.mel_spec_computer = MelSpecComputer(sr=self.sr, n_mels=self.n_mels, fmin=self.fmin,
                                                 fmax=self.fmax)

    def audio_to_image(self, audio):
        melspec = self.mel_spec_computer(audio)
        image = mono_to_color(melspec)
        return image

    def __call__(self, row):

        begin_time = time.time()

        start_time = time.time()
        audio, orig_sr = lb.load(row.filepath, dtype="float32", mono=True, sr=32000) 
        if self.resample and orig_sr != self.sr: 
            audio = lb.resample(audio, orig_sr, self.sr, res_type=self.res_type)
        read_time = time.time() - start_time
        audios = []
        time_stop = 0
        for i in range(0, max(1, len(audio) - self.step + 1), self.step):
            audios.append(audio[i:i+self.step])
            time_stop = i + self.step
        if time_stop < len(audio) + 1:
            if len(audio) - time_stop > 2 * self.sr:
                audios.append(audio[time_stop - 1:])
        seconds = 0
        for audio_s in audios:
            audio_s = crop_or_pad(audio_s, length=self.audio_length)
            start_time = time.time()
            image = self.audio_to_image(audio_s)
            transfer_time = time.time() - start_time
            image = np.stack([image])
            path = self.saveRootPath / f"{row.filename}_{seconds}.npy"
            path.parent.mkdir(exist_ok=True, parents=True)
            np.save(str(path), image)
            self.mel_df.loc[len(self.mel_df.index)] = [row.filename, seconds, path]
            seconds += self.step // self.sr
            all_time = time.time() - begin_time
        return row.filename, seconds, read_time, transfer_time, all_time


def get_audios_as_images(df, saveRootPath, segment):

    pool = joblib.Parallel(8) 
    converter = AudioToImage(saveRootPath=saveRootPath,  step=int(segment * SR))
    mapper = joblib.delayed(converter)
    tasks = [mapper(row) for row in df.itertuples(False)]

    filename_seconds_list = pool(tasks)


    mel_df = pd.DataFrame(columns=['filename', 'seconds', 'melpath'])
    for filename, max_seconds, read_time, transfer_time, all_time in filename_seconds_list:
        for sec in range(0, max_seconds, segment):
            mel_path = saveRootPath / f"{filename}_{sec}.npy"
            mel_df.loc[len(mel_df.index)] = [filename, sec, str(mel_path)]
            print(filename + "   read_time:  "+str(read_time * 1000)+", transfer_time:   "+str(transfer_time * 1000)+", all_time:   "+str(all_time * 1000)+"\n")

    return mel_df


def melspec_generator(meta_df, segment=5, AUDIO_IMAGES_SAVE_ROOT=Path(os.path.join(workdir,'temp/MelspecImage'))):
    print("make mfcc\n")
    start = time.time()
    AUDIO_IMAGES_SAVE_ROOT.mkdir(exist_ok=True, parents=True)

    mel_df = get_audios_as_images(meta_df, saveRootPath=AUDIO_IMAGES_SAVE_ROOT, segment=segment)

    mel_spend_time = time.time() - start
    return mel_df, mel_spend_time
