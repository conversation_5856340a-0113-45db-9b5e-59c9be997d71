"""
File:     predict.py
Version:  V 2.0
"""
import os
import os.path
import pandas as pd
import app.libs.sound_v2.melspec_generator as melspec_generator

# from utils import melspec_generator
import json
import numpy as np
from app.libs.sound_v2.inference_arm import InferenceArm
from pydub import AudioSegment


class SoundDetection(object):
    def __init__(self, config_json):
        with open(config_json, 'r') as config_file:
            self.config = json.load(config_file)

        bird_model = self.config["rknn_birdmodel"]
        nonbird_model = self.config["rknn_nonbirdmodel"]
        self.rknn_bird = InferenceArm(bird_model)
        self.rknn_nonbird = InferenceArm(nonbird_model)
        
    def mp3_to_ogg(self, mp3_file):
        try:
            ogg_file = mp3_file[:-3] + "ogg"
            audio = AudioSegment.from_file(mp3_file, format="mp3")
            audio.export(ogg_file, format="ogg")
            return ogg_file
        except:
            print("mp3 to wave failure")
    
    def get_config(self, item):
        return self.config.get(item)
    
    def release_npu(self):
        self.rknn_bird.release_npu()
        self.rknn_nonbird.release_npu()
        
    def normalize(self, image):
        image = image.astype("float32", copy=False) 
        image = np.stack([image, image, image])
        return image

    def Preprocss(self, image_path):
        image = np.load(image_path)[0]
        image = self.normalize(image)
        return image

    def valid_point(self, prob,num=3):
        num_ = len(prob)
        prob_valid = []
        for i in range(num_):
            prob_valid.append(round(prob[i],num))
        return prob_valid

    def merge(self, prob_bird, prob_nonbird):
        num_class = len(self.config["class_name"])
        list_bird = self.config["bird_class"]
        list_nonbird = self.config["nonbird_class"]

        score = []
        for i in range(num_class):
            if i in list_bird :
                score.append(prob_bird[list_bird.index(i)])
            elif i in list_nonbird :
                score.append(prob_nonbird[list_nonbird.index(i)])
            else :
                score.append(0)

        return score
    
    def melspec_generator(self, sound_path):
        audio_path = sound_path
        if audio_path.endswith(".mp3"):
            audio_path = self.mp3_to_ogg(audio_path)
        print(audio_path)

        segment = self.config['segment'] 
        mode_singledetect = True if self.config['mode_singledetect']=="True" else False

        df = pd.DataFrame(columns=['filename', 'filepath'])
        if mode_singledetect:
            audiopath = audio_path
            filename = os.path.split(audiopath)[1]
            df.loc[len(df.index)] = [filename, audiopath]

        df, mel_time = melspec_generator.melspec_generator(df, segment=segment)

        return df
    
    
    
    def sound_detect(self, df):

        # audio_path = sound_path
        # if audio_path.endswith(".mp3"):
        #     audio_path = self.mp3_to_ogg(audio_path)
        # print(audio_path)

        # segment = self.config['segment'] 
        # mode_singledetect = True if self.config['mode_singledetect']=="True" else False

        # df = pd.DataFrame(columns=['filename', 'filepath'])
        # if mode_singledetect:
        #     audiopath = audio_path
        #     filename = os.path.split(audiopath)[1]
        #     df.loc[len(df.index)] = [filename, audiopath]

        # df, mel_time = melspec_generator.melspec_generator(df, segment=segment)

        pred_re = []
        list_bird = self.config["bird_class"]
        list_nonbird = self.config["nonbird_class"]
        for idx, row in df.iterrows():
            filename = row['filename']
            seconds = row['seconds']
            melpath = row['melpath']
            result_row = []
            result_row.append(filename)
            result_row.append(seconds)      
            image = self.Preprocss(melpath)
            image = image.reshape((3, 128, 201)).astype("float32")
            image = image.transpose((1, 2, 0))
            prob_bird = self.rknn_bird.predict(image, list_bird)
            prob_nonbird = self.rknn_nonbird.predict(image, list_nonbird)
            prob = self.merge(prob_bird, prob_nonbird)
            prob = self.valid_point(prob)
            pred_re.append(prob)

        return pred_re

if __name__ == '__main__':
    sound_path = "/home/<USER>/sounddetect0803/demo/HB001.mp3"
    sound_object = SoundDetection('config.json')
    preditct_result = sound_object.sound_detect(sound_path)

    thresh = sound_object.get_config('thresh')
    class_name = sound_object.get_config('class_name')
    class_code = sound_object.get_config('class_code')

    pre_res_array = np.array(preditct_result)
    thresh_array = np.array(thresh)

    count = np.sum(pre_res_array>=thresh_array, axis=0)
    max = np.max(count)
    index = np.where(count==max)[0].tolist()

    class_list = [(class_name[i], class_code[i]) for i in index]
    
    print(class_list)
