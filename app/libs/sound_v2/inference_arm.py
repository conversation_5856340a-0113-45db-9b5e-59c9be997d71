"""
File:     inference_arm.py
Version:  V 2.0
"""

from __future__ import absolute_import
from __future__ import division
from __future__ import print_function
import numpy as np
from rknnlite.api import RKNNLite
from flask import current_app


class InferenceArm(object):
    def __init__(self, model):
        # 初始化npu 
        self.dnn = self.init_npu()
        # 加载模型
        self.load_model(model)
        # 运行npu
        self.npu_status = self.run_npu()


    def sigmoid(self, x):
        s = 1 / (1 + np.exp(-x))
        return s

    def Postprocss(self, output_data, list_class):
        prob = []
        out_dims = len(list_class)
        for i in range(out_dims):
            prob.append(float(self.sigmoid(output_data[0][i])))

        return prob


    def predict(self, mel_image, class_list):
        if self.npu_status != 0:
            current_app.logger.info('Init runtime environment failed')
            exit(self.npu_status)

        outputs = self.inference(mel_image)
        output_data = outputs[0]
        prob = self.Postprocss(output_data, class_list)

        return prob

    def init_npu(self):
        rknn = RKNNLite(verbose=False)
        return rknn


    def run_npu(self):
        ret = self.dnn.init_runtime()
        return ret


    def release_npu(self):
        self.dnn.release()


    def load_model(self, model):
        ret = self.dnn.load_rknn(model)
        return ret


    def inference(self, img):
        outputs = self.dnn.inference(inputs=[img])
        return outputs


    
