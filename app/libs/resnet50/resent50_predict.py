# coding:utf-8
import os
from paddlelite.lite import *
import numpy as np
from PIL import Image
from torchvision import transforms

EXPORT_PADDLE_LITE = True  

cwd = os.getcwd()

transform = transforms.Compose([
            transforms.Resize(448),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                 std=[0.229, 0.224, 0.225])
            ])

def softmax(x):
    orig_shape = x.shape

    if len(x.shape) > 1:
        exp_minmax = lambda x: np.exp(x - np.max(x))
        denom = lambda x: 1.0 / np.sum(x)
        x = np.apply_along_axis(exp_minmax, 1, x)
        denominator = np.apply_along_axis(denom, 1, x)

        if len(denominator.shape) == 1:
            denominator = denominator.reshape((denominator.shape[0], 1))

        x = x * denominator
    else:
        x_max = np.max(x)
        x = x - x_max
        numerator = np.exp(x)
        denominator = 1.0 / np.sum(numerator)
        x = numerator.dot(denominator)

    assert x.shape == orig_shape
    return x

def numpy_max(lists):
    list_max_num = []
    list_max_idx = []
    for row in lists:
        maxNum = row[0]
        maxIdx = 0
        idx = 0
        for n in row:
            if n > maxNum:
                maxNum = n
                maxIdx = idx
            idx = idx + 1
        list_max_idx.append(maxIdx)
        list_max_num.append(maxNum)
    return list_max_num, list_max_idx

def Init(model_dir, threads=1):
    print("load model" + model_dir + " ...\n")
    config = MobileConfig()
    config.set_model_from_file(model_dir)
    print(config)
    predictor = create_paddle_predictor(config)
    print("load ok\n")
    return predictor

def parse_labels(labels_path):
    options = dict()
    with open(labels_path,'r') as f:
        lines = f.readlines()
    for line in lines:
        key, value = line.strip().split(' ')
        options[key.strip()] = value.strip()
    
    return options
# def prediect(img_dir):
#     model_dir = './model/opt.nb'
#     thread_num = 1
#     predictor = Init(model_dir, thread_num)

#     source_imgD = os.path.join(cwd, img_dir)
#     allTestDataName = []
#     for i in os.listdir(source_imgD):
#         allTestDataName.append(i)
#     allTestDataName.sort()
#     count = 0
#     for img_name in allTestDataName:
#         img_path = os.path.join(source_imgD, img_name)

#         img = Image.open(img_path)
#         img = transform(img).unsqueeze(0)
#         img = img.numpy()

#         input_tensor = predictor.get_input(0)
#         input_tensor.from_numpy(img)

#         predictor.run()

#         output_tensor = predictor.get_output(0)
#         outputs = output_tensor.numpy()
#         score = softmax(outputs)

#         _, predicted = numpy_max(outputs)
#         ret1, ret2 = numpy_max(score)  

#         with open("yumi_result", "a+") as f:
#             f.write("{} true label:4, classify label:{} score:{:.4f}\n".format(img_name, str(predicted[0]), ret1[0]))
#             if str(predicted[0]) == "4":
#                 count += 1
#     with open("yumi_result.txt", "a+") as f:
#         f.write("count:" + str(count) + "\n")

def predict(predictor, img_path):
    img = Image.open(img_path)
    img = transform(img).unsqueeze(0)
    img = img.numpy()

    input_tensor = predictor.get_input(0)
    input_tensor.from_numpy(img)

    predictor.run()

    output_tensor = predictor.get_output(0)
    outputs = output_tensor.numpy()
    score = softmax(outputs)

    _, predicted = numpy_max(outputs)
    ret1, ret2 = numpy_max(score)  

    return str(predicted[0])