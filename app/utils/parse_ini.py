import os
import configparser

class ParseIniFile(object):
    def __init__(self, ini_file_path):
        self.cf = configparser.ConfigParser()
        if os.path.isfile(ini_file_path):
            self.ini_file_path = ini_file_path
            self.cf.read(self.ini_file_path)
        else:
            self.ini_file_path = None

    def get_data(self, section, value):
        try:
            self.cf.read(self.ini_file_path) 
            res = self.cf.get(section, value)
        except:
            res = None
        return res

    def set_data(self, section, set_tuple):
        try:
            if self.cf.has_section(section):
                self.cf.set(section, set_tuple[0], set_tuple[1]) 
                self.cf.write(open(self.ini_file_path, 'w'))
            else:
                self.cf.add_section(section)
                self.cf.set(section, set_tuple[0], set_tuple[1]) 
                self.cf.write(open(self.ini_file_path, 'w'))
            res = True
        except:
            res = False
        return res

    def del_data(self,section):
        try:
            if self.cf.has_section(section):
                self.cf.remove_section(section)
                self.cf.write(open(self.ini_file_path, 'w'))
            res = True
        except:
            res = False
        return res
    