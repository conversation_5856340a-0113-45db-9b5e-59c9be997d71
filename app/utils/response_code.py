# coding:utf-8

class RET:
    OK                  = 2000
    RPNFTASK            = 1101
    RPNFFTPD            = 1102
    RPNFFTPU            = 1103
    RPNFSTAG            = 1104
    RPNFCOV             = 1105
    RPNFHEIGHT          = 1106
    RPNFDENSITY         = 1107
    RPNFDRY             = 1108
    RPNFLEAF            = 1109
    RPNFCOM             = 1110
    RPNFANIMAL          = 1111
    RPERRTASK           = 1201
    RPERRFTPD           = 1202
    RPERRFTPU           = 1203
    RPERRSTAG           = 1204
    RPERRCOV            = 1205
    RPERRHEIGHT         = 1206
    RPERRDENSITY        = 1207
    RPERRDRY            = 1208
    RPERRLEAF           = 1209
    RPERRCOM            = 1210
    RPERRANIMAL         = 1211
    FMIMG               = 2101
    FMSOUND             = 2102
    FMPC                = 2103
    FFIMG               = 2201
    FFSOUND             = 2202
    FFPC                = 2203
    FQIMG               = 2301
    FQSOUND             = 2302
    FQPC                = 2303
    SRMEM               = 3101
    SRDIS               = 3102
    SRMPU               = 3103
    SROVER              = 3104
    FRFTPD              = 4101
    FRFTPU              = 4102
    FRSTAG              = 4103
    FRCOV               = 4104
    FRHEIGHT            = 4105
    FRDENSITY           = 4106
    FRDRY               = 4107
    FRLEAF              = 4108
    FRCOM               = 4109
    FRANIMAL            = 4110
    FILEERR             = 2400
    GROWTHERR           = 4400
    TYPEERR             = 5000
    PCERR               = 6000
    SOUNDERR            = 7000
    STATICRUNERR        = 8000
    UPLOADPARAMS        = 9001
    UPLOADRUNERR        = 9002
    UPLOADMERGEERR      = 9003
    DWLOADPARAMS        = 9004
    DWLOADRUNERR        = 9005
    PROCESSPARAMSERR    = 9008
    PROCESSRUNERR       = 9009

msg_map = {
    RET.OK                  : "success",
    RET.RPNFTASK            : "task basic params not full",
    RET.RPNFFTPD            : "FTP upload params not full",
    RET.RPNFFTPU            : "FTP download params not full",
    RET.RPNFSTAG            : "plant stage params not full",
    RET.RPNFCOV             : "plant coverage params not full",
    RET.RPNFHEIGHT          : "plant height params not full",
    RET.RPNFDENSITY         : "plant density params not full",
    RET.RPNFDRY             : "plant drymatterweight params not full",
    RET.RPNFLEAF            : "plant leafareaofindex params not full",
    RET.RPNFCOM             : "community params not full",
    RET.RPNFANIMAL          : "animal params not full",
    RET.RPERRTASK           : "task basic params error",
    RET.RPERRFTPD           : "FTP upload params error",
    RET.RPERRFTPU           : "FTP download params error",
    RET.RPERRSTAG           : "plant stage params error",
    RET.RPERRCOV            : "plant coverage params error",
    RET.RPERRHEIGHT         : "plant height params error",
    RET.RPERRDENSITY        : "plant density params error",
    RET.RPERRDRY            : "plant drymatterweight params error",
    RET.RPERRLEAF           : "plant leafareaofindex params error",
    RET.RPERRCOM            : "community params error",
    RET.RPERRANIMAL         : "animal params error",
    RET.FMIMG               : "image file miss",
    RET.FMSOUND             : "sound file miss",
    RET.FMPC                : "pointcloud file miss",
    RET.FFIMG               : "image format error",
    RET.FFSOUND             : "sound format error",
    RET.FFPC                : "pointcloud format error",
    RET.FQIMG               : "image quality low",
    RET.FQSOUND             : "sound quality low",
    RET.FQPC                : "pointcloud quality low",
    RET.SRMEM               : "memory insufficient",
    RET.SRDIS               : "hard insufficient",
    RET.SRMPU               : "MPU insufficient",
    RET.SROVER              : "response overtime",
    RET.FRFTPD              : "FTP download exception",
    RET.FRFTPU              : "FTP upload exception",
    RET.FRSTAG              : "plant stage identify exception",
    RET.FRCOV               : "plant coverage identify exception",
    RET.FRHEIGHT            : "plant height identify exception",
    RET.FRDENSITY           : "plant density identify exception",
    RET.FRDRY               : "plant drymatterweight identify exception",
    RET.FRLEAF              : "plant leafareaofindex identify exception",
    RET.FRCOM               : "community generation exception",
    RET.FRANIMAL            : "animal identify exception",
    RET.FILEERR             : "file format error or file miss",
    RET.GROWTHERR           : "plant phenology identity exception",
    RET.TYPEERR             : "unsupported recognition type",
    RET.PCERR               : "pointcloud identify exception",
    RET.SOUNDERR            : "sound identify exception",
    RET.STATICRUNERR        : "static test model exception",
    RET.UPLOADPARAMS        : "upload params not full",
    RET.UPLOADRUNERR        : "upload running exception",
    RET.UPLOADMERGEERR      : "upload merge exception",
    RET.DWLOADPARAMS        : "download params not full",
    RET.DWLOADRUNERR        : "download run error",
    RET.PROCESSPARAMSERR    : "process params not full",
    RET.PROCESSRUNERR       : "process running exception"
}
