
import os
import numpy as np
import tarfile , zipfile, rarfile
from PIL import Image
from flask import current_app

def check_file(filename, ext):
    file_ext = filename.split('.')[-1]
    if isinstance(ext, list):
        if file_ext in ext:
            return True
        else:
            return False
    if isinstance(ext, str):      
        if file_ext == ext:
            return True
        else:
            return False

# 求取平均值
def average(value_list, th=2):
    """
    根据方差计算前一天的平均覆盖度
    :param arr: 前一天的覆盖度列表
    :th: 方差阈值，默认值即经验值
    :return avg: 前一天的平均覆盖度
    """
    if value_list:            
        # print(type(value_list[0]))
        v = np.var(value_list)
        if v <= th :
            avg = np.average(value_list)
            return avg
        else:
            if len(value_list) <= 2:
                avg = np.average(value_list)
            else:
                value_list.remove(max(value_list))
                value_list.remove(min(value_list))
                avg = np.average(value_list)
            return avg
    else:
        return None
        
#图片完整性判断
def isvalidimage(pathfile):
    bvalid = True
    try:
        Image.open(pathfile).verify()
    except:
        bvalid = False
    return bvalid

# 文件进行zip压缩
def zip_compress(data_dir, token):
    output_name = f"{token}.zip"
    zip_file_new = os.path.join('app/static/results', output_name)
    if os.path.exists(data_dir):
        current_app.logger.info('正在为您压缩...')
        # 压缩后的名字
        zip = zipfile.ZipFile(zip_file_new, 'w', zipfile.ZIP_DEFLATED)
        for dir_path, dir_names, file_names in os.walk(data_dir):
            # 去掉目标跟路径，只对目标文件夹下面的文件及文件夹进行压缩
            fpath = dir_path.replace(data_dir, '')
            for filename in file_names:
                zip.write(os.path.join(dir_path, filename), os.path.join(fpath, filename))
        zip.close()
        current_app.logger.info('该目录压缩成功...')
    else:
        current_app.logger.info('您要压缩的目录不存在...')

# 对文件解压
def uncompress(src_file, dest_dir):
    filefmt = src_file.split('/')[-1].split('.')[-1]
    if filefmt in ('tgz', 'tar') :
        try :
            tar = tarfile.open(src_file)
            names = tar.getnames()
            for name in names:
                tar.extract(name, dest_dir)
            tar.close()
        except Exception as e :
            return (False, e, filefmt)
    elif filefmt == 'zip':
        try :
            zip_file = zipfile.ZipFile(src_file)
            for names in zip_file.namelist():
                zip_file.extract(names, dest_dir)
            zip_file.close()
        except Exception as e :
            return (False, e, filefmt)
    elif filefmt == 'rar' :
        try :
            rar = rarfile.RarFile(src_file)
            os.chdir(dest_dir)
            rar.extractall()
            rar.close()
        except Exception as e :
            return (False, e, filefmt)
    else :
        return (False, '文件格式不支持或者不是压缩文件', filefmt)
    return (True, '', filefmt)

# 递归删除文件和文件夹
def deldir(dir):
    if not os.path.exists(dir):
        return False
    if os.path.isfile(dir):
        os.remove(dir)
        return True
    for i in os.listdir(dir):
        t = os.path.join(dir, i)
        if os.path.isdir(t):
            deldir(t)#重新调用次方法
        else:
            os.unlink(t)
    #递归删除目录下面的空文件夹
    os.removedirs(dir)


def del_file(path_data):
    for i in os.listdir(path_data) :# os.listdir(path_data)#返回一个列表，里面是当前目录下面的所有东西的相对路径
        file_data = file_data = os.path.join(path_data, i)
        if os.path.isfile(file_data) == True:#os.path.isfile判断是否为文件,如果是文件,就删除.如果是文件夹.递归给del_file.
            os.remove(file_data)
        else:
            del_file(file_data)
# 获取文件信息
def get_file_info(filename):
    ext = '.' + filename.split('.')[1]
    info = filename.split('.')[0].split('_')
    num = len(info)
    tz, year, month, day, h, m, s, jw = ('', '', '', '', '', '', '', '')

    if ext == '.jpg':
        tz = info[0]
        if len(info) == 4:
            year = info[2][0:4]
            month = info[2][4:6]
            day = info[2][6:8]
            h = info[2][8:10]
            m = info[2][10:12]
            s = info[2][12:14]
            jw = info[-1]
        elif len(info) == 6:
            year = info[4][0:4]
            month = info[4][4:6]
            day = info[4][6:8]
            h = info[4][8:10]
            m = info[4][10:12]
            s = info[4][12:14]
            jw = info[2]
        else:
            year = info[3][0:4]
            month = info[3][4:6]
            day = info[3][6:8]
            h = info[3][8:10]
            m = info[3][10:12]
            s = info[3][12:14]
            jw = info[2]

    elif ext == '.xml':
        tz = info[3] if len(info) == 7 else info[0]
        year = info[4][0:4] if len(info) == 7 else info[2][0:4]
        month = info[4][4:6] if len(info) == 7 else info[2][4:6]
        day = info[4][6:8] if len(info) == 7 else info[2][6:8]
        h = info[4][8:10] if len(info) == 7 else info[2][8:10]
        m = info[4][10:12] if len(info) == 7 else info[2][10:12]
        s = info[4][12:14] if len(info) == 7 else info[2][12:14]

    elif ext == '.tgz':
        tz = info[0]
        year = info[2][0:4]
        month = info[2][4:6]
        day = info[2][6:8]
        h = info[2][8:10]
        m = info[2][10:12]
        s = info[2][12:14]


    elif ext == '.txt':
        tz = info[0]
        year = info[2][0:4]
        month = info[2][4:6]
        day = info[2][6:8]
        h = info[2][8:10]
        m = info[2][10:12]
        s = info[2][12:14]

    elif ext == '.csv':
        tz = info[1]
        year = info[2][0:4]
        month = info[2][4:6]
        day = info[2][6:8]
        h = info[2][8:10]
        m = info[2][10:12]
        s = info[2][12:14]


    elif ext == '.DAT':
        tz = info[1]
        year = info[2][0:4]
        month = info[2][4:6]
        day = info[2][6:8]
        h = info[2][8:10]
        m = info[2][10:12]
        s = info[2][12:14]

    elif ext == '.mp3' or ext == '.wav':
        tz = info[0]
        year = info[1][0:4]
        month = info[1][4:6]
        day = info[1][6:8]
        h = info[1][8:10]
        m = info[1][10:12]
        s = info[1][12:14]
    
    elif ext == '.ogg':
        tz = info[0]
        year = info[1][0:4]
        month = info[1][4:6]
        day = info[1][6:8]
        h = info[1][8:10]
        m = info[1][10:12]
        s = info[1][12:14]
    
    return (tz, year, month, day, h, m, s, jw, ext, num)