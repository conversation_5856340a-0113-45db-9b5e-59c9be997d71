"""
FTP常用操作
"""
from ftplib import FTP
import os
class FTP_OP(object):
  def __init__(self, host, username, password, port):
    """
    初始化ftp
  :param host: ftp主机ip
  :param username: ftp用户名
  :param password: ftp密码
  :param port: ftp端口 
  """
    self.host = host
    self.username = username
    self.password = password
    self.port = port
    print("FTP初始化成功!")
  def ftp_connect(self):
    """
    连接ftp
    :return:
    """
    ftp = FTP(timeout=10)
    # print("初始化成功！")
    # ftp.set_debuglevel(1) # 不开启调试模式
    ftp.connect(host=self.host, port=self.port) # 连接ftp
    ftp.login(self.username, self.password) # 登录ftp
    # print("FTP登录成功!")
    # ftp.set_pasv(False)##ftp有主动 被动模式 需要调整 
    return ftp
  def download_file(self, ftp_file_path, dst_file_path):
    """
    从ftp下载文件到本地
    :param ftp_file_path: ftp下载文件路径
    :param dst_file_path: 本地存放路径
    :return:
    """
    buffer_size = 102400 #默认是8192
    ftp = self.ftp_connect()
    print(ftp.getwelcome() ) #显示登录ftp信息
    print("开始下载文件！")
    #获取目录下的文件
    # file_list = ftp.nlst(ftp_file_path)
    # ftp.cwd(dst_file_path)#进入ftp待下载路径
    print(ftp_file_path)
    if not os.path.exists(dst_file_path):
      with open(dst_file_path, "wb") as f:
        ftp.retrbinary('RETR %s' % ftp_file_path, f.write, buffer_size)
        f.close()
    ftp.quit()
  def upload_file(self, ftp_file_path, dst_file_path):
    """
    从本地上传到FTP
    :param ftp_file_path: ftp上传文件路径
    :param dst_file_path: 本地存放路径
    :return:
    """
    buffer_size = 102400 #默认是8192
    ftp = self.ftp_connect()
    print(ftp.getwelcome() ) #显示登录ftp信息
    # try:
    #     ftp.cwd(ftp_file_path)
    # except error_perm:
    #     ftp.mkd(ftp_file_path)
    print(ftp_file_path)
    fp=open(dst_file_path, "rb")
    ftp.storbinary('STOR {}'.format(ftp_file_path), fp, buffer_size)
    ftp.quit()
  def get_filelist(self,ftp_path):
    """
    获取ftp路径下文件列表
    :param ftp_file_path: ftp下载文件路径
    :return:
    """
    ftp = self.ftp_connect()
    # 切换到指定的工作目录
    ftp.cwd(ftp_path)
    # 获取当前目录中的文件列表
    file_list = ftp.nlst()
    ftp.quit()
    return file_list
