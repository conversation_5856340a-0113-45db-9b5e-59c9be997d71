# coding:utf-8

from flask import Flask
from flask_caching import Cache
from flask_executor import Executor
import logging
from logging.handlers import RotatingFileHandler
from config import config_map

# 缓存模块实例化
cache = Cache()
# 异步执行模块实例化
executor = Executor()

# 导入异常处理
try:
    from app.core.exceptions import register_error_handlers
    ENHANCED_EXCEPTIONS = True
except ImportError:
    ENHANCED_EXCEPTIONS = False
    def register_error_handlers(app):
        """空的错误处理注册函数"""
        pass

# 配置日志信息
logging.basicConfig(level=logging.INFO)

# 确保日志路径相对于项目根目录
import os
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
log_dir = os.path.join(project_root, "logs")
os.makedirs(log_dir, exist_ok=True)
log_file = os.path.join(log_dir, "log")

file_log_handler = RotatingFileHandler(log_file, maxBytes=1024*1024*100, backupCount=10)
formatter = logging.Formatter('%(levelname)s %(filename)s:%(lineno)d %(message)s')
file_log_handler.setFormatter(formatter)
logging.getLogger().addHandler(file_log_handler)

# 禁止第三方库的日志输出
logging.getLogger('matplotlib.font_manager').disabled = True
logging.getLogger('PIL.TiffImagePlugin').disabled = True
logging.getLogger('numba.core.ssa').disabled = True
logging.getLogger('numba.core.interpreter').disabled = True
logging.getLogger('numba.core.byteflow').disabled = True
logging.getLogger('pydub.converter').disabled = True


def create_app(config_name):
    """
    创建flask的应用对象
    :param config_name: str  配置模式的模式的名字 （"develop",  "product"）
    :return:
    """
    app = Flask(__name__)

    # 根据配置模式的名字获取配置参数的类
    config_class = config_map.get(config_name)
    app.config.from_object(config_class)

    # 初始化缓存模块
    cache.init_app(app)
    # 初始化异步模块
    executor.init_app(app)

    # 注册增强的异常处理（如果可用）
    if ENHANCED_EXCEPTIONS:
        register_error_handlers(app)

    # 注册蓝图
    from app import api_v1_0
    app.register_blueprint(api_v1_0.api, url_prefix='/v1')

    return app