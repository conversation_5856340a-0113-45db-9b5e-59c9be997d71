import os
import json
import pandas as pd
from coverage import brt_cov
import time
import shutil

#图像文件批量重命名
def rename(filePath):
    """
    param filePath: 文件夹的路径
    """
    labelpath="/home/<USER>/web_server/app/tasks/coverage/label.csv"
    label_data=pd.read_csv(labelpath)
    crop_number={"yumi":"010405","mianhua":"010502","shuidao":"010201","xiaomai":"010302"}
    for root, dirs, files in os.walk(filePath):
        # for循环遍历文件名字
        for fileName in files:
            if fileName.split('.')[1]=="jpg":
                tz,code,date,jw=fileName.split(".")[0].split("_")
                currow=label_data[label_data["name"]==fileName]
                curcode=crop_number[currow["crop"].iloc[0]]
                file="WH001_{}_{}_{}.jpg".format(curcode,date,jw)
                os.rename(os.path.join(root, fileName), os.path.join(root, file))                    
                print(file)
                # os.__exit__()

#按照csv内容将图像文件集成到同一文件夹中
def copyfile(filePath,filelistpath):
    zw_list=["yumi","mianhua","shuidao","xiaomai"]
    file_data=pd.read_csv(filelistpath)
    file_list=file_data["filename"].tolist()
    outpath="/home/<USER>/web_server/app/tasks/coverage/image_v2"
    for crop in zw_list:
        crop_path = os.path.join(filePath, crop)
        for root, dirs, files in os.walk(crop_path):
            for file in files:
                if file in file_list:
                    path = os.path.join(crop_path, file)
                    print(path)
                    shutil.copy(path, outpath)

def label_filename(filePath):
    file_data=pd.read_csv(filePath)
    crop_number={"yumi":"010405","mianhua":"010502","shuidao":"010201","xiaomai":"010302"}
    for index, row in file_data.iterrows():
        prename=file_data.at[index, 'name']
        tz,code,date,jw=prename.split(".")[0].split("_")
        curcode=crop_number[file_data.at[index, 'crop']]
        file="WH001_{}_{}_{}.jpg".format(curcode,date,jw)
        file_data.at[index, 'name']=file
    file_data.to_csv("label_v1.csv")


zw_list=["yumi","mianhua","shuidao","xiaomai"]
image_path="/home/<USER>/web_server/app/tasks/coverage/v2"
# image_path="/home/<USER>/web_server/app/tasks/coverage/image"
# rename(image_path)
# filelistpath="/home/<USER>/web_server/app/tasks/coverage/coverage_v2.csv"
# copyfile(image_path,filelistpath)
# os.__exit__()
csvpath="/home/<USER>/web_server/app/tasks/coverage/label_v1.csv"
# label_filename(csvpath)
# os.__exit__()
# data_result=pd.DataFrame(columns=["crop","filename","label","test","gztest","time"])
data_result=pd.DataFrame(columns=["filename","label","test","re","<=10"])
# data_result=pd.read_csv("ym_coverage.csv")
gz_data=pd.read_csv(csvpath)
pro_file="/home/<USER>/web_server/app/tasks/coverage/coverage_v1.csv"
file_data=pd.read_csv(pro_file)
un_file_list=file_data["filename"].tolist()

for root, dirs, files in os.walk(image_path):
    for file in files:
        if file.split('.')[1]=="jpg":
            path = os.path.join(image_path, file)
            print(path)
            #获取该图像文件的标准测试数据
            currow=gz_data[gz_data["name"]==file]
            val, avg, coverage = brt_cov(1,path)
            curlabel=currow["label"].iloc[0]
            curre=abs(coverage-curlabel)/curlabel*100
            curres="TRUE" if curre<=10 else "FALSE"
            data_result.loc[len(data_result)] = [file,curlabel,coverage,curre,curres]
            # print(data_result)
            # os.__exit__()
# os.__exit__()
# 

# for root, dirs, files in os.walk(image_path):
#         for file in files:
#             if file.split('.')[1]=="jpg":
#                 path = os.path.join(image_path, file)
#                 print(path)
#                 #获取该图像文件的标准测试数据
#                 currow=gz_data[gz_data["name"]==file]
#                 val, avg, coverage = brt_cov(1,path)
#                 curlabel=currow["label"].iloc[0]
#                 curre=abs(coverage-curlabel)/curlabel*100
#                 curres="TRUE" if curre<=10 else "FALSE"
#                 data_result.loc[len(data_result)] = [file,curlabel,coverage,curre,curres]
                # print(data_result)
                
                # os.__exit__()
# for crop in zw_list:
#     crop_path = os.path.join(image_path, crop)
#     csvpath=os.path.join(crop_path, "data.csv")
#     gz_data=pd.read_csv(csvpath)
#     for root, dirs, files in os.walk(crop_path):
#         for file in files:
#             if file.split('.')[1]=="jpg":
#                 path = os.path.join(crop_path, file)
#                 print(path)
#                 #获取该图像文件的标准测试数据
#                 currow=gz_data[gz_data["name"]==file]
#                 start_time=time.time()
#                 val, avg, coverage = brt_cov(1,path)
#                 end_time=time.time()
#                 data_result.loc[len(data_result)] = [crop,file,currow["label"].iloc[0],coverage,currow["gztest"].iloc[0],end_time-start_time]

# data_result.loc[data_result[data_result["filename"]==file].index,"cv_test"]=coverage
# data_result.loc[data_result[data_result["filename"]==file].index,"cv_time"]=end_time-start_time


data_result.to_csv("coverage_newname_v2.csv")
# os.__exit__()
