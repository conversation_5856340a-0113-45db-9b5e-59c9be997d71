####################################################
# 本程序的功能： 覆盖度检测
# 版本：V1
####################################################
# coding=utf-8
import os
import numpy as np
from skimage import measure
from skimage import img_as_ubyte
import cv2
import time


"""水稻的k值为0，其余为1，测试结果水稻也取1效果更好
   当前的覆盖度值为计算出来的覆盖度值乘修正系数1.1
   此函数返回值共三个，val 为有效值，avg为图片的亮度值，coverage为覆盖度值"""
def brt_cov(k, readpath):
    time1=time.time()
    size = os.path.getsize(readpath)
    img = cv2.imread(readpath)
    if size == 0:
        log = ("log.txt")
        val = 0
        with open(log, "a+") as f:
            f.write(readpath + "\n")
        return val, 0, 0
    height = img.shape[0]
    width = img.shape[1]
    time2=time.time()
    # print("覆盖度读取文件：",time2-time1)
    HSV = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    B_Channel,G_Channel,R_Channel = cv2.split(img)

    """HSV空间下青色分量范围"""
    if k == 1:
        LowerQing = np.array([26, 43, 46])       #else
    elif k == 0:
        LowerQing = np.array([78, 43, 46])        #水稻

    UpperQing = np.array([99, 255, 255])
    H_Channel, S_Channel, V_Channel = cv2.split(HSV)
    mask = cv2.inRange(HSV, LowerQing, UpperQing)
    QingThings = cv2.bitwise_and(img, img, mask=mask)
    BGRQing = cv2.cvtColor(QingThings, cv2.COLOR_HSV2BGR)
    GRAYQing = cv2.cvtColor(BGRQing, cv2.COLOR_BGR2GRAY)
    _, thresholdQing = cv2.threshold(GRAYQing, 10, 255, cv2.THRESH_BINARY)
    time3=time.time()
    # print("HSV空间下操作：",time3-time2)
    """颜色指数法进行图像分割"""
    image_exg_exr = G_Channel * 0.3 - R_Channel * 0.24 - B_Channel * 0.1
    image_exg_exr[image_exg_exr < 0] = 0
    image_exg_exr[image_exg_exr > 25.5] = 25.5
    image_exg_exr = image_exg_exr * 10
    # print(image_exg_exr)
    # image_exg_exr = image_exg_exr.astype(np.uint8)
    # print(image_exg_exr)
    """计算亮度"""
    avg = np.mean(V_Channel)

    ret, threshold = cv2.threshold(image_exg_exr, 10, 255, cv2.THRESH_BINARY)
    threshold = threshold + thresholdQing
    time4=time.time()
    # print("图像分割：",time4-time3)
    """筛除连通区域小于threshold的"""
    label = measure.label(threshold, connectivity=1)
    properties = measure.regionprops(label)
    valid_label = set()
    for prop in properties:
        if prop.area > 100:
            valid_label.add(prop.label)
    #时间复杂度改进
    # retval, label, stats, centroids = cv2.connectedComponentsWithStats(threshold.astype(np.uint8), connectivity=4)
    # print(retval)
    # valid_label=set()
    # for index, stat in enumerate(stats):
    #     if index==0:#这一句话很重要！！！！！！
    #         continue
    #     x,y,cw,ch,area = stat
    #     if area>100:
    #         valid_label.add(index)
    time5=time.time()
    # print("筛选连通域：",time5-time4)
    current_bw = np.in1d(label, list(valid_label)).reshape(label.shape)

    """膨胀操作"""
    kernel = np.ones((2,2),np.uint8)
    current_bw = img_as_ubyte(current_bw)
    thresh = cv2.dilate(current_bw, kernel, iterations=1)

    """计算覆盖度"""
    result = np.sum(thresh > 100)
    totalNumber = height * width
    # print(height,width)
    coverage = 1.1*(result * 100 / totalNumber)
    if coverage>=100:
        coverage = 98.27
    val = 1
    time6=time.time()
    # print("计算覆盖度：",coverage)
    return val, avg, coverage



if __name__ == '__main__':
    # img_dir = r"D:\XQX\dailywork\Coding\xqx_shidian_v2\src\CropDetect\mianhua-density-ceshi\IMG-yumi\2"
    # save_dir = r"D:\XQX\dailywork\Coding\xqx_shidian_v2\src\CropDetect\mianhua-density-ceshi\binary10"
    img_path="/home/<USER>/web_server/app/tasks/coverage/D1706_010405_01_20200825120000_HADING.jpg"
    # for i in os.listdir(img_dir):
    #     img_path = os.path.join(img_dir, i)
    #     save_path = os.path.join(save_dir,i.split(".")[0] + "binary.jpg")
    val, avg, coverage = brt_cov(1,img_path)
    #     print(i)
    print(coverage)
    print("覆盖度模块加载完毕！")