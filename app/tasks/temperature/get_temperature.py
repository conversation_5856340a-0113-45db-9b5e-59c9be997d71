from app.utils.parse_ini import ParseIniFile
from app.utils.ftp import FTP_OP
from app.utils.commons import del_file
import os
import  xml.dom.minidom
from datetime import datetime

# 加载配置文件
pif = ParseIniFile('app/config.ini')

#获取规定时间区间内xml文件的气象数据
#参数：开始时间字符串、结束时间字符串、时间粒度字符穿（day\hour\minute）、待查询参数
def get_temperature(start_time_str,end_time_str,quantum,param):
    """
    :start_time_str,end_time_str: 获得该闭区间内的xml文件信息,字符串格式“%Y%m%d”
    :return:
    """
    cur_host=pif.get_data("settings","t_ip")
    cut_user=pif.get_data("settings","t_name")
    cut_pwd=pif.get_data("settings","t_pwd")
    cut_port=pif.get_data("settings","t_port")
    cur_ftp = FTP_OP(cur_host, cut_user, cut_pwd,int(cut_port))
    cur_url_path=pif.get_data("settings","t_url_path")
    # FTP文件在服务器端的存储路径
    static_dir = pif.get_data("common", "static_dir")
    dst_file_path = os.path.join(static_dir, 'ftp/xml')
    file_list=cur_ftp.get_filelist(cur_url_path)
    res_list={}
    if quantum=="day":
        datetime_str="%Y%m%d"
        datetime_strnum=8
    elif quantum=="hour":
        datetime_str="%Y%m%d%H"
        datetime_strnum=10
    elif quantum=="minute":
        datetime_str="%Y%m%d%H%M"
        datetime_strnum=12
    elif quantum=="second":
        datetime_str="%Y%m%d%H%M%S"
        datetime_strnum=14
    # 筛选符合时间条件的xml文件
    start_time,end_time = datetime.strptime(start_time_str, datetime_str),datetime.strptime(end_time_str, datetime_str)
    print(start_time,end_time)
    for file in file_list:
        if len(file.split("_"))==7:
            print(file.split("_")[4],len(file.split("_")[4]))
            cur_time_str=file.split("_")[4][:datetime_strnum]
            cur_time=datetime.strptime(cur_time_str, datetime_str)
            print(cur_time)
            if cur_time>=start_time and cur_time<=end_time:
                dst_file = os.path.join(dst_file_path, file)
                cur_url=os.path.join(cur_url_path, file)
                cur_ftp.download_file(cur_url, dst_file)
                #读取下载成功的xml文件
                dom = xml.dom.minidom.parse(dst_file)
                root = dom.documentElement
                param_list=dom.getElementsByTagName(param)
                param_item=param_list[0]
                res_list[cur_time_str]=param_item.firstChild.data
    print(res_list)
    del_file(dst_file_path)
    return res_list

#通过xml文件路径直接获取气象参数
def get_temperature_v1(param):
    cur_host=pif.get_data("settings","t_ip")
    cut_user=pif.get_data("settings","t_name")
    cut_pwd=pif.get_data("settings","t_pwd")
    cut_port=pif.get_data("settings","t_port")
    cur_ftp = FTP_OP(cur_host, cut_user, cut_pwd,int(cut_port))
    cur_url=pif.get_data("settings","t_url")
    file_name = cur_url.split("/")[-1]
    # FTP文件在服务器端的存储路径
    static_dir = pif.get_data("common", "static_dir")
    dst_file_path = os.path.join(static_dir, 'ftp/xml')
    dst_file = os.path.join(dst_file_path, file_name)
    cur_ftp.download_file(cur_url, dst_file)
    #读取下载成功的xml文件
    dom = xml.dom.minidom.parse(dst_file)
    root = dom.documentElement
    param_list=dom.getElementsByTagName(param)
    param_item=param_list[0]
    print(param_item.firstChild.data)
    del_file(dst_file_path)
    return param_item.firstChild.data
    
    



    