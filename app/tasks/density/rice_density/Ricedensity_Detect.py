# -*-coding:utf-8-*-
from __future__ import division
import os
import time
import numpy as np
import math
import cv2
import datetime
from app.tasks.density.rice_density.utils import loadMatDate, saveMatData
from app.tasks.density.rice_density.RiceGetDateFromImgName import RiceGetDateFromImgName
from app.tasks.density.rice_density.DensityDetect import DensityDetect
from app.tasks.density.rice_density.GetDateFromImgName import GetDateFromImgName
import logging
import scipy.io
path="app/tasks/density/rice_density/"


def get_dict(key, value):
    return {key: value}

class Gloabals:
    def __init__(self):
        self.TransplantImgName = ""
        self.RiceCanGetCoverage = -1
        self.CoverageTempResult = \
            scipy.io.loadmat("{}CoverageTempResult.mat".format(path))["CoverageTempResult"]
        self.RiceCoverageTempResult = \
            scipy.io.loadmat("{}RiceCoverageTempResult.mat".format(path))["RiceCoverageTempResult"]
        self.RiceDensityDetectNumhist = \
            scipy.io.loadmat("{}RiceDensityDetectNumhist.mat".format(path))["RiceDensityDetectNumhist"]
        self.RiceNum = 0
        self.EstablishmentImgName = ""
        self.AverageCoverageForEstablishment = -1
        self.density = 0

        self.current_stage = None
        self.current_time = time.time()


def Ricedensity_Detect(img_Name, CurrentPeriodFlag, days, RiceperivousAverageCoverage, area,Image_last, riceTilleringRate):
    imgname = os.path.basename(img_Name)
    imgname = os.path.splitext(imgname)[0]
    year, month, day = GetDateFromImgName(imgname)

    globals = Gloabals()
    area = 21.1
    RiceCurrentPeriodFlag = CurrentPeriodFlag
    RiceNum = 0
    if RiceCurrentPeriodFlag == -1:
        RiceNum = -1
    elif RiceCurrentPeriodFlag >= 31:  # 此处得到globals.RiceDensityDetectNum值
        RiceNum = DensityDetect(Image_last.astype(np.int8))
        RiceDensityDetectNumhist = globals.RiceDensityDetectNumhist
        a = np.where(RiceDensityDetectNumhist >= 0)[0]
        RiceDensityDetectNumhist[len(a)] = RiceNum
        a = np.where(RiceDensityDetectNumhist >= 0)[0]
        SumRiceNum = 0
        for i in range(len(a)):
            SumRiceNum = SumRiceNum + RiceDensityDetectNumhist[a[i]]
        RiceNum = SumRiceNum / len(a)
        globals.RiceNum = RiceNum
        globals.RiceDensityDetectNumhist = RiceDensityDetectNumhist

    RiceDensityDetectNum = RiceNum
    globals.RiceDensityDetectNum = RiceDensityDetectNum
    RateNum = riceTilleringRate

    """
    area 需确认计算方式？？
    """

    if RiceNum > 400:
        area = 52.7
    elif RiceNum > 30 and RiceNum <= 400:
        area = 21.1
    elif RiceNum <= 30:
        area = 10.1

    if RiceCurrentPeriodFlag >= 41:  # 此处得到 globals.AverageCoverageForEstablishment值
        RiceCoverageTempResult = globals.RiceCoverageTempResult
        EstablishmentImgName = globals.EstablishmentImgName
        if len(EstablishmentImgName) != 0:
            month, day = RiceGetDateFromImgName(EstablishmentImgName)
        if len(np.where(RiceCoverageTempResult[:, 0] == month * 100 + day)) == 0:
            numd = 0
            sumd = 0
            r, c = np.where(RiceCoverageTempResult[:, 0] == month * 100 + day)
            for i in range(1, 45):
                if RiceCoverageTempResult[r, i] != -1:
                    numd = numd + 1
                    sumd = RiceCoverageTempResult[r, i] + sumd
            AverageCoverageForEstablishment = sumd / numd
            globals.AverageCoverageForEstablishment = AverageCoverageForEstablishment
    RiceNum = RiceDensityDetectNum

    if RiceCurrentPeriodFlag == -1:
        density = -1
    elif RiceCurrentPeriodFlag <= 41 and RiceCurrentPeriodFlag > 0:
        density = (RiceNum * 4) / area
    elif RiceCurrentPeriodFlag == 51:
        AverageCoverageForEstablishment = globals.AverageCoverageForEstablishment
        if AverageCoverageForEstablishment != -1:
            density = ((RiceNum * 4) + (RiceNum * 4) * (
                        RiceperivousAverageCoverage - AverageCoverageForEstablishment) * (RateNum - 1) / (
                                   0.90 - AverageCoverageForEstablishment)) / area
        else:
            density = ((RiceNum * 4) + (RiceNum * 4) * (RiceperivousAverageCoverage - 0.2) * (RateNum - 1) / (
                        0.90 - 0.2)) / area

    elif RiceCurrentPeriodFlag == 52 and days == 1:  # 拔节第一天
        AverageCoverageForEstablishment = globals.AverageCoverageForEstablishment
        if AverageCoverageForEstablishment != -1:
            density = ((RiceNum * 4) + (RiceNum * 4) * (
                        RiceperivousAverageCoverage - AverageCoverageForEstablishment) * (RateNum - 1) / (
                                   0.90 - AverageCoverageForEstablishment)) / area
        else:
            density = ((RiceNum * 4) + (RiceNum * 4) * (RiceperivousAverageCoverage - 0.2) * (RateNum - 1) / (
                        0.90 - 0.2)) / area
    else:
        density = globals.density

    globals.density = density

    if density >= 0:
        Ricedensity = density * 3
    else:
        Ricedensity = -1

    return Ricedensity