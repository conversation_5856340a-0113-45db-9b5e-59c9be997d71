#-*-coding:utf-8-*-
from __future__ import division
import os
import math
import cv2
import scipy.io
import numpy as np
from app.tasks.density.rice_density.utils import loadMatDate, saveMatData
from skimage import color
from skimage import measure

def ImageWipeArea(Image, AreaThreshold):

    IO = Image
    I = IO
    I = I.astype(np.uint8)
    if len(I.shape) > 2:
        I = cv2.cvtColor(I, cv2.COLOR_BGR2GRAY)
    else:
        I = I

    #thresh, bw = cv2.threshold(I, 0, 255, cv2.THRESH_OTSU)
    bw = (I <= 0.95) * 1.0

    L = measure.label(bw, connectivity=2)
    stats = measure.regionprops(L)
    Ar = np.array([x.area for x in stats])
    labels = np.zeros(I.shape)
    ind = np.where(Ar>AreaThreshold)
    a_row = len(ind)
    for i in range(a_row):
        labels[L == (ind[i]+1)] = 1
    Image_last = labels

    return Image_last

