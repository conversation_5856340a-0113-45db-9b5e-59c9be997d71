# coding:utf-8
from __future__ import division
import numpy as np
import cv2
from sklearn.cluster import KMeans
from app.tasks.density.rice_density.utils import loadMatDate
from skimage import color, measure
from app.tasks.density.rice_density.rgb2hsi import rgb2hsi


def ricesegmentation(ImageData):
    meanH = loadMatDate("meanH.mat", "meanH")
    devH = loadMatDate("devH.mat", "devH")

    standardH = np.sqrt(devH)

    new_size = (600,800)

    image_orig = cv2.cvtColor(ImageData, cv2.COLOR_BGR2RGB)
    # 慢////////////////////////////56s/////////////////////////////////
    Image = cv2.resize(image_orig, new_size)

    image_orig = Image
    num = image_orig.shape
    # Kmean分类
    image_orig = image_orig.astype(np.uint8)
    lab_he = cv2.cvtColor(image_orig, cv2.COLOR_RGB2LAB)
    ab = np.double(lab_he[:,:,1:3])
    nrows, ncols = ab.shape[:2]
    ab = ab.reshape(int(nrows*ncols),2)
    nColors = 5
    # 慢//17s///////////////////////////////////////////////////////////
    # cluster_idx = KMeans(n_clusters=nColors, max_iter=38,n_init=40, init='k-means++', n_jobs=-1).fit(ab)
    cluster_idx = KMeans(n_clusters=nColors, max_iter=38,n_init=40, init='k-means++').fit(ab)
    
    cluster__center = cluster_idx.cluster_centers_

    pixel_labels = cluster_idx.labels_.reshape(nrows, ncols)

    numberOfclass1 = np.sum(pixel_labels == 1)
    numberOfclass2 = np.sum(pixel_labels == 2)
    numberOfclass3 = np.sum(pixel_labels == 3)
    numberOfclass4 = np.sum(pixel_labels == 4)
    numberOfclass5 = np.sum(pixel_labels == 0)
    numberOfclass6 = np.sum(pixel_labels == 5)

    resizeImage = image_orig

    row,col,depth = resizeImage.shape

    YUV = color.rgb2ycbcr(resizeImage)
    YUV = YUV.astype(np.int32)
    HSI = rgb2hsi(resizeImage)

    areOfcrop = 0

    HSI0 = HSI[:, :,0].reshape(nrows, ncols, 1)
    HSII = HSI[:, :, 1].reshape(nrows, ncols, 1)
    meanHH = (meanH[YUV[:, :, 0]].reshape(nrows, ncols, 1))
    standardHH = standardH[YUV[:, :, 0]].reshape(nrows, ncols, 1)
    YUVV = YUV[:, :, 0].reshape(nrows, ncols, 1)
    resizeImage = (np.abs(HSI0 - meanHH) > np.multiply(1.9,standardH[YUV[:, :, 0]])) | (HSII < 0.2) | (YUVV >150)
    row, col, depth = resizeImage.shape
    resizeImage = resizeImage.astype(np.uint8)

    resizeImage = np.multiply(resizeImage,255)
    coincidentArea1 = 0
    coincidentArea2 = 0
    coincidentArea3 = 0
    coincidentArea4 = 0
    coincidentArea5 = 0
    coincidentArea6 = 0
    # 统计第一区间
    rowOfpixel11,colOfpixel11 = np.where(pixel_labels == 1)
    row = rowOfpixel11.shape
    rowOfpixel1 = rowOfpixel11.reshape(row[0],1)
    row = colOfpixel11.shape
    colOfpixel1 = colOfpixel11.reshape(row[0], 1)
    # 慢//////////////2s估计////////////////////////////////////////////////////////////////
    for i in range(numberOfclass1):
        if resizeImage[rowOfpixel1[i], colOfpixel1[i], 0] == 0:
            coincidentArea1 = coincidentArea1 + 1

    percentOfcoincidentArea1 = (1.0*coincidentArea1) / numberOfclass1

    # 统计第二类区间
    rowOfpixel11,colOfpixel11 = np.where(pixel_labels == 2)
    row = rowOfpixel11.shape
    rowOfpixel2 = rowOfpixel11.reshape(row[0], 1)
    row = colOfpixel11.shape
    colOfpixel2 = colOfpixel11.reshape(row[0], 1)
    for i in range(numberOfclass2):
        if resizeImage[rowOfpixel2[i,0],colOfpixel2[i,0],0] == 0:
            coincidentArea2 = coincidentArea2 + 1
    percentOfcoincidentArea2 = (1.0*coincidentArea2) / numberOfclass2

    #统计第三空间
    rowOfpixel11,colOfpixel11 = np.where(pixel_labels == 3)
    row = rowOfpixel11.shape
    rowOfpixel3 = rowOfpixel11.reshape(row[0], 1)
    row = colOfpixel11.shape
    colOfpixel3 = colOfpixel11.reshape(row[0], 1)
    for i in range(numberOfclass3):
        if resizeImage[rowOfpixel3[i,0],colOfpixel3[i,0],0] == 0:
            coincidentArea3 = coincidentArea3 + 1
    percentOfcoincidentArea3 = (1.0*coincidentArea3) / numberOfclass3

    #统计第四空间
    rowOfpixel,colOfpixel = np.where(pixel_labels == 4)
    row = rowOfpixel.shape
    rowOfpixel4 = rowOfpixel.reshape(row[0], 1)
    row = colOfpixel.shape
    colOfpixel4 = colOfpixel.reshape(row[0], 1)
    for i in range(numberOfclass4):
        if resizeImage[rowOfpixel4[i,0],colOfpixel4[i,0],0] == 0:
            coincidentArea4 = coincidentArea4 + 1
    percentOfcoincidentArea4 = (1.0*coincidentArea4) / numberOfclass4

    #统计第五空间
    rowOfpixel,colOfpixel = np.where(pixel_labels == 0)
    row = rowOfpixel.shape
    rowOfpixel5 = rowOfpixel.reshape(row[0], 1)
    row = colOfpixel.shape
    colOfpixel5 = colOfpixel.reshape(row[0], 1)
    for i in range(numberOfclass5):
        if resizeImage[rowOfpixel5[i,0],colOfpixel5[i,0],0] == 0:
            coincidentArea5 = coincidentArea5 + 1
    percentOfcoincidentArea5 = 1.0*coincidentArea5 / numberOfclass5

    #统计第六区间
    rowOfpixel,colOfpixel = np.where(pixel_labels == 5)
    row = rowOfpixel.shape
    rowOfpixel6 = rowOfpixel.reshape(row[0], 1)
    row = colOfpixel.shape
    colOfpixel6 = colOfpixel.reshape(row[0], 1)

    for i in range(numberOfclass6):
        if resizeImage[rowOfpixel6[i,0],colOfpixel6[i,0],0] == 0:
            coincidentArea6 = coincidentArea6 + 1
    if numberOfclass6 == 0:
        percentOfcoincidentArea6 = 0
    else:
        percentOfcoincidentArea6 = 1.0*coincidentArea6 / numberOfclass6

    percent = 0.50
    if float(percentOfcoincidentArea1) > float(percent):
        for i in range(numberOfclass1):
            pixel_labels[rowOfpixel1[i,0],colOfpixel1[i,0]] = 255
    else:
        for i in range(numberOfclass1):
            pixel_labels[rowOfpixel1[i,0],colOfpixel1[i,0]] = 0

    if float(percentOfcoincidentArea2) > float(percent):
        for i in range(numberOfclass2):
            pixel_labels[rowOfpixel2[i, 0], colOfpixel2[i, 0]] = 255
    else:
        for i in range(numberOfclass2):
            pixel_labels[rowOfpixel2[i, 0], colOfpixel2[i, 0]] = 0

    if float(percentOfcoincidentArea3) > float(percent):
        for i in range(numberOfclass3):
            pixel_labels[rowOfpixel3[i, 0], colOfpixel3[i, 0]] = 255
    else:
        for i in range(numberOfclass3):
            pixel_labels[rowOfpixel3[i, 0], colOfpixel3[i, 0]] = 0

    if float(percentOfcoincidentArea4) > float(percent):
        for i in range(numberOfclass4):
            pixel_labels[rowOfpixel4[i, 0], colOfpixel4[i, 0]] = 255
    else:
        for i in range(numberOfclass4):
            pixel_labels[rowOfpixel4[i, 0], colOfpixel4[i, 0]] = 0

    if float(percentOfcoincidentArea5) > float(percent):
        for i in range(numberOfclass5):
            pixel_labels[rowOfpixel5[i, 0], colOfpixel5[i, 0]] = 255
    else:
        for i in range(numberOfclass5):
            pixel_labels[rowOfpixel5[i, 0], colOfpixel5[i, 0]] = 0

    if float(percentOfcoincidentArea6) > float(percent):
        for i in range(numberOfclass6):
            pixel_labels[rowOfpixel6[i, 0], colOfpixel6[i, 0]] = 255
    else:
        for i in range(numberOfclass6):
            pixel_labels[rowOfpixel6[i, 0], colOfpixel6[i, 0]] = 0

    resizeDataOfImage = image_orig
    rowofImage,colofImage,depthOfImage = Image.shape
    for i in range(rowofImage):
        for j in range(colofImage):
            if pixel_labels[i,j] == 0:
                resizeDataOfImage[i,j,0] = 255
                resizeDataOfImage[i,j,1] = 255
                resizeDataOfImage[i,j,2] = 255

    IO = resizeDataOfImage
    I = IO
    if len(I.shape) > 2:
        I = cv2.cvtColor(I, cv2.COLOR_RGB2GRAY)
    else:
        I = I

    # otsu法求阈值
    thresh, bw = cv2.threshold(I, 0, 255, cv2.THRESH_OTSU)
    bw = (I > 250) * 1

    bw = np.abs(bw-1)
    L = measure.label(bw, connectivity=2)

    stats = measure.regionprops(L)
    Ar = np.array([x.area for x in stats])
    labels = np.zeros(I.shape)
    ind = np.where(Ar > 10)[0]

    a_row = len(ind)

    for i in range(a_row):
        labels[L == (ind[i]+1)] = 1

    IO = np.double(IO)
    I_result = np.zeros(IO.shape)
    I_result[:,:,0] =  np.multiply(IO[:,:,0],labels)
    I_result[:,:,1] =  np.multiply(IO[:,:,1],labels)
    I_result[:,:,2] =  np.multiply(IO[:,:,2],labels)

    Image_last= I_result
    min_im = np.min(np.min(Image_last[:,:, 0]))
    a_im,b_im,c_im = Image_last.shape
    min_im = np.ones([a_im,b_im]) * min_im

    logic_im = (min_im != Image_last[:,:,0])
    logic_im = logic_im.astype(np.uint8)
    areOfcrop = np.sum(np.sum(logic_im))
    areOfcrop = (areOfcrop*1.0) / (a_im*b_im)
    if areOfcrop > 0.8:
        HSII = HSI[:, :, 0].reshape(nrows, ncols,1)
        meanHH = meanH[YUV[:,:,0]].reshape(nrows, ncols,1)
        standardHH = standardH[YUV[:,:,0]].reshape(nrows, ncols,1)
        resizeImage = np.abs(HSII - meanHH) <= np.multiply(1.65,standardHH)
        areOfcropNew = np.sum(np.sum(resizeImage))
        areOfcrop = (areOfcropNew*1.0) / (a_im*b_im)
        Image_last = resizeImage.reshape(600, 800)
    return (Image_last, areOfcrop)
