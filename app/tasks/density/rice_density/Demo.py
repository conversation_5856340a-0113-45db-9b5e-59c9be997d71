#coding=utf-8
from __future__ import division
import numpy as np
import scipy.io
from app.tasks.density.rice_density.ricesegmentation import ricesegmentation
from app.tasks.density.rice_density.Ricesegmentation_3 import Ricesegmentation_3
from app.tasks.density.rice_density.Ricedensity_Detect import Ricedensity_Detect
import logging
import os,cv2
import os.path as ospath
from app.tasks.density.rice_density.utils import loadMatDate, saveMatData
def get_dict(key,value):
    return {key:value}


def rice_density(img_path, periodstagecode, days, area, riceTilleringRate=10):  # 分蘖率与覆盖度关系， num准确计算
    img_data = cv2.imread(img_path)
    if not periodstagecode:
       periodstagecode = 31

    Image_last, coverage = ricesegmentation(img_data)
    logging.info("coverage: {}".format(coverage))
    if np.sum(np.sum(Image_last.astype(np.int8))) == 0:
        Image_last, coverage = Ricesegmentation_3(img_data)
    # days = 1
    # RiceperivousAverageCoverage = 0.3
    # 图像根目录， 当前发育期， 发育期持续天数， 平均覆盖度
    density = Ricedensity_Detect(img_path, periodstagecode, days, coverage, area, Image_last, riceTilleringRate)
    print(density)
    return density

if __name__ == '__main__':
    img_path = r"D:\XQX\dailywork\Coding\xqx_shidian_v2\src\CropDetect\shuidao-density-detect\D9706_010405_20210701080000_01.jpg"
    # density = rice_density(img_name, 41, 1)
    # print(density)
# print("水稻密度算法模块加载完成！")
