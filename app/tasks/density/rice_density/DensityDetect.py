# -*-coding:utf-8-*-
from __future__ import division
import cv2
import numpy as np
from skimage import measure
import skimage.morphology as sm
from app.tasks.density.rice_density.DistanceAffribute import DistanceAffribute
from app.tasks.density.rice_density.ImageWipeArea import ImageWipeArea
try:
    from skimage import filters
except:
    # it is filter in earler version (<0.10)
    from skimage import filter as filters
from skimage import color


def DensityDetect(image):
    im = image
    num = im.shape
    im = im.astype(np.uint8)
    if len(num) > 2:
        im = cv2.cvtColor(im, cv2.COLOR_BGR2GRAY)


    thresh, bw = cv2.threshold(im, 0, 255, cv2.THRESH_OTSU)
    D = (im > thresh) * 1

    row,col = D.shape[:2]
    AreaNum = np.sum(np.sum(D))/(row*col)

    if AreaNum < 0.15:
        D = sm.opening(D, sm.square(4))
    elif AreaNum >= 0.15 and AreaNum < 0.21:
        D = sm.opening(D, sm.square(4))
    elif <PERSON>um >= 0.21 and AreaNum < 0.3:
        D = sm.opening(D, sm.square(4))
        D = sm.opening(D, sm.disk(2))
    elif AreaNum >= 0.3:
        D = sm.opening(D, sm.disk(3))
        D = sm.opening(D, sm.square(2))

    Threshold1 = (1.0*1)/3
    Threshold2 = 1.7
    DResult = DistanceAffribute(D, Threshold1, Threshold2)
    DResult = DResult.astype(np.uint8)
    if len(DResult.shape) > 2:
        image = cv2.cvtColor(DResult, cv2.COLOR_BGR2GRAY)
    else:
        image = DResult

    thresh, bw = cv2.threshold(image, 0, 255, cv2.THRESH_OTSU)
    DResult = (image > thresh) * 1
    DResult = measure.label(bw, connectivity=bw.ndim)
    s = measure.regionprops(DResult)
    centroids = np.array([x.centroid for x in s])

    AreaRegion = np.array([x.area for x in s])

    if len(AreaRegion) == 0:
        AreaThreshold = 0
    else:
        AreaThreshold = (np.max(AreaRegion) + np.mean(AreaRegion)) * 0.03

    Image_last = ImageWipeArea(DResult, AreaThreshold)
    num1 = Image_last.shape

    if len(num1) > 2:
        image = cv2.cvtColor(Image_last, cv2.COLOR_RGB2GRAY)
    else:
        imagae = Image_last
    '''
    image = color.rgb2gray(Image_last)
    
    level = filters.threshold_otsu(image)
    Image_last = (image <= level) * 1.0
    '''
    thres, binarea1 = cv2.threshold(image, 0, 255, cv2.THRESH_OTSU)
    Image_last = (image > thres) * 1

    dilatedBW = sm.opening(Image_last, sm.square(4))
    dilatedBW = dilatedBW.astype(np.uint8)

    '''
    thresh, bw = cv2.threshold(image, 0, 255, cv2.THRESH_OTSU)
    DResult = (image > thresh) * 1
    DResult = measure.label(bw, connectivity=bw.ndim)
    s = measure.regionprops(DResult)
    centroids = np.array([x.centroid for x in s])
    '''
    dilatedBW = measure.label(binarea1, connectivity=bw.ndim)
    ss = measure.regionprops(dilatedBW)

    RiceNum = len(ss)
    return RiceNum


