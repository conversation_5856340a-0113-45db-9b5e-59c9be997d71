# -*-coding:utf-8-*-
from __future__ import division
import numpy as np
import cv2
from skimage.transform import resize
try:
    from skimage import filters
except:
    # it is filter in earler version (<0.10)
    from skimage import filter as filters
from skimage import color


def Ricesegmentation_3(img_data):
    img = cv2.cvtColor(img_data, cv2.COLOR_BGR2RGB)

    im = resize(img, (600, 800), mode='reflect', preserve_range=True).astype('uint8')
    im = np.double(im)
    R = im[:,:,0]
    G = im[:,:,1]
    B = im[:,:,2]
    Ration1 = G/R
    Ration2 = G/B
    Ration1 = (Ration1 >= 1.0).astype('uint8')
    Ration2 = (Ration2 >= 1.0).astype('uint8')

    MaskImage = (Ration1 + Ration2)
    MaskImage = (MaskImage >= 2).astype('uint8')

    ExG = np.zeros(im.shape)
    row,col = im.shape[:2]
    for i in range(row):
        for j in range(col):
            if MaskImage[i,j] == 1:
                ExG[i,j,0] = R[i,j]
                ExG[i,j,1] = 2*G[i,j]
                ExG[i,j,2] = B[i,j]

    #ExG�ҶȻ�ʵ��
    ExGGray = np.zeros([row,col])
    for i in range(row):
        for j in range(col):
            ExGGray[i,j] = 0.299*ExG[i,j,0] + 0.587 *ExG[i,j,1] + 0.114 *ExG[i,j,2]

    #################### otsu������ֵ
    ExGGray = ExGGray.astype(np.uint8)
    thresh, bw = cv2.threshold(ExGGray, 0, 255, cv2.THRESH_OTSU + cv2.THRESH_BINARY)
    bw = (ExGGray <= thresh) * 1.0

    ExG = bw
    #��ȡExRͼ��
    row,col = im.shape[:2]
    ExR = np.zeros([row,col])
    ExR = np.double(ExR)
    for i in range(row):
        for j in range(col):
            ExR[i,j] = 0.96 *R[i,j] - G[i,j]

    image = color.rgb2gray(ExR)
    thresh = filters.threshold_otsu(image)
    bw = (image <= thresh) * 1.0
    ExR = bw

    #��ȡExBͼ��
    row,col = im.shape[:2]
    ExB = np.zeros([row,col])
    ExB = ExB.astype(np.double)
    for i in range(row):
        for j in range(col):
            ExB[i,j] = 1.38*B[i,j] - G[i,j]

    image = color.rgb2gray(ExB)
    thresh = filters.threshold_otsu(image)
    bw = (image <= thresh) * 1.0
    ExB = bw

    ExG_R = ExG - ExR
    #����ͼ����
    row,col = im.shape[:2]
    ResultImage = np.zeros([row,col])
    for i in range(row):
        for j in range(col):
            if ExG_R[i,j]  == 1 and MaskImage[i,j] == 1 and ExB[i,j] == 0:
                ResultImage[i,j] = 1


    #�ָ���ʾ���
    imResult = im
    im = np.double(im)
    ResultImage = np.double(ResultImage)

    imResult[:,:,0] =  np.multiply(im[:,:,0],ResultImage)
    imResult[:,:,1] =  np.multiply(im[:,:,1],ResultImage)
    imResult[:,:,2] =  np.multiply(im[:,:,2],ResultImage)

    Image_last = imResult.astype(np.int)
    ResultImage = ResultImage.astype(np.double)
    areOfcrop = np.sum(np.sum(ResultImage)) / (row*col)
    return(Image_last, areOfcrop)




