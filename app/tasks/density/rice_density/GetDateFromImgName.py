# -*- coding: utf-8 -*-
from __future__ import division
def GetDateFromImgName(imgname):
    '''
    获取当前图片的时间月份和日期
    :param imgname: 图片名
    :return: 图片的时间月份和日期
    '''
    # data = imgname[-17:-13]
    # year = int(data)
    # data = imgname[-13:-11]
    # month = int(data)
    # data = imgname[-11:-9]
    # day = int(data)
    info = imgname.split('.')[0].split('_')
    num = len(info)
    if num ==6:
        data=imgname.split("_")[4][0:4]
        year = int(data)
        data=imgname.split("_")[4][4:6]
        month = int(data)
        data=imgname.split("_")[4][6:8]
        day = int(data)
    elif num==5:
        data=imgname.split("_")[3][0:4]
        year = int(data)
        data=imgname.split("_")[3][4:6]
        month = int(data)
        data=imgname.split("_")[3][6:8]
        day = int(data)
    print(info,num,year, month, day)
    return (year, month, day)
