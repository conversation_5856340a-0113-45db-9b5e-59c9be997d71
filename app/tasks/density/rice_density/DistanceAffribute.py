#-*-coding:utf-8-*-
from __future__ import division
import os
import numpy as np
import scipy.io
import math
from skimage import measure
from scipy import ndimage
from app.tasks.density.rice_density.utils import loadMatDate, saveMatData

def DistanceAffribute(D, Threshold1, Threshold2):
    dilatedBW = D
    # % ����������̾���
    B = (D == False)
    B = B.astype(np.uint8)
    #D = bwdist(~D)
    D = ndimage.distance_transform_edt(D)


    dilatedBW = dilatedBW.astype(np.uint8)
    L = measure.label(dilatedBW, connectivity=1)
    s = measure.regionprops(L)   # % ������ͨ������
    centroids = np.array([x.centroid for x in s])  # % ��������ϲ�

    row = len(s) #.shape[:2]

    t = 0

    AreaRegion = np.array([x.area for x in s])

    for j in range(row):
        if s[j].area < (Threshold1 * np.mean(AreaRegion)):
            for i in range(s[j].coords.shape[:1][0]):
                D[s[j].coords[i,0],s[j].coords[i,1]] = 0

            t = 1
            DistRegion = np.zeros([1,(s[j].coords.shape[:1][0]+1)])
            for i in range(s[j].coords.shape[:1][0]):
                DistRegion[0,t] = D[s[j].coords[i,0],s[j].coords[i,1]]
                t = t + 1
            threash = (np.mean(DistRegion) + np.max(DistRegion))/2
            threash = (Threshold2 * np.mean(DistRegion))

            for i in (range(s[j].coords.shape[:1][0])):
                if D[s[j].coords[i,0],s[j].coords[i,1]] > threash:
                    D[ s[j].coords[i, 0],s[j].coords[i, 1]] = 1
                else:
                    D[s[j].coords[i, 0],s[j].coords[i, 1]] = 0
    DResult = D
    return DResult
