#-*-coding:utf-8-*-
from __future__ import division
import cv2
import os
import numpy as np
import math

def rgb2hsi(image):

    rgb_r = cv2.split(image)[0]
    rgb_g = cv2.split(image)[1]
    rgb_b = cv2.split(image)[2]

    width,height = rgb_r.shape[:2]
    hsi = np.zeros([width,height,3])
    hsi_h = hsi[:,:,0]
    hsi_s = hsi[:,:,1]
    hsi_i = hsi[:,:,2]
    PI = math.pi

    for i in range(width):
        for j in range(height):
            R = np.double(rgb_r[i,j])
            G = np.double(rgb_g[i,j])
            B = np.double(rgb_b[i,j])

            mmin = R
            if G < mmin:
                mmin = G
            if B < mmin:
                mmin = B

            I = (R + G + B) / 3
            S = 1 - mmin / I
            if S== 0.0:
                H = 0
            else:
                H = ((R-G)+(R-B))/2.0
                H = H/math.sqrt((R-G)**2+(R-B)**2)
                H = math.acos(H)
                if B > G:
                    H = 2 * PI - H
                H = H / (2*PI)
            hsi_h[i,j] = H
            hsi_s[i,j] = S
            hsi_i[i,j] = I / 255
    hsi[:,:,0] = hsi_h
    hsi[:,:,1] = hsi_s
    hsi[:,:,2] = hsi_i
    return hsi

