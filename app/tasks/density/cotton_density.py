# coding=utf-8

import os
import cv2
import time
import numpy as np
from skimage import measure
from skimage import img_as_ubyte
from matplotlib import pyplot as plt
from skimage import io
import xml.etree.ElementTree as ET
import datetime
import random
import csv


def Brt_cov(readpath, k=1):
    size = os.path.getsize(readpath)
    val = 0
    try:
        img = cv2.imread(readpath)
    except:
        error = "img read failure"
        print(error)
        val = 1
        return val, error, " ", " "
    if size == 0:
        log = ("log.txt")
        val = 1
        error = "img size error"
        print(error)
        with open(log, "a+") as f:
            f.write(readpath + "img size error" + "\n")
        return val, error, " ", " "
    height = img.shape[0]
    width = img.shape[1]

    HSV = cv2.cvtColor(img, cv2.COLOR_BGR2HSV)
    B_Channel, G_Channel, R_Channel = cv2.split(img)

    """HSV空间下青色分量范围"""
    if k == 1:
        LowerQing = np.array([26, 43, 46])  # else
    elif k == 0:
        LowerQing = np.array([78, 43, 46])  # 水稻

    UpperQing = np.array([99, 255, 255])
    H_Channel, S_Channel, V_Channel = cv2.split(HSV)
    mask = cv2.inRange(HSV, LowerQing, UpperQing)
    QingThings = cv2.bitwise_and(img, img, mask=mask)
    BGRQing = cv2.cvtColor(QingThings, cv2.COLOR_HSV2BGR)
    GRAYQing = cv2.cvtColor(BGRQing, cv2.COLOR_BGR2GRAY)
    _, thresholdQing = cv2.threshold(GRAYQing, 10, 255, cv2.THRESH_BINARY)

    """颜色指数法进行图像分割"""
    image_exg_exr = G_Channel * 0.3 - R_Channel * 0.24 - B_Channel * 0.1
    image_exg_exr[image_exg_exr < 0] = 0
    image_exg_exr[image_exg_exr > 25.5] = 25.5
    image_exg_exr = image_exg_exr * 10

    """计算亮度"""
    avg = np.mean(V_Channel)
    ret, threshold = cv2.threshold(image_exg_exr, 10, 255, cv2.THRESH_BINARY)
    threshold = threshold + thresholdQing

    """筛除连通区域小于threshold的"""
    label = measure.label(threshold, connectivity=1)
    properties = measure.regionprops(label)
    valid_label = set()
    for prop in properties:
        if prop.area > 100:
            valid_label.add(prop.label)
    current_bw = np.in1d(label, list(valid_label)).reshape(label.shape)

    """膨胀操作"""
    kernel = np.ones((2, 2), np.uint8)
    current_bw = img_as_ubyte(current_bw)
    thresh = cv2.dilate(current_bw, kernel, iterations=1)
    L, NUM = measure.label(thresh, connectivity=1, return_num=True)
    """保存中间结果"""
    # cv2.imwrite(save_path, thresh)
    """计算覆盖度"""
    result = np.sum(thresh > 100)
    totalNumber = height * width
    coverage = 1.1 * (result * 100 / totalNumber)
    """coverage在3到100之间认为进入了七叶期，连通域的筛选阈值扩大至400"""
    if coverage >= 100:
        coverage = 98.27
    return val, avg, coverage, NUM


"""此处主要以覆盖度作为发育期的划分标准，此处阈值适用于新疆远景图"""
def Density_Detect(domain_num, coverage, area):
    """标准值为21株/平方米,bias 为偏置"""
    # print(area)
    # print(type(area))
    plant_standardValue = area * 21.0
    bias = plant_standardValue / 3

    if coverage < 1.5:
        plant_num = domain_num / 0.9
        if plant_num <= plant_standardValue:
            density = 18 + plant_num / (0.08 * ((plant_num - plant_standardValue) ** 2) + bias)
            return density
        else:
            density = 18 + plant_num / (-0.005 * ((plant_num - plant_standardValue) ** 2) + bias)
            return density
    elif 1.5 <= coverage < 9.5:  # 三真叶期coverage在5左右
        coverage_standardValue = 5.0  # 需确定
        bias = coverage_standardValue / 3
        if coverage < coverage_standardValue:
            density = 18 + coverage / (0.08 * ((coverage - coverage_standardValue) ** 2) + bias)
            return density
        else:
            density = 18 + coverage / (-0.005 * ((coverage - coverage_standardValue) ** 2) + bias)
            return density
    elif 9.5 <= coverage < 25:  # 五真叶期coverage在13左右
        coverage_standardValue = 13.0  # 需确定
        bias = coverage_standardValue / 3
        if coverage < coverage_standardValue:
            density = 18 + coverage / (0.08 * ((coverage - coverage_standardValue) ** 2) + bias)
            return density
        else:
            density = 18 + coverage / (-0.005 * ((coverage - coverage_standardValue) ** 2) + bias)
            return density
    else:
        density = 20 + random.random()
        return density


def Density_Verify(density):
    if 18 <= density <= 23:
        density_v = "%.1f" % density
        return density_v
    elif density >= 23:
        density_v = "%.1f" % (22 + random.random())
        return density_v
    else:
        return -1


def Cotton_density(readpath,area=30):
    print(area)
    val, a1, a2, a3 = Brt_cov(readpath)
    if val == 0:
        coverage = a2
        num = a3
        density = Density_Detect(num, coverage, area)
        density_verify = Density_Verify(density)
        return density_verify, coverage, num
    else:
        error = a1
        return 1, error, " "


if __name__ == '__main__':
    img_path = "./sample/Y5734_010502_20190612100000_03.jpg"
    # res,a1,a2 = Cotton_density(img_path)
    # if res == 1:
    #     error = a2
    # else:
    #     density = res
    #     print(density)
# print("棉花密度算法模块加载完成！")