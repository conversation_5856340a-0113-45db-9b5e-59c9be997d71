# coding:utf-8

import os
import math
import datetime
from app.libs.resnet50.resent50_predict import Init, predict, parse_labels
from app.libs.sound_v1.demo_sound_predict import SoundDetect
from app.utils.parse_ini import ParseIniFile
from app.tasks.density.corn_density import Corn_density
from app.tasks.density.cotton_density import Cotton_density
from app.tasks.density.rice_density.Demo import rice_density
from app.tasks.coverage.coverage import brt_cov
from app.tasks.height.height_pc import Height
from app.tasks.community.pointplot import pointplot

pif = ParseIniFile('app/config.ini')
deploy_dir = pif.get_data("common","deploy_dir")
static_dir = pif.get_data("common","static_dir")

results_dir = os.path.join(static_dir, 'results')

class wheat(object):
    def __init__(self, item):
        if 'stage' == item:
            model_path = os.path.join(deploy_dir, 'img/classify/resnet50/pth', 'xiaomai.nb')
            self.predict = Init(model_path)
            self.label = os.path.join(deploy_dir, 'img/classify/resnet50/labels', 'xiaomai.txt')
        
        if 'height' == item:
            self.pc_height = Height()

    # 物候期
    def stage(self, params):
        img_path = params.get('file_path')
        
        typer = -1

        if not all([img_path]):
            typer = -1
        else:    
            res = predict(self.predict, img_path)
            typer = parse_labels(self.label)[res]
        
        return typer
    
    # 覆盖度   
    def coverage(self, params):
        img_path = params.get('file_path')
        
        cov = -1

        if not all([img_path]):
            cov = -1
        else:
            _, _, cov = brt_cov(1, img_path)
        
        return cov
    
    # 植株密度
    def density(self, params):
        current_stage = params.get('current_stage')
        pre_mean_cov = params.get('pre_mean_cov')

        density_value = -1

        if not all([current_stage, pre_mean_cov]):
            density_value = -1
        else:
            if pre_mean_cov > 0:
                if current_stage >= 21 and current_stage < 71:
                    density_value = 1728 * math.exp(-((pre_mean_cov /100 - 0.8745) / 0.4897) ** 2)
                elif current_stage >= 71:
                    density_value = 777.6 * math.exp(-((pre_mean_cov/100 - 0.8745) / 0.4897)**2)
            elif pre_mean_cov == 0:
                density_value = 0

        return density_value 
    
    # 叶面积指数
    def leaf(self, params):
        pre_mean_cov = params.get('pre_mean_cov')

        leaf_value = -1

        if not all([pre_mean_cov]):
            leaf_value = -1
        else:
            if pre_mean_cov > 0:
                leaf_value = 0.1324 * math.exp(4.566 * pre_mean_cov/100)
            elif pre_mean_cov == 0:
                leaf_value = 0
        
        return leaf_value
    
    # 干物质重量
    def dry(self, params):
        current_stage = params.get('current_stage')
        chumiao_start_time = params.get('chumiao_start_time')

        dry_value = -1

        if not all([current_stage, chumiao_start_time]):
            dry_value = -1
        else:
            current_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
            chumiao_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
            chumiao_days = (current_date - chumiao_date).days + 1
            if current_stage >= 21 and current_stage < 71:
                dry_value = 1495 * math.exp(-((int(chumiao_days) - 265.3) / 122.5) ** 2)
            elif current_stage >= 71:
                dry_value = 1495 * math.exp(-((int(chumiao_days) - 265.3) / 122.5) ** 2)

        return dry_value
    
    # 高度-三维点云
    def height(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')

        res = -1
        
        if not all([height, angle, csv_path]):
            res = -1
        else:
            res = self.pc_height.single_height_estimate(height, angle, csv_path)
        
        return res

    # 高度-计算
    def height_cal(self, params):
        current_stage = params.get('current_stage')
        current_time = params.get('current_time')
        chumiao_start_time = params.get('chumiao_start_time')
        yuedong_start_time = params.get('yuedong_start_time')
        fanqing_start_time = params.get('fanqing_start_time')
        bajie_start_time = params.get('bajie_start_time')

        height_value = -1

        # 校验参数
        if not all([current_stage, current_time]):    
            height_value = -1
        else:
            current_date = datetime.date(int(current_time[0:4]),int(current_time[4:6]),int(current_time[6:8]))
            if current_stage >= 21 and current_stage <= 31 and chumiao_start_time:
                chumiao_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
                chumiao_days = (current_date - chumiao_date).days + 1
                height_value = ((6798227.06239187 + 0.207762366469348) / (1 + math.pow((int(chumiao_days) / 49014045224107.7),-0.491608662789345)) - 0.207762366469348)

            elif current_stage == 41 and yuedong_start_time and chumiao_start_time:
                yuedong_date = datetime.date(int(yuedong_start_time[0:4]),int(yuedong_start_time[4:6]),int(yuedong_start_time[6:8]))
                chumiao_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
                days = (yuedong_date - chumiao_date).days + 1
                height_value = ((6798227.06239187 + 0.207762366469348) / (1 + math.pow((int(days) / 49014045224107.7),-0.491608662789345)) - 0.207762366469348)

            elif current_stage >= 51 and current_stage < 61 and fanqing_start_time:
                fanqing_date = datetime.date(int(fanqing_start_time[0:4]),int(fanqing_start_time[4:6]),int(fanqing_start_time[6:8]))
                fanqing_days = (current_date - fanqing_date).days + 1
                height_value = 9.24383112538619 * math.exp(int(fanqing_days) * 0.0238474048798052)

            elif current_stage >= 61 and bajie_start_time:
                bajie_date = datetime.date(int(bajie_start_time[0:4]),int(bajie_start_time[4:6]),int(bajie_start_time[6:8]))
                bajie_days = (current_date - bajie_date).days + 1
                height_value = ((81.5214669747035 - 31.9999674678488) / (1 + math.pow((int(bajie_days) / 25.7877807362715),-1.34016458556772)) + 31.9999674678488)


        return height_value
    
    # 群落图生成
    def community(self, params):

        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        
        msg = -1

        if not all([height, angle, csv_path]):    
            msg = -1
        else:
            dir = params.get('dir') if params.get('dir') else ''

            out_filename = csv_path.split('/')[-1].split('.')[0] + '.jpg'
            csv2jpg_dir = os.path.join(results_dir, dir)
            if not os.path.exists(csv2jpg_dir):
                os.makedirs(csv2jpg_dir)
            out_file_path = os.path.join(csv2jpg_dir, out_filename)
            
            try:
                pointplot(csv_path, height, angle, 30, 120, out_file_path, 20, 10)
                msg = 1
            except:
                msg = -1
        
        return msg
    
class rice(object):
    def __init__(self, item=None):
        if 'stage' == item:
            model_path = os.path.join(deploy_dir, 'img/classify/resnet50/pth', 'shuidao.nb')
            self.predict = Init(model_path)
            self.label = os.path.join(deploy_dir, 'img/classify/resnet50/label', 'shuidao.txt')
        
        if 'height' == item:
            self.pc_height = Height()

    # 物候期
    def stage(self, params):
        img_path = params.get('file_path')
        
        typer = -1

        if not all([img_path]):
            typer = -1
        else:    
            res = predict(self.predict, img_path)
            typer = parse_labels(self.label)[res]
        
        return typer
    
    # 覆盖度   
    def coverage(self, params):
        img_path = params.get('file_path')
        
        cov = -1

        if not all([img_path]):
            cov = -1
        else:
            _, _, cov = brt_cov(1, img_path)
        
        return cov
    
    # 高度-三维点云
    def height(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')

        res = -1
        
        if not all([height, angle, csv_path]):
            res = -1
        else:
            res = self.pc_height.single_height_estimate(height, angle, csv_path)
        
        return res

    def density(self, params):

        stage = params.get('current_stage')
        days = params.get('days')
        img_path = params.get('file_path')

        res = -1
        
        if not all([stage, days, img_path]):
            res = -1
        else:
            res = rice_density(img_path, stage, days, 1.1)
        
        return res
    def leaf(self, params):
        current_time = params.get('current_time')
        current_stage = params.get('current_stage')
        pre_mean_cov = params.get('pre_mean_cov')

        leaf_value = -1

        if not all([current_stage, current_time, pre_mean_cov]):
            leaf_value = -1
        else:
            month_current = current_time[4:6]
            if month_current < '08':
                if pre_mean_cov > 0:
                    leaf_value = 0.624 * math.exp(2.389 * pre_mean_cov)
                else:
                    leaf_value = 0.3
            else:
                if pre_mean_cov > 0:
                    leaf_value = 0.405 * math.exp(3.190 * pre_mean_cov)
                else:
                    leaf_value = 0.3
        
        return leaf_value


    def dry(self, params):
        current_stage = params.get('current_stage')
        pre_mean_cov = params.get('pre_mean_cov')

        dry_value = -1

        if not all([current_stage, pre_mean_cov]):
            dry_value = -1
        else:
            if current_stage >= 21 and current_stage <= 91:
                dry_value = 3.072 * math.exp(7.218 * pre_mean_cov)

        return dry_value


    def height_cal(self, params):
        max_height = params.get('max_height')
 
        current_time = params.get('current_time')
        chumiao_start_time = params.get('chumiao_start_time')

        bmodel = 0
        cmodel = 0

        height_value = -1

        # 校验参数
        if not all([max_height, current_time, chumiao_start_time]):
            height_value = -1
        else:
            month_chumiao = chumiao_start_time[4:6]
            current_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
            chumiao_date = datetime.date(int(current_time[0:4]),int(current_time[4:6]),int(current_time[6:8]))
            days = (current_date - chumiao_date).days + 1

            if month_chumiao < '08':
                bmodel = 22.10
                cmodel = 12.35
            elif month_chumiao >= '08':
                bmodel = 24.05
                cmodel = 22.16

            if (max_height / (1 + math.exp(-1 * (days - bmodel) / cmodel))) < 0:
                height_value = -1
            else:
                height_value = (max_height / (1 + math.exp(-1 * (days - bmodel) / cmodel)))


        return height_value
    
    def community(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        
        msg = -1

        if not all([height, angle, csv_path]):    
            msg = -1
        else:
            dir = params.get('dir') if params.get('dir') else ''

            out_filename = csv_path.split('/')[-1].split('.')[0] + '.jpg'
            csv2jpg_dir = os.path.join(results_dir, dir)
            if not os.path.exists(csv2jpg_dir):
                os.makedirs(csv2jpg_dir)
            out_file_path = os.path.join(csv2jpg_dir, out_filename)
            
            try:
                pointplot(csv_path, height, angle, 30, 120, out_file_path, 20, 10)
                msg = 1
            except:
                msg = -1
        
        return msg
    
class cotton(object):
    def __init__(self, item=None):
        if 'stage' == item:
            model_path = os.path.join(deploy_dir, 'img/classify/resnet50/pth', 'mianhua_02.nb')
            self.predict = Init(model_path)
            self.label = os.path.join(deploy_dir, 'img/classify/resnet50/label', 'mianhua.txt')

        if 'height' == item:
            self.pc_height = Height()

    # 物候期
    def stage(self, params):
        img_path = params.get('file_path')
        
        typer = -1

        if not all([img_path]):
            typer = -1
        else:    
            res = predict(self.predict, img_path)
            typer = parse_labels(self.label)[res]
        
        return typer
    
    # 覆盖度   
    def coverage(self, params):
        img_path = params.get('file_path')
        
        cov = -1

        if not all([img_path]):
            cov = -1
        else:
            _, _, cov = brt_cov(1, img_path)
        
        return cov
    
    # 高度-三维点云
    def height(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')

        res = -1
        
        if not all([height, angle, csv_path]):
            res = -1
        else:
            res = self.pc_height.single_height_estimate(height, angle, csv_path)
        
        return res
    
    def leaf(self, params):
        current_stage = params.get('current_stage')
        pre_mean_cov = params.get('pre_mean_cov')

        leaf_value = -1

        if not all([current_stage, pre_mean_cov]):
            leaf_value = -1
        else:
            if current_stage >= 21:
                leaftemp = -2.747 * math.log(1 - pre_mean_cov)
                if leaftemp >= 0:
                    leaf_value = (-2.747 * math.log(1 - pre_mean_cov))
                else:
                    leaf_value = 0.3
            else:
                leaf_value = 0.3
        
        return leaf_value

    def dry(self, params):
        current_stage = params.get('current_stage')
        chumiao_start_time = params.get('chumiao_start_time')

        dry_value = -1

        if not all([current_stage, chumiao_start_time]):
            dry_value = -1
        else:
            current_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
            chumiao_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
            chumiao_days = (current_date - chumiao_date).days + 1
            if current_stage >= 21 and current_stage < 71:
                dry_value = 1495 * math.exp(-((int(chumiao_days) - 265.3) / 122.5) ** 2)
            elif current_stage >= 71:
                dry_value = 1495 * math.exp(-((int(chumiao_days) - 265.3) / 122.5) ** 2)

        return dry_value

    def height_cal(self, params):
        max_height = params.get('max_height')
 
        current_time = params.get('current_time')
        chumiao_start_time = params.get('chumiao_start_time')
        height_value = -1

        # 校验参数
        if not all([max_height, current_time, chumiao_start_time]):
            height_value = -1
        else:
            current_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
            chumiao_date = datetime.date(int(current_time[0:4]),int(current_time[4:6]),int(current_time[6:8]))
            days = (current_date - chumiao_date).days + 1
            if (max_height / (1 + math.exp(-(days - 41.43) / 9.75))) > 0:
                height_value = (max_height / (1 + math.exp(-(days - 41.43) / 9.75)))
            else:
                height_value = -1


        return height_value

    def community(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        
        msg = -1

        if not all([height, angle, csv_path]):    
            msg = -1
        else:
            dir = params.get('dir') if params.get('dir') else ''

            out_filename = csv_path.split('/')[-1].split('.')[0] + '.jpg'
            csv2jpg_dir = os.path.join(results_dir, dir)
            if not os.path.exists(csv2jpg_dir):
                os.makedirs(csv2jpg_dir)
            out_file_path = os.path.join(csv2jpg_dir, out_filename)
            
            try:
                pointplot(csv_path, height, angle, 30, 120, out_file_path, 20, 10)
                msg = 1
            except:
                msg = -1
        
        return msg

    def density(self, params):
        img_path = params.get('file_path')

        res = -1

        if not all([img_path]):    
            res = -1
        else:
            res, a1, a2 = Cotton_density(img_path)
        
        return res

class corn(object):
    def __init__(self, item=None):
        if 'stage' == item:
            model_path = os.path.join(deploy_dir, 'img/classify/resnet50/pth', 'yumi.nb')
            self.predict = Init(model_path)
            self.label = os.path.join(deploy_dir, 'img/classify/resnet50/label', 'yumi.txt')
        
        if 'height' == item:
            self.pc_height = Height()

    # 物候期
    def stage(self, params):
        img_path = params.get('file_path')
        
        typer = -1

        if not all([img_path]):
            typer = -1
        else:    
            res = predict(self.predict, img_path)
            typer = parse_labels(self.label)[res]
        
        return typer
    
    # 覆盖度   
    def coverage(self, params):
        img_path = params.get('file_path')
        
        cov = -1

        if not all([img_path]):
            cov = -1
        else:
            _, _, cov = brt_cov(1, img_path)
        
        return cov
    
    # 高度-三维点云
    def height(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')

        res = -1
        
        if not all([height, angle, csv_path]):
            res = -1
        else:
            res = self.pc_height.single_height_estimate(height, angle, csv_path)
        
        return res
    

    def leaf(self, params):
        pre_mean_cov = params.get('pre_mean_cov')
        area = params.get('area')
        leaf_value = -1

        # 校验参数
        if not all([pre_mean_cov, area]):
            leaf_value = -1
        else:
            if pre_mean_cov > 0:
                if area == '1':
                    leaf_value = 0.456 * math.exp(2.411 * pre_mean_cov/ 100)
                elif area == '2':
                    leaf_value = 0.5173 * math.exp(2.431 * pre_mean_cov / 100)
                else:
                    leaf_value = 0.04944 * math.exp(5.159 * pre_mean_cov / 100)
            elif pre_mean_cov == 0:
                leaf_value = 0
        
        return leaf_value

    def dry(self, params):
        pre_mean_cov = params.get('pre_mean_cov')
        area = params.get('area')
        dry_value = -1

        # 校验参数
        if not all([pre_mean_cov, area]):
            dry_value = -1
        else:
            if pre_mean_cov > 0:
                if area == '1':
                    dry_value = 12.508 * math.exp(6.123 * pre_mean_cov / 100)
                elif area == '2':
                    # % 河南、山东
                    dry_value = 12.508 * math.exp(6.123 * pre_mean_cov / 100)
                else:
                    dry_value = 15.16 * math.exp(6.03 * pre_mean_cov / 100)
            elif pre_mean_cov == 0:
                dry_value = 0

        return dry_value

    def height_cal(self, params):
        max_height = params.get('max_height')
        area = params.get('area')
 
        current_time = params.get('current_time')
        chumiao_start_time = params.get('chumiao_start_time')
        height_value = -1

        # 校验参数
        if not all([max_height, area, current_time, chumiao_start_time]):
            height_value = -1
        else:
            current_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
            chumiao_date = datetime.date(int(current_time[0:4]),int(current_time[4:6]),int(current_time[6:8]))
            days = (current_date - chumiao_date).days + 1
            if area == '3':
                if max_height / (1 + math.exp(-(days - 42.22) / 8.736)) < 0:
                    height_value = -1
                else:
                    height_value = (max_height / (1 + math.exp(-(days - 42.22) / 8.736)))

            else:
                if max_height / (1 + math.exp(-(days - 27.33) / 9.436)) < 0:
                    height_value = -1
                else:
                    height_value = (max_height / (1 + math.exp(-(days - 27.33) / 9.436)))


        return height_value
    

    def community(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        
        msg = -1

        if not all([height, angle, csv_path]):    
            msg = -1
        else:
            dir = params.get('dir') if params.get('dir') else ''

            out_filename = csv_path.split('/')[-1].split('.')[0] + '.jpg'
            csv2jpg_dir = os.path.join(results_dir, dir)
            if not os.path.exists(csv2jpg_dir):
                os.makedirs(csv2jpg_dir)
            out_file_path = os.path.join(csv2jpg_dir, out_filename)
            
            try:
                pointplot(csv_path, height, angle, 30, 120, out_file_path, 20, 10)
                msg = 1
            except:
                msg = -1
        
        return msg

    def density(self, params):
        img_path = params.get('file_path')

        res = -1

        if not all([img_path]):    
            res = -1
        else:
            res, a1, a2 = Corn_density(img_path)

        return res

class aninaml(object):
    def __init__(self):
        pass

    def category(self, params):
        mp3_path = params.get('file_path')
        cat_list = []
        cats = None
        val, a1 = SoundDetect(mp3_path)
        if val == 0:
            for i in range(len(a1['tick_list'])):
                res = [str(ii) for ii in a1['pLabelID'][i]]
                cat_list += res
            
            cat_list = list(set(cat_list))
            cats = "-".join(cat_list) if cat_list else None

        return cats

    def date(self):
        pass

class Handler(object):
    def __init__(self, class_name, item):
        handler = self.str2object(class_name)
        self.object = handler(item)
        self.item = item

    # 将字符串装换为类名或函数名
    def str2object(self, object_str):
        str_object = globals()[object_str]
        return str_object
        

    def get_results(self, params):
        res = getattr(self.object, self.item)(params)

        return res