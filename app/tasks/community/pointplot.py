import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from matplotlib.pyplot import LinearLocator
from mpl_toolkits.mplot3d import Axes3D
import math

from io import BytesIO
from PIL import Image

plt.switch_backend('agg')  # 修改ssh上的backend


# pointplot(csvfile,mountHeight,mountAngle,elev,azim,jpgfile,figwidth,figheight)函数里面的参数含义如下：
# csvfile=需要显示的csv文件名，mountHeight=此台站配置文件里的雷达安装高度
# mountAngle=此台站配置文件里的雷达安装角度，elev=调节可视化输出上下方向的视角
# azim=调节可视化输出左右方向的视角,jpgfile=生成图片的文件名
# figwidth=生成图片的宽度,figheight=生成图片的高度

def pointplot(csvfile, mountHeight, mountAngle, elev, azim, jpgfile, figwidth, figheight):
    df = pd.read_csv(csvfile)
    df2 = df.iloc[:, 8:12]
    df2.columns = ['x', 'y', 'z', 'reflectivity']
    df2 = df2.loc[(df2['x'] != 0) & (df2['y'] != 0) & (df2['reflectivity'] != 0)]

    mountHeight = mountHeight
    mountAngle = 3.1415926535 / 180 * mountAngle
    cos1 = math.cos(mountAngle)
    sin1 = math.sin(mountAngle)

    xy = df2.iloc[:, 0:3].values

    xyz = np.dot(np.array([[cos1, 0, sin1], [0, 1, 0], [-1 * sin1, 0, cos1]]), xy.T).T

    xx = xyz[:, 0]
    yy = xyz[:, 1]

    xyz[:, 2] = xyz[:, 2] + mountHeight
    zz = xyz[:, 2]
    rf = df2['reflectivity']
    df3 = pd.DataFrame(xyz)
    df3.columns = ['x', 'y', 'z']
    df3['ref'] = rf

    zz = int((df3['z'].max() - df3['z'].min()) // 0.1 + 1)
    df3['zcut'] = pd.cut(df3['z'], zz, labels=range(0, zz))
    fenceng = df3.groupby(['zcut'])['z'].count()
    fc = np.where(fenceng == 0)[0]
    if len(fc) != 0:
        df3 = df3.loc[df3['zcut'] <= fc[0], ['x', 'y', 'z', 'zcut', 'ref']]
    # df3=df3.sample(n=100000)
    dff = df3.iloc[:, 0:3].values
    xin = dff[:, 0]
    yin = dff[:, 1]
    zin = dff[:, 2] * 100

    fig = plt.figure(figsize=(figwidth, figheight))  # 生成图片的像素大小
    ax = Axes3D(fig)
    scc = ax.scatter(xin, yin, zin, c=zin, s=10, cmap='hsv')  # 生成三角剖析表面

    ax.view_init(elev=elev, azim=azim)
    ax.set_xlabel('X (m)')
    ax.set_ylabel('Y (m)')
    ax.set_zlabel('Height (cm)')
    ax.set_title('Canopy 3D Plotting')
    zran = int(max(zin) / 5) + 1
    zmin=int(min(zin) / 5)+1
    print(zran)
    cbar = plt.colorbar(scc)
    #自定义z轴
    if zran>=0:
        ax.set_zlim(0, zran * 5)
        cbar.set_ticks(np.arange(0, zran * 5, 5))
    else:
        ax.set_zlim(zmin * 5,zran * 5)
        cbar.set_ticks(np.arange(zmin * 5,zran * 5, 5))
    # cbar = plt.colorbar(scc)
    
    ##设置z轴网格线的疏密
    ax.zaxis.set_major_locator(LinearLocator(zran-zmin + 1))
    plt.savefig(jpgfile)
    return 1  # 执行成功返回


def scatterplot(csvfile, mountHeight, mountAngle, elev, azim, jpgfile, figwidth, figheight,isgif):
    df = pd.read_csv(csvfile)
    df2 = df.iloc[:, 8:12]
    df2.columns = ['x', 'y', 'z', 'reflectivity']
    df2 = df2.loc[(df2['x'] != 0) & (df2['y'] != 0) & (df2['reflectivity'] != 0)]

    mountHeight = mountHeight
    mountAngle = 3.1415926535 / 180 * mountAngle
    cos1 = math.cos(mountAngle)
    sin1 = math.sin(mountAngle)

    xy = df2.iloc[:, 0:3].values

    xyz = np.dot(np.array([[cos1, 0, sin1], [0, 1, 0], [-1 * sin1, 0, cos1]]), xy.T).T

    xx = xyz[:, 0]
    yy = xyz[:, 1]

    xyz[:, 2] = xyz[:, 2] + mountHeight
    zz = xyz[:, 2]
    rf = df2['reflectivity']
    df3 = pd.DataFrame(xyz)
    df3.columns = ['x', 'y', 'z']
    df3['ref'] = rf

    zz = int((df3['z'].max() - df3['z'].min()) // 0.1 + 1)
    df3['zcut'] = pd.cut(df3['z'], zz, labels=range(0, zz))
    fenceng = df3.groupby(['zcut'])['z'].count()
    fc = np.where(fenceng == 0)[0]
    if len(fc) != 0:
        df3 = df3.loc[df3['zcut'] <= fc[0], ['x', 'y', 'z', 'zcut', 'ref']]
    
    # 将x轴,y轴整数周围点筛选
    if isgif in ["2","3"]:
        df3['xtop']=round(df3['x'])
        df3['ytop']=round(df3['y'])
        df3=df3[abs(df3['xtop']-df3['x'])<0.25]
        df3=df3[abs(df3['ytop']-df3['y'])<0.25]

    dff = df3.iloc[:, 0:3].values
    xin = dff[:, 0]
    yin = dff[:, 1]
    zin = dff[:, 2] * 100

    fig = plt.figure(figsize=(figwidth, figheight))  # 生成图片的像素大小
    ax = fig.add_subplot(111, projection='3d')
    # 生成沿着z轴颜色变化的散点图
    scatter=ax.scatter(xin, yin, zin,s=1,cmap='jet',c=zin)
    # 生成散点图
    # scatter=ax.scatter(xin, yin, zin,s=1,cmap='jet')

    # 标题字号大小
    plt.rcParams.update({'font.size': 20,})
    
    fig.colorbar(scatter,shrink=0.5, aspect=10,pad=0.1)

    ax.view_init(elev=elev, azim=azim)
    # 设置坐标轴标题及图表标题
    ax.set_xlabel('X (m)',fontdict={'size':20},labelpad=10)
    ax.set_ylabel('Y (m)',fontdict={'size':20},labelpad=10)
    ax.set_zlabel('Height (cm)',fontdict={'size':20},labelpad=20)
    ax.set_title('Canopy 3D Plotting')
    # x,y,z轴刻度值字体大小设置
    plt.xticks(size = 18)
    plt.yticks(size = 18)
    ax.zaxis.set_tick_params(labelsize = 18,pad=10)
    zran = int(max(zin) / 5) + 1
    zmin=int(min(zin) / 5)+1
    # print(zran)

    if isgif in ["1","3"]:
        buf = BytesIO()
        fig.savefig(buf, bbox_inches='tight', pad_inches=1.0)
        return Image.open(buf)
    else:
        plt.savefig(jpgfile)
        return 1
    

def scattergif(csvfile, mountHeight, mountAngle, elev, azim, jpgfile, figwidth, figheight,isgif):
    if isgif in ["1","3"]:
        images = [scatterplot(csvfile, mountHeight, mountAngle, elev, angle, jpgfile, figwidth, figheight,isgif) for angle in [45,90,135]]    
        # print(images,jpgfile)
        images[0].save(jpgfile, save_all=True, append_images=images[1:], duration=1000, loop=0)
    else:
        scatterplot(csvfile, mountHeight, mountAngle, elev, azim, jpgfile, figwidth, figheight,isgif) 
        

# scattergif('/home/<USER>/web_server_v5/app/tasks/community/Y5734_LIDAR_20211101113000_00.csv',4.58,41.5, 25, 125, 'mm1.gif', 25, 15,"1")
#pointplot('E:/ceshi/ALC_WX001_20220811160000_TIMING.csv', 6, 56.5, 30, 135, 'mm1.jpg', 25, 10)  # 函数调用示意
# pointplot("height/uncompress/mnt/cf/Y8678_LIDAR_20220826113000_00.csv", 4.05, 40, 30, 120,'matplot_out/mmm.jpg', 20, 10)  # 函数调用示意