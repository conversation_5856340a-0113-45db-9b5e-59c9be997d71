# coding=utf-8
"""
   作物生长量：高度(height)计算,激光雷达、红蓝杆
"""
from fileinput import filename
import os
import numpy as np
import pandas as pd
import logging
# from utils import uncompress
import math
import tarfile
# dir_height = '/data/data/height/'

def uncompress(src_file, dest_dir):
    print(src_file)
    filefmt = src_file.split('/')[-1].split('.')[-1]
    if filefmt in ('tgz', 'tar') :
        try :
            tar = tarfile.open(src_file)  
            names = tar.getnames()   
            for name in names:  
                tar.extract(name, dest_dir)  
            tar.close()
        except Exception as e :
            return (False, e, filefmt)
    else :
        return (False, 'error2', filefmt)

    return (True, '', filefmt)

class Height(object):
    def __init__(self):
        pass
        # 判断csv文件是否存在
        # if not os.path.exists(dir_height + 'result/result.csv'):
        #     df = pd.DataFrame([],columns=['taizhan','time','height'])
        #     df.to_csv(dir_height + 'result/result.csv', encoding='utf-8')
        # if not os.path.exists(dir_height + 'result/current_result.csv'):
        #     df = pd.DataFrame([],columns=['taizhan','current_time','average_height'])
        #     df.to_csv(dir_height + 'result/current_result.csv', encoding='utf-8')

    # def single_height_estimate(self, taizhan, csvfile):
    def single_height_estimate(self,mountHeight, mountAngle,targetLength,csvfile):

        df = pd.read_csv(csvfile)
        # 获取json文件，每一个json文件对应于一个台站株高仪的配置信息
        # canshu = pd.read_json(dir_height + 'json/' + taizhan + '.json',typ='series')
    
        # mountheight = canshu['height']
        mountAngle = 3.1415926535/180*mountAngle
        # dirr = canshu['dir']
        # folderr = canshu['folder']
        # adjustt = canshu['adjust']
        # correctt = canshu['correct']
        # heightmin = canshu['heightmin']
        # heightmax = canshu['heightmax']
        # shoudong = canshu['shoudong']
        # chushi = canshu['chushi']

        df2=df.iloc[:,8:12]
        df2.columns=['x','y','z','reflectivity']
        # mountHeight = mountheight
        # mountAngle = mountangle
        cos1 = math.cos(mountAngle)
        sin1 = math.sin(mountAngle)
        df2=df2.loc[(df2['x']!=0) & (df2['reflectivity']!=0)] #去除异常点
        #识别区域边长指定
        if targetLength!=0:
            tl=targetLength/2000
            df2=df2.loc[(df2['y']>=-tl) & (df2['y']<=tl) & (df2['z']>=-tl) & (df2['z']<=tl)]
       
        xy=df2.iloc[:,0:3].values    #取出原始坐标的xyz值
        xyz = np.dot(np.array([[cos1,0,sin1],[0,1,0],[-1*sin1,0,cos1]]),xy.T).T   #利用旋转矩阵进行变换

        #通过所有点与o点的相对高度加上hbase获得所有点的离地高度
        
        # if adjustt:
        #     try:
        #         rr = np.load(dir_height + 'npy/' + taizhan + '_adjust.npy')    
        #     except:
        #         print('run adjust first')            
        # if adjustt:
        #     xyz = np.dot(rr,xyz.T).T
            
            
        xyz[:,2]=xyz[:,2]+mountHeight
        df3=pd.DataFrame(xyz)
        df3.columns=['x','y','z']
        

        zz=int((df3['z'].max()-df3['z'].min())//0.1 + 1)
        df3['zcut']=pd.cut(df3['z'],zz,labels = range(0,zz))
        fenceng=df3.groupby(['zcut'])['z'].count()
        fc=np.where(fenceng==0)[0]
        if len(fc)!=0:
            df3 = df3.loc[df3['zcut']<=fc[0],['x','y','z','zcut']]
        
        zzcut = pd.qcut(df3['z'],[0,0.85,0.95,1],['1','2','3'])  #分位分类
        height=df3['z'][zzcut=='2'].mean()    #对95分位的点取平均获得height

        # if shoudong:
        #     try:
        #         tz = np.load(dir_height + 'npy/' + taizhan + '.npy')
        #         height=height+tz
        #     except:
        #         tiaozheng=chushi-height
        #         np.save(dir_height + 'npy/' + taizhan, tiaozheng)
                
        return height*1000

    def height_estimate(self,dir_height):
        # 读取csv文件的height数据
        all_height = pd.read_csv(dir_height + 'result/result.csv',index_col=0)
        current_height = pd.read_csv(dir_height + 'result/current_result.csv',index_col=0)
        # 筛选出指定文件夹下的tgz文件，有则解压至新的文件路径下
        #tgz_list = filter(lambda index: index.endswith('.tgz') and index.startswith(taizhan + '_LIDAR'), os.listdir('/data/data/height'))
        # 遍历该目录下文件，解压相应文件
        tgz_path = os.path.join(dir_height,"tgz/")
        for dirpath,dirnames,filenames in os.walk(tgz_path):
            # 分别处理每个tgz文件
            for file in filenames:
                import time
                time_start = time.time() 
                tgz_info = file.split('.')[0].split('_')
                tgz_name = file.split('.')[0] #Y5734_LIDAR_20210629173000_00
                tgz_taizhan = tgz_info[0]
                tgz_time = tgz_info[2]
                if not uncompress(os.path.join(tgz_path,file) , dir_height + 'uncompress')[0]:
                    print(tgz_name+ 'uncompres error')
                else:
                    # 算出该tgz文件的height
                    # 存入对应csv中
                    height = self.single_height_estimate(tgz_taizhan, dir_height + 'uncompress/mnt/cf/'+tgz_name+'.csv')
                    if len(all_height[(all_height['taizhan'] == tgz_taizhan) & (all_height['time'] == int(tgz_time))]) > 0:
                        all_height.loc[all_height[(all_height.taizhan == tgz_taizhan) & (all_height.time == int(tgz_time))].index.tolist(),'height'] = height
                    else:
                        all_height.loc[len(all_height)] = [tgz_taizhan, tgz_time, height]
                    print(all_height)
                    all_height.to_csv(dir_height + 'result/result.csv', encoding='utf-8')
                    # 获取指定台站的所有行数据
                    taizhan_all_height = all_height[(all_height['taizhan'] == tgz_taizhan)]
                    # 获取time series数据并转换为list
                    # taizhan_tgz_time_list = taizhan_all_height['time'].tolist()
                    # 获取小于当前时刻的最大的时刻
                    # max_time = max(filter(lambda value: value[0:8] + '000000' < tgz_time, taizhan_tgz_time_list))
                    # 将找出的时刻作为索引找出满足条件的高度数据并转换为list
                    sorted_height = taizhan_all_height.sort_values(by="time", inplace=False, ascending=False)
                    if len(sorted_height) >= 3:
                        height_list = sorted_height.iloc[:3]['height'].tolist()
                    # 求取height平均值
                    else:
                        height_list = sorted_height.iloc[0]['height'].tolist()
                    average_height = np.average(height_list)
                    # 更新：将最新结果写入current_result.csv文件中
                    # 判断台站信息是否在csv中
                    if tgz_taizhan in current_height['taizhan'].tolist():
                        current_height.loc[current_height[current_height.taizhan == tgz_taizhan].index.tolist(),'average_height'] = average_height
                    else:
                        current_height.loc[len(current_height)] = [tgz_taizhan, tgz_time, average_height]
                    current_height.to_csv(dir_height + 'result/current_result.csv', encoding='utf-8')
                    logging.info('current_average_height:   ' + str(average_height))
                    print(file,"success!")        
                time_end = time.time()  # 记录结束时间
                time_sum = time_end - time_start  # 计算的时间差为程序的执行时间，单位为秒/s
                print(time_sum)
        # 删除该tgz文件
        #os.remove(tgz_path)
                

# 调用示例
 
dir_height = '/home/<USER>/cropdetect/height/'
# h=Height()
# # h.height_estimate(dir_height)
# res=h.single_height_estimate(4.05,40,"height/uncompress/mnt/cf/Y8678_LIDAR_20220826113000_00.csv")

# print(res)
print("高度算法模块加载完成！")