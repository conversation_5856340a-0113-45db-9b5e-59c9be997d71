# coding=utf-8
"""
   作物生长量：高度(height)计算,激光雷达、红蓝杆
"""
from fileinput import filename
import os
import numpy as np
import pandas as pd
import logging
# from utils import uncompress
import math
import tarfile
# dir_height = '/data/data/height/'

def uncompress(src_file, dest_dir):
    print(src_file)
    filefmt = src_file.split('/')[-1].split('.')[-1]
    if filefmt in ('tgz', 'tar') :
        try :
            tar = tarfile.open(src_file)  
            names = tar.getnames()   
            for name in names:  
                tar.extract(name, dest_dir)  
            tar.close()
        except Exception as e :
            return (False, e, filefmt)
    else :
        return (False, 'error2', filefmt)

    return (True, '', filefmt)

class Height(object):
    def __init__(self):
        pass

    def average(self, xyz):
        df = pd.DataFrame(xyz)
        df.columns = ['x', 'y', 'z']
        height = None

        zz = int((df['z'].max() - df['z'].min()) // 0.1 + 1)
        df['zcut'] = pd.cut(df['z'], zz, labels=range(0, zz))
        fenceng = df.groupby(['zcut'])['z'].count()
        fc = np.where(fenceng == 0)[0]
        if len(fc) != 0:
            df = df.loc[df['zcut'] <= fc[0], ['x', 'y', 'z', 'zcut']]

        zzcut = pd.qcut(df['z'], [0, 0.85, 0.95, 1], ['1', '2', '3'])  # 分位分类
        height = df['z'][zzcut == '2'].mean()  # 对95分位的点取平均获得height

        return height

    def single_height_estimate(self, mountHeight, mountAngle, targetLength, csvfile):

        df = pd.read_csv(csvfile)
        mountAngle = 3.1415926535 / 180 * mountAngle

        df2 = df.iloc[:, 8:12]
        df2.columns = ['x', 'y', 'z', 'reflectivity']

        cos1 = math.cos(mountAngle)
        sin1 = math.sin(mountAngle)
        df2 = df2.loc[(df2['x'] != 0) & (df2['reflectivity'] != 0)]  # 去除异常点

        # 识别区域边长指定
        if targetLength != 0:
            tl = targetLength / 2000
            df2 = df2.loc[(df2['y'] >= -tl) & (df2['y'] <= tl) & (df2['z'] >= -tl) & (df2['z'] <= tl)]

        # 取出原始坐标的xyz值
        xy = df2.iloc[:, 0:3].values
        xyz = np.dot(np.array([[cos1, 0, sin1], [0, 1, 0], [-1 * sin1, 0, cos1]]), xy.T).T
        xyz[:, 2] = xyz[:, 2] + mountHeight
        if xyz.size>0:
            height = self.average(xyz)
        else:
            height=0
        depth = mountHeight - height

    
        return (height * 1000, depth * 1000)

    def single_height_estimate_v1(self, mountHeight, mountAngle, targetLength, csvfile):

        df = pd.read_csv(csvfile)
        mountAngle = 3.1415926535/180*mountAngle


        df2=df.iloc[:,8:12]
        df2.columns=['x','y','z','reflectivity']

        cos1 = math.cos(mountAngle)
        sin1 = math.sin(mountAngle)
        df2=df2.loc[(df2['x']!=0) & (df2['reflectivity']!=0)] #去除异常点
        #识别区域边长指定
        if targetLength!=0:
            tl=targetLength/2000
            df2=df2.loc[(df2['y']>=-tl) & (df2['y']<=tl) & (df2['z']>=-tl) & (df2['z']<=tl)]
       
        xy=df2.iloc[:,0:3].values    #取出原始坐标的xyz值
        xyz = np.dot(np.array([[cos1,0,sin1],[0,1,0],[-1*sin1,0,cos1]]),xy.T).T   #利用旋转矩阵进行变换

        #通过所有点与o点的相对高度加上hbase获得所有点的离地高度
            
            
        xyz[:,2]=xyz[:,2] + mountHeight
        df3=pd.DataFrame(xyz)
        df3.columns=['x','y','z']
        # print(xyz)

        zz=int((df3['z'].max()-df3['z'].min())//0.1 + 1)
        df3['zcut']=pd.cut(df3['z'],zz,labels = range(0,zz))
        fenceng=df3.groupby(['zcut'])['z'].count()
        fc=np.where(fenceng==0)[0]
        if len(fc)!=0:
            df3 = df3.loc[df3['zcut']<=fc[0],['x','y','z','zcut']]
        
        zzcut = pd.qcut(df3['z'],[0,0.85,0.95,1],['1','2','3'])  #分位分类
        height=df3['z'][zzcut=='2'].mean()    #对95分位的点取平均获得height
                
        return (height * 1000, -1)
