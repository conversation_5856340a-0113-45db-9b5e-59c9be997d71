# coding:utf-8

import os
import math
import datetime
import numpy as np
from app.libs.resnet50.resent50_predict import Init, predict, parse_labels
from app.libs.yolov5.yolov5_predict import Yolov5
from app.libs.sound_v2.predict import SoundDetection as SoundDetection_v2
from app.libs.sound_v3.predict import SoundDetection as SoundDetection_v3
from app.utils.parse_ini import ParseIniFile
from app.tasks.density.corn_density import Corn_density
from app.tasks.density.cotton_density import Cotton_density
from app.tasks.density.rice_density.Demo import rice_density
from app.tasks.coverage.coverage import brt_cov
from app.tasks.height.height_pc import Height
from app.tasks.community.pointplot import pointplot
import collections

pif = ParseIniFile('app/config.ini')
deploy_dir = pif.get_data("common","deploy_dir")
static_dir = pif.get_data("common","static_dir")

results_dir = os.path.join(static_dir, 'results')

class OrderedSet(collections.Set):
    def __init__(self, iterable=()):
        self.d = collections.OrderedDict.fromkeys(iterable)

    def __len__(self):
        return len(self.d)

    def __contains__(self, element):
        return element in self.d

    def __iter__(self):
        return iter(self.d)
    

class wheat(object):
    def __init__(self, item):
        if 'stage' == item:
            model_path = os.path.join(deploy_dir, 'img/classify/resnet50/pth', 'xiaomai.nb')
            self.predict = Init(model_path)
            self.label = os.path.join(deploy_dir, 'img/classify/resnet50/label', 'xiaomai.txt')
        
        if 'height' == item:
            self.pc_height = Height()

    # 物候期
    def stage(self, params):
        img_path = params.get('file_path')
        
        res = predict(self.predict, img_path)
        typer = parse_labels(self.label)[res]
        
        return typer
    
    # 覆盖度   
    def coverage(self, params):
        img_path = params.get('file_path')
        _, _, cov = brt_cov(1, img_path)
        return round(cov,2)
    
    # 植株密度
    def density(self, params):
        current_stage = params.get('current_stage')
        pre_mean_cov = params.get('pre_mean_cov')

        if pre_mean_cov > 0:
            if current_stage >= 21 and current_stage < 71:
                density_value = 1728 * math.exp(-((pre_mean_cov /100 - 0.8745) / 0.4897) ** 2)
            elif current_stage >= 71:
                density_value = 777.6 * math.exp(-((pre_mean_cov/100 - 0.8745) / 0.4897)**2)
        elif pre_mean_cov == 0:
            density_value = 0

        return density_value
    # 状态
    def state(self, params):
        pass

    # 叶面积指数
    def leaf(self, params):
        pre_mean_cov = params.get('pre_mean_cov')
        if pre_mean_cov > 0:
            leaf_value = 0.1324 * math.exp(4.566 * pre_mean_cov/100)
        elif pre_mean_cov == 0:
            leaf_value = 0
        return leaf_value
    
    # 干物质重量
    def dry(self, params):
        current_stage = params.get('current_stage')
        chumiao_start_time = params.get('chumiao_start_time')


        current_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
        chumiao_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
        chumiao_days = (current_date - chumiao_date).days + 1
        if current_stage >= 21 and current_stage < 71:
            dry_value = 1495 * math.exp(-((int(chumiao_days) - 265.3) / 122.5) ** 2)
        elif current_stage >= 71:
            dry_value = 1495 * math.exp(-((int(chumiao_days) - 265.3) / 122.5) ** 2)

        return dry_value
    
    # 高度-三维点云
    def height(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        target_length = params.get('target_length')

        res = self.pc_height.single_height_estimate(height, angle, target_length, csv_path)
        
        return res
    
    # 群落图生成
    def community(self, params):

        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        
        msg = -1

        if not all([height, angle, csv_path]):    
            msg = -1
        else:
            dir = params.get('dir') if params.get('dir') else ''

            out_filename = csv_path.split('/')[-1].split('.')[0] + '.jpg'
            csv2jpg_dir = os.path.join(results_dir, dir)
            if not os.path.exists(csv2jpg_dir):
                os.makedirs(csv2jpg_dir)
            out_file_path = os.path.join(csv2jpg_dir, out_filename)
            
            try:
                pointplot(csv_path, height, angle, 30, 120, out_file_path, 20, 10)
                msg = 1
            except:
                msg = -1
        
        return msg
    
class rice(object):
    def __init__(self, item):
        if 'stage' == item:
            model_path = os.path.join(deploy_dir, 'img/classify/resnet50/pth', 'shuidao.nb')
            self.predict = Init(model_path)
            self.label = os.path.join(deploy_dir, 'img/classify/resnet50/label', 'shuidao.txt')
        
        if 'height' == item:
            self.pc_height = Height()

    # 物候期
    def stage(self, params):
        img_path = params.get('file_path')
        res = predict(self.predict, img_path)
        typer = parse_labels(self.label)[res]
        
        return typer
    
    # 覆盖度   
    def coverage(self, params):
        img_path = params.get('file_path')
        _, _, cov = brt_cov(1, img_path)
        
        return round(cov,2)
    
    # 高度-三维点云
    def height(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        target_length = params.get('target_length')

        res = self.pc_height.single_height_estimate(height, angle, target_length, csv_path)
        
        return res

    def density(self, params):

        stage = params.get('current_stage')
        days = params.get('days')
        img_path = params.get('file_path')
        area = params.get('rice_area')
        area=1.1 if area==None else float(area)
        print(img_path, stage, days,area)
        res = rice_density(img_path, stage, days,area)[0]
        
        return res
    
    def leaf(self, params):
        current_time = params.get('current_time')
        pre_mean_cov = params.get('pre_mean_cov')


        month_current = current_time[4:6]
        if month_current < '08':
            if pre_mean_cov > 0:
                leaf_value = 0.624 * math.exp(2.389 * pre_mean_cov)
            else:
                leaf_value = 0.3
        else:
            if pre_mean_cov > 0:
                leaf_value = 0.405 * math.exp(3.190 * pre_mean_cov)
            else:
                leaf_value = 0.3
        
        return leaf_value


    def dry(self, params):
        current_stage = params.get('current_stage')
        pre_mean_cov = params.get('pre_mean_cov')

        if current_stage >= 21 and current_stage <= 91:
            dry_value = 3.072 * math.exp(7.218 * pre_mean_cov)

        return dry_value

    
    def community(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        
        msg = -1

        if not all([height, angle, csv_path]):    
            msg = -1
        else:
            dir = params.get('dir') if params.get('dir') else ''

            out_filename = csv_path.split('/')[-1].split('.')[0] + '.jpg'
            csv2jpg_dir = os.path.join(results_dir, dir)
            if not os.path.exists(csv2jpg_dir):
                os.makedirs(csv2jpg_dir)
            out_file_path = os.path.join(csv2jpg_dir, out_filename)
            
            try:
                pointplot(csv_path, height, angle, 30, 120, out_file_path, 20, 10)
                msg = 1
            except:
                msg = -1
        
        return msg
    # 状态
    def state(self, params):
        pass
    
class cotton(object):
    def __init__(self, item):
        if 'stage' == item:
            model_path = os.path.join(deploy_dir, 'img/classify/resnet50/pth', 'mianhua_02.nb')
            self.predict = Init(model_path)
            self.label = os.path.join(deploy_dir, 'img/classify/resnet50/label', 'mianhua.txt')

        if 'height' == item:
            self.pc_height = Height()

    # 物候期
    def stage(self, params):
        img_path = params.get('file_path')
        res = predict(self.predict, img_path)
        typer = parse_labels(self.label)[res]

        if typer == 'kaihuasheng':
            typer = 'kaihua'
        if typer == 'tuxusheng':
            typer = 'tuxu'
        
        return typer
    
    # 覆盖度   
    def coverage(self, params):
        img_path = params.get('file_path')
        _, _, cov = brt_cov(1, img_path)
        
        return round(cov,2)
    
    # 高度-三维点云
    def height(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        target_length = params.get('target_length')


        res = self.pc_height.single_height_estimate(height, angle, target_length, csv_path)
        
        return res
    
    def leaf(self, params):
        current_stage = params.get('current_stage')
        pre_mean_cov = params.get('pre_mean_cov')
        
        # pre_mean_cov=pre_mean_cov/100
        # print(pre_mean_cov)
        if current_stage >= 21:
            leaftemp = -2.747 * math.log(1 - pre_mean_cov)
            if leaftemp >= 0:
                leaf_value = (-2.747 * math.log(1 - pre_mean_cov))
            else:
                leaf_value = 0.3
        else:
            leaf_value = 0.3
        
        return leaf_value

    def dry(self, params):
        current_stage = params.get('current_stage')
        chumiao_start_time = params.get('chumiao_start_time')


        current_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
        chumiao_date = datetime.date(int(chumiao_start_time[0:4]),int(chumiao_start_time[4:6]),int(chumiao_start_time[6:8]))
        chumiao_days = (current_date - chumiao_date).days + 1
        if current_stage >= 21 and current_stage < 71:
            dry_value = 1495 * math.exp(-((int(chumiao_days) - 265.3) / 122.5) ** 2)
        elif current_stage >= 71:
            dry_value = 1495 * math.exp(-((int(chumiao_days) - 265.3) / 122.5) ** 2)

        return dry_value

    def community(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        

        dir = params.get('dir') if params.get('dir') else ''

        out_filename = csv_path.split('/')[-1].split('.')[0] + '.jpg'
        csv2jpg_dir = os.path.join(results_dir, dir)
        if not os.path.exists(csv2jpg_dir):
            os.makedirs(csv2jpg_dir)
        out_file_path = os.path.join(csv2jpg_dir, out_filename)
        
        try:
            pointplot(csv_path, height, angle, 30, 120, out_file_path, 20, 10)
            msg = 1
        except:
            msg = -1
        
        return msg

    def density(self, params):
        img_path = params.get('file_path')
        area = params.get('cotton_area')
        area=30 if area==None else float(area)
        res, a1, a2 = Cotton_density(img_path, area)
        
        return res

    # 状态
    def state(self, params):
        pass

class corn(object):
    def __init__(self, item):
        if 'stage' == item:
            model_path = os.path.join(deploy_dir, 'img/classify/resnet50/pth', 'yumi.nb')
            yolov5_path = os.path.join(deploy_dir, 'img/detect/yolov5/pth', 'yumi_yolov5s.rknn')
            label_detect_path = os.path.join(deploy_dir, 'img/detect/yolov5/label', 'yumi.txt')
            self.predict = Init(model_path)
            self.label = os.path.join(deploy_dir, 'img/classify/resnet50/label', 'yumi.txt')
            
            self.yolov5_object = Yolov5(yolov5_path, label_detect_path)
        
        if 'height' == item:
            self.pc_height = Height()

    # 物候期
    def stage(self, params):
        img_path = params.get('file_path')
        typer = None
        
        res = predict(self.predict, img_path)
        class_res = parse_labels(self.label)[res]

        img = self.yolov5_object.image_pre_process(img_path)
        outputs = self.yolov5_object.inference(img)
        classes = self.yolov5_object.image_post_process(outputs)
        
        print(classes)
        classes_dict = {}

        if class_res in ['kaitu', 'rushu']:
            if len(classes) == 0:
                typer = 'kaihua'
            else:
                for key in classes:
                    classes_dict[key] = classes_dict.get(key, 0) + 1

                if classes_dict.get('tusi') and classes_dict.get('tusi') >= 10:
                    typer = 'tusi'
                elif classes_dict.get('tusi') == None or ( classes_dict.get('tusi') and classes_dict.get('tusi') < 10):
                    typer = 'rushu' if class_res == 'rushu' else 'kaihua'
        else:
            typer = class_res

        return typer
    
    # 覆盖度   
    def coverage(self, params):
        img_path = params.get('file_path')
        
        _, _, cov = brt_cov(1, img_path)
        
        return round(cov,2)
    
    # 高度-三维点云
    def height(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        target_length = params.get('target_length')

        res = self.pc_height.single_height_estimate(height, angle, target_length, csv_path)
        
        return res
    

    def leaf(self, params):
        pre_mean_cov = params.get('pre_mean_cov')
        area = params.get('area')

        if pre_mean_cov > 0:
            if area == '1':
                leaf_value = 0.456 * math.exp(2.411 * pre_mean_cov/ 100)
            elif area == '2':
                leaf_value = 0.5173 * math.exp(2.431 * pre_mean_cov / 100)
            else:
                leaf_value = 0.04944 * math.exp(5.159 * pre_mean_cov / 100)
        elif pre_mean_cov == 0:
            leaf_value = 0
        
        return leaf_value

    def dry(self, params):
        pre_mean_cov = params.get('pre_mean_cov')
        area = params.get('area')

        if pre_mean_cov > 0:
            if area == '1':
                dry_value = 12.508 * math.exp(6.123 * pre_mean_cov / 100)
            elif area == '2':
                # % 河南、山东
                dry_value = 12.508 * math.exp(6.123 * pre_mean_cov / 100)
            else:
                dry_value = 15.16 * math.exp(6.03 * pre_mean_cov / 100)
        elif pre_mean_cov == 0:
            dry_value = 0

        return dry_value
    
    def community(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        
        msg = -1

        if not all([height, angle, csv_path]):    
            msg = -1
        else:
            dir = params.get('dir') if params.get('dir') else ''

            out_filename = csv_path.split('/')[-1].split('.')[0] + '.jpg'
            csv2jpg_dir = os.path.join(results_dir, dir)
            if not os.path.exists(csv2jpg_dir):
                os.makedirs(csv2jpg_dir)
            out_file_path = os.path.join(csv2jpg_dir, out_filename)
            
            try:
                pointplot(csv_path, height, angle, 30, 120, out_file_path, 20, 10)
                msg = 1
            except:
                msg = -1
        
        return msg

    def density(self, params):
        img_path = params.get('file_path')
        area = params.get('corn_area')
        area=13 if area==None else float(area)
        
        res, a1, a2 = Corn_density(img_path, area)

        return res
    # 状态
    def state(self, params):
        pass

class sound(object):
    def __init__(self, item):
        config_path_v2 = os.path.join(deploy_dir, 'sound/v2/config', 'config.json')
        config_path_v3 = os.path.join(deploy_dir, 'sound/v3/config', 'config.json')
        self.sound_v2 = SoundDetection_v2(config_path_v2)
        self.sound_v3 = SoundDetection_v3(config_path_v3)


    def category(self, params):
        mp3_path = params.get('file_path')
        df = self.sound_v2.melspec_generator(mp3_path)
        
        v2_set = self.sound_list_v2(df)
        v3_set = self.sound_list_v3_1(df)

        return v2_set + v3_set

    def category_3(self, params):
        mp3_path = params.get('file_path')
        df = self.sound_v2.melspec_generator(mp3_path)
        
        v2_list = self.sound_list_v2(df)
        v3_list = self.sound_list_v3(df)

        # list(OrderedSet(class_two) - OrderedSet(class_three))

        print(v2_list, v3_list)
        # 交集
        intersection = list(set(v2_list) & set(v3_list))
        # 差集1
        difference_v2 = list(OrderedSet(v2_list) - OrderedSet(v3_list))
        # 差集2
        difference_v3 = list(OrderedSet(v3_list) - OrderedSet(v2_list))
        class_list = [] 
        if len(intersection) >= 3:
            class_list = intersection[0:3]
        
        elif len(intersection) == 2:
            if len(difference_v3) >= 1:
                class_list = intersection + [difference_v3[0]]
            else:
                class_list = intersection + [difference_v2[0]] if len(difference_v2) >= 1 else intersection
            
        elif len(intersection) == 1:
            if len(difference_v3) >= 2:
                class_list = intersection + difference_v3[0:2]
            elif len(difference_v3) == 1:
                class_list = intersection + [difference_v2[0]] + [difference_v3[0]] if len(difference_v2) >= 1 else intersection + [difference_v3[0]]
            else:
                class_list = intersection + difference_v2[0:2] if len(difference_v2) >= 2 else intersection + difference_v2
        
        else:         
            if len(difference_v2) >= 1:
                class_list = [difference_v2[0]] + difference_v3[0:2] if len(difference_v3) >= 2 else [difference_v2[0]] + difference_v3
            else:
                class_list = difference_v3[0:3] if len(difference_v3) >= 3 else difference_v3

        # 替换编码
        code_replace = {
            '04000577': '04000159',
            '04000413': '04000130'
        }

        class_filter_list = list(map(lambda x: code_replace.get(x) if x in code_replace.keys() else x, class_list))

        # 通过豆雁，添加灰雁
        class_filter_list.append("04000106") if "04000104" in class_filter_list  and "04000104" != class_filter_list[-1] else class_filter_list
        class_filter_list.pop(-2) if len(class_filter_list) > 3 else class_filter_list

        return class_filter_list
    
    # 综合判识
    def sound_list(self, params):
        mp3_path = params.get('file_path')
        df = self.sound_v2.melspec_generator(mp3_path)
        
        v2_list = self.sound_list_v2(df)
        v3_list = self.sound_list_v3(df)

        # 释放npu
        self.sound_v2.release_npu()
        self.sound_v3.release_npu()

        print(v2_list, v3_list)
        # 交集
        intersection = list(set(v2_list) & set(v3_list))
        # 差集1
        difference_v2 = list(OrderedSet(v2_list) - OrderedSet(v3_list))
        # 差集2
        difference_v3 = list(OrderedSet(v3_list) - OrderedSet(v2_list))

        class_list = [] 
        if len(intersection) >= 3:
            class_list = intersection[0:3]
        
        elif len(intersection) == 2:
            if len(difference_v3) >= 1:
                class_list = intersection + [difference_v3[0]]
            else:
                class_list = intersection + [difference_v2[0]] if len(difference_v2) >= 1 else intersection
            
        elif len(intersection) == 1:
            if len(difference_v3) >= 2:
                class_list = intersection + difference_v3[0:2]
            elif len(difference_v3) == 1:
                class_list = intersection + [difference_v2[0]] + [difference_v3[0]] if len(difference_v2) >= 1 else intersection + [difference_v3[0]]
            else:
                class_list = intersection + difference_v2[0:2] if len(difference_v2) >= 2 else intersection + difference_v2
        
        else:         
            if len(difference_v2) >= 1:
                class_list = [difference_v2[0]] + difference_v3[0:2] if len(difference_v3) >= 2 else [difference_v2[0]] + difference_v3
            else:
                class_list = difference_v3[0:3] if len(difference_v3) >= 3 else difference_v3


        # 替换编码
        code_replace = {
            '04000577': '04000159',
            '04000413': '04000130'
        }

        class_filter_list = list(map(lambda x: code_replace.get(x) if x in code_replace.keys() else x, class_list))

        # 通过豆雁，添加灰雁
        class_filter_list.append("04000106") if "04000104" in class_filter_list  and "04000104" != class_filter_list[-1] else class_filter_list
        class_filter_list.pop(-2) if len(class_filter_list) > 3 else class_filter_list
        
        return class_filter_list
    
    def sound_list_v2(self, df):
        preditct_result = self.sound_v2.sound_detect(df)
 
        thresh = self.sound_v2.get_config('thresh')
        class_name = self.sound_v2.get_config('class_name')
        class_code = self.sound_v2.get_config('class_code')

        pre_res_array = np.array(preditct_result)
        thresh_array = np.array(thresh)

        count = np.sum(pre_res_array>=thresh_array, axis=0)
        # 降序
        count_sort = -np.sort(-count)

        max1 = count_sort[0]
        max2 = count_sort[1]
        max3 = count_sort[2]
        # max = np.max(count)
        index1 = np.where(count==max1)[0].tolist()
        class_list1 = [class_code[i] for i in index1] if max1 != 0 else []

        index2 = np.where(count==max2)[0].tolist()
        class_list2 = [class_code[i] for i in index2] if max2 != 0 else []
     
        index3 = np.where(count==max3)[0].tolist()
        class_list3 = [class_code[i] for i in index3] if max3 != 0 else []
        # class_list = [class_code[i] for i in index] if max != 0 else []

        class_list = class_list1 + class_list2 + class_list3
    
        class_three = class_list[0:3] if len(class_list) > 3 else class_list
        return class_three

    def sound_list_v3(self, df):
        preditct_result = self.sound_v3.sound_detect(df)

        class_name = self.sound_v3.get_config('class_name')
        threshold = self.sound_v3.get_config("threshold_value")
        low_value_flag = self.sound_v3.get_config("low_value_flag")


        count = np.sum(preditct_result,axis=0)

        count_sort = -np.sort(-count)

        max1 = count_sort[0]
        max2 = count_sort[1]
        max3 = count_sort[2]

        index1 = np.where(count==max1)[0].tolist()
        class_list1 = [(class_name[i], max1) for i in index1] if max1 != 0 else []

        index2 = np.where(count==max2)[0].tolist()
        class_list2 = [(class_name[i], max2) for i in index2] if max2 != 0 else []
     
        index3 = np.where(count==max3)[0].tolist()
        class_list3 = [(class_name[i], max3) for i in index3] if max3 != 0 else []


        class_list = class_list1 + class_list2 + class_list3
    
        class_three = class_list[0:3] if len(class_list) > 3 else class_list

        class_three = [tuple_index[0] for tuple_index in class_three if tuple_index[1] > threshold]

        # 根据low_value_flag判断是否删除阈值最低编码
        print(low_value_flag==True)
        class_three.pop(-1) if low_value_flag and len(class_three) > 1 else class_three
       
        # 对空列表，加一个最高阈值的编码
        class_three = [class_list[0][0]] if len(class_three) == 0 and len(class_list) > 0 else class_three


        return class_three
    
    def sound_list_v3_1(self, df):
        # mp3_path = params.get('file_path')

        preditct_result = self.sound_v3.sound_detect(df)
        # 释放npu
        # self.sound_v3.release_npu()

        # thresh = self.sound_v2.get_config('thresh')
        class_name = self.sound_v3.get_config('class_name')
        # class_code = self.sound_v2.get_config('class_code')

        count = np.sum(preditct_result,axis=0)
        # print(len(count))
            # 降序
        count_sort = -np.sort(-count)

        max1 = count_sort[0]
        max2 = count_sort[1]
        max3 = count_sort[2]
        max4 = count_sort[3]
        max5 = count_sort[4]
        max6 = count_sort[5]
        # max = np.max(count)
        index1 = np.where(count==max1)[0].tolist()
        class_list1 = [(class_name[i],max1) for i in index1] if max1 != 0 else []

        index2 = np.where(count==max2)[0].tolist()
        class_list2 = [(class_name[i],max2) for i in index2] if max2 != 0 else []
     
        index3 = np.where(count==max3)[0].tolist()
        class_list3 = [(class_name[i],max3) for i in index3] if max3 != 0 else []

        index4 = np.where(count==max4)[0].tolist()
        class_list4 = [(class_name[i],max4) for i in index4] if max4 != 0 else []

        index5 = np.where(count==max5)[0].tolist()
        class_list5 = [(class_name[i],max5) for i in index5] if max5 != 0 else []

        index6 = np.where(count==max6)[0].tolist()
        class_list6 = [(class_name[i],max6) for i in index6] if max6 != 0 else []

        # class_list = [class_code[i] for i in index] if max != 0 else []

        class_list = class_list1 + class_list2 + class_list3 + class_list4 + class_list5 + class_list6
    
        class_six = class_list[0:6] if len(class_list) > 6 else class_list

        return class_six
    
    def date(self, params):
        mp3_path = params.get('file_path')
        df = self.sound_v2.melspec_generator(mp3_path)
        
        v2_set = set(self.sound_list_v2(df))
        v3_set = set(self.sound_list_v3(df))

        # 释放npu
        self.sound_v2.release_npu()
        self.sound_v3.release_npu()
        
        print(v2_set, v3_set)
        # 交集
        intersection = list(v2_set & v3_set)
        # 差集1
        difference_v2 = list(v2_set - v3_set)
        # 差集2
        difference_v3 = list(v3_set - v2_set)

        class_list = []
        if len(intersection) >= 3:
            class_list = intersection[0:3]
        
        elif len(intersection) == 2:
            if len(difference_v3) >= 1:
                class_list = intersection + [difference_v3[0]]
            else:
                class_list = intersection + [difference_v2[0]] if len(difference_v2) >= 1 else intersection
            
        elif len(intersection) == 1:
            if len(difference_v3) >= 2:
                class_list = intersection + difference_v3[0:2]
            elif len(difference_v3) == 1:
                class_list = intersection + [difference_v3[0]] + [difference_v2[0]] if len(difference_v2) >= 1 else intersection + [difference_v3[0]]
            else:
                class_list = intersection + difference_v2[0:2] if len(difference_v2) >= 2 else intersection + difference_v2
        
        else:         
            if len(difference_v2) >= 1:
                class_list = [difference_v2[0]] + difference_v3[0:2] if len(difference_v3) >= 2 else [difference_v2[0]] + difference_v3
            else:
                class_list = difference_v3[0:3] if len(difference_v3) >= 3 else difference_v3

        return class_list

class pc(object):
    def __init__(self, item):
        if 'height' == item:
            self.pc_height = Height()

    def height(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        target_length = params.get('target_length')

        canopy_height, target_depth = self.pc_height.single_height_estimate(height, angle, target_length, csv_path)
        
        return canopy_height

    def community(self, params):
        height = params.get('height')
        angle = params.get('angle')
        csv_path = params.get('file_path')
        
        dir = params.get('dir') if params.get('dir') else ''

        out_filename = csv_path.split('/')[-1].split('.')[0] + '.jpg'
        csv2jpg_dir = os.path.join(results_dir, dir)
        if not os.path.exists(csv2jpg_dir):
            os.makedirs(csv2jpg_dir)
        out_file_path = os.path.join(csv2jpg_dir, out_filename)
        
        try:
            pointplot(csv_path, height, angle, 30, 120, out_file_path, 20, 10)
            msg = 1
        except:
            msg = -1
        
        return msg

class Handler(object):
    def __init__(self, class_name, item):
        handler = self.str2object(class_name)
        self.object = handler(item)
        self.item = item

    # 将字符串装换为类名或函数名
    def str2object(self, object_str):
        str_object = globals()[object_str]
        return str_object
        

    def get_results(self, params):
        res = getattr(self.object, self.item)(params)

        return res
