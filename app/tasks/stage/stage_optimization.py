# coding=utf-8

import datetime

class StageJudger(object):
    _N = 3
    _Stage_Range_Delta = 2
    corn_stage_code2name = {
        11: "bozhong", 21: "chumiao", 31: "sanye", 41: "qiye", 61: "bajie",
        71: "chouxiong", 72: "kaihua", 73: "tusi", 81: "rushu", 91: "chengshu"}
    rice_stage_code2name = {
        11: "bozhong", 21: "chumiao", 31: "yizai", 41: "fanqing", 51: "fennie", 52: "bajie", 61: "yunsui",
        71: "chousui", 81: "rushu", 91: "chengshu"}
    wheat_stage_code2name = {
        11: "bozhong", 21: "chumiao", 22: "sanye", 31: "fennie", 41: "yuedong", 51: "fanqing", 61: "bajie", 62: "yunsui",
        71: "chousui", 72: "kaihua", 81: "rushu", 91: "chengshu"}
    cotton_stage_code2name = {
        11: "bozhong", 21: "chumiao", 31: "sanye", 41: "wuye", 61: "xian<PERSON>i", 71: "kaihua",
        81: "lieling",
        82: "tuxu", 91: "tingzhi"}
    

    stage_days = {
        "corn": {"bozhong": [0, 5], "chumiao": [0, 6], "sanye": [5, 13], "qiye": [5, 18], "bajie": [13, 30],"chouxiong": [0, 3], "kaihua": [0, 3], "tusi": [20, 28], "rushu": [20,35], "chengshu": [0, 90]},
        "rice": {"bozhong": [0, 2], "chumiao": [0, 6], "yizai": [0, 8], "fanqing": [0, 15], "fennie": [12, 30], "bajie": [8, 30], "yunsui":[6, 15], "chousui": [5, 25], "rushu": [12, 29], "chengshu": [0, 30]},
        "wheat": {"bozhong": [0, 3], "chumiao": [7, 12], "sanye": [5, 15], "fennie": [10, 60],"yuedong": [20, 80], "fanqing": [20, 60],"bajie": [25, 35], "yunsui": [5, 12], "chousui": [6, 15], "kaihua": [18, 30], "rushu": [10, 25], "chengshu": [0, 30]},
        "cotton": {"bozhong": [0, 5], "chumiao": [10, 30], "sanye": [5, 10], "wuye": [15, 45], "xianlei": [20, 30], "kaihua": [45, 57],"lieling": [4, 5],"tuxu": [30, 40], "tingzhi": [0, 80]}
    }

    stage_dict = {
        "corn": [11, 21, 31, 41, 61, 71, 72, 73, 81, 91],
        "rice": [11, 21, 31, 41, 51, 52, 61, 71, 81, 91],
        "wheat": [11, 21, 22, 31, 41, 51, 61, 62, 71, 72, 81, 91],
        "cotton": [11, 21, 31, 41, 61, 71, 81, 82, 91]
    }

    def __init__(self, crop_type, active_stage, stages, active_stage_time='', nn=3):
        self.crop_type = crop_type
        self.active_stage = active_stage # 请求中的发育期，初始发育期
        self.last_5_stages = stages
        self._N = nn
        self.active_stage_time = active_stage_time

    # 计算间隔时间
    def get_days(self, now_file_time, active_stage_time):
        year1 = now_file_time[0:4]
        month1 = now_file_time[4:6]
        day1 = now_file_time[6:8]
        Day1 = datetime.date(int(year1), int(month1), int(day1))

        year2 = active_stage_time[0:4]
        month2 = active_stage_time[4:6]
        day2 = active_stage_time[6:8]
        Day2 = datetime.date(int(year2), int(month2), int(day2))

        # 这里是不是要加1！！！！！！！！！！
        Days = (Day1 - Day2).days + 1
        return Days
    
    # 判断连续发育期
    # 发生发育期的改变的条件：连续n张图的发育期识别结果相同
    def is_last_n(self, n, now_stage):
        if len(self.last_5_stages) < n:
            return False
        for i in range(n):
            if self.last_5_stages[-1 - i] !=  now_stage:
                return False
        return True
    
    # 获取当前发育期的下一个发育期
    def get_next_stage(self, now_stage):
        if self.crop_type in StageJudger.stage_dict:
            stage_lists = StageJudger.stage_dict[self.crop_type]
            for i in range(len(stage_lists)):
                if now_stage == stage_lists[i]:
                    try:
                        return stage_lists[i + 1]
                    except:
                        return stage_lists[i]
    
    # 更新最近的5次发育期识别结果
    # def append_last_stage(self, now_stage):
    #     self.last_5_stages.append(now_stage)
    #     if len(self.last_5_stages) >= 5:
    #         self.last_5_stages = self.last_5_stages[-5:]

    # 判断时间间隔
    # 这里的参数要注意：
    # now_stage指的请求中的发育期，now_file_time为当前的识别时间，不一致的
    def judge_stage(self, now_stage, now_file_time):
        if self.crop_type == "corn":
            this_code2name = StageJudger.corn_stage_code2name
        elif self.crop_type == "rice":
            this_code2name = StageJudger.rice_stage_code2name
        elif self.crop_type == "wheat":
            this_code2name = StageJudger.wheat_stage_code2name
        elif self.crop_type == "cotton":
            this_code2name = StageJudger.cotton_stage_code2name
        else:
            this_code2name = None

        if self.crop_type in StageJudger.stage_days:
            if self.active_stage_time == "":
                self.active_stage_time = now_file_time[:8]

            day_range = StageJudger.stage_days[self.crop_type][this_code2name[now_stage]]
            Days = self.get_days(now_file_time, self.active_stage_time)
            next_stage = self.get_next_stage(now_stage)

            # 先判断是不是在指定范围之内
            if Days < day_range[0]:
                self.active_stage = now_stage
                return


            if Days >= day_range[1]:
                self.active_stage_time = now_file_time[:8]
                self.active_stage = next_stage
                return

            # 在指定范围之内，在判断发育期是否一致
            if self.active_stage != now_stage:
                self.active_stage_time = now_file_time[:8]
                self.active_stage = next_stage
                return
            
    def _set_stage_imple(self, now_stage):
        # 首先给发育期赋值为请求中的初始发育期
        local_stage = self.active_stage
        
        if now_stage is None:
            return

        if now_stage <= 0:
            return

        if now_stage <= self.active_stage:
            return

        if self.crop_type == 'corn':
            if now_stage == 71 and self.is_last_n(2, now_stage):
                self.active_stage = now_stage
                return

        if self.crop_type == 'rice':
            if now_stage == 41 and self.is_last_n(1, now_stage):
                self.active_stage = now_stage
                return

        if self.crop_type == 'cotton':
            if now_stage == 31 and self.is_last_n(2, now_stage):  # 三叶
                self.active_stage = now_stage
                return
            if now_stage == 82 and self.is_last_n(2, now_stage):  # 吐絮
                self.active_stage = now_stage
                return
            if now_stage == 91 and self.is_last_n(1, now_stage):  # 停止
                self.active_stage = now_stage
                return

        if self.is_last_n(self._N, now_stage):
            stage = self.get_next_stage(local_stage)
            self.active_stage = stage
            return
        
    def set_stage(self, now_stage, now_file_time):
        # 请求中的当前发育期
        local_stage = self.active_stage
        self._set_stage_imple(now_stage)
        self.judge_stage(local_stage, now_file_time)



    # 输入：模型识别的发育期，当前识别时间
    # 输出：当前发育期、当前发育开始时间、间隔时间
    def get_stage_info(self, now_stage_name, now_file_time):
        now_stage_code = self.predict2CurrentPeriodFlag(self.crop_type, now_stage_name)
        self.set_stage(now_stage_code, now_file_time)
        days = self.get_days(now_file_time, self.active_stage_time)
        return (self.active_stage, self.active_stage_time, days, now_stage_code)


    def predict2CurrentPeriodStr(self, cropType, predictions):
        stageFlag = {"rice": {"bozhong": 11, "chumiao": 21, "sanye": 22, "yizai": 31, "fanqing": 41, "fennie": 51,
                                 "bajie": 52, "yunsui": 61, "chousui": 71, "rushu": 81, "chengshu": 91},
                     "wheat": {"bozhong": 11, "chumiao": 21, "sanye": 22, "fennie": 31, "yuedong": 41,
                                 "fanqing": 51, "qisheng": 52, "bajie": 61, "yunsui": 62, "chousui": 71, "kaihua": 72,
                                 "rushu": 81, "chengshu": 91},
                     "corn": {"bozhong": 11, "chumiao": 21, "sanye": 31, "yizai": 32, "qiye": 41, "bajie": 61,
                              "chouxiong": 71, "kaihua": 72, "tusi": 73, "rushu": 81, "chengshu": 91},
                     "cotton": {"bozhong": 11, "chumiao": 21, "sanye": 31, "wuye": 41, "xianlei": 61,
                                 "kaihua": 71, "lieling": 81, "tuxu": 82, "tingzhi": 91}}
        if cropType in stageFlag:
            for peroid,num in stageFlag[cropType].items():
                if int(num) == predictions:
                    return peroid


    def predict2CurrentPeriodFlag(self, cropType, code_name):
        stageFlag = {"rice": {"bozhong": 11, "chumiao": 21, "sanye": 22, "yizai": 31, "fanqing": 41, "fennie": 51,
                                 "bajie": 52, "yunsui": 61, "chousui": 71, "rushu": 81, "chengshu": 91},
                     "wheat": {"bozhong": 11, "chumiao": 21, "sanye": 22, "fennie": 31, "yuedong": 41,
                                 "fanqing": 51, "qisheng": 52, "bajie": 61, "yunsui": 62, "chousui": 71, "kaihua": 72,
                                 "rushu": 81, "chengshu": 91},
                     "corn": {"bozhong": 11, "chumiao": 21, "sanye": 31, "yizai": 32, "qiye": 41, "bajie": 61,
                              "chouxiong": 71, "kaihua": 72, "tusi": 73, "rushu": 81, "chengshu": 91},
                     "cotton": {"bozhong": 11, "chumiao": 21, "sanye": 31, "wuye": 41, "xianlei": 61,
                                 "kaihua": 71, "lieling": 81, "tuxu": 82, "tingzhi": 91}}
        if cropType in stageFlag:
            if code_name in stageFlag[cropType]:
                return stageFlag[cropType][code_name]