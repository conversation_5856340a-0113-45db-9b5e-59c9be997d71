#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
点云分析API使用示例
"""

from point_cloud_api import PointCloudAnalyzer, recognize_crop_height, generate_point_cloud_visualization

def example_1_height_recognition():
    """
    示例1: 作物高度识别
    """
    print("=== 示例1: 作物高度识别 ===")
    
    # 使用便捷函数进行高度识别
    result = recognize_crop_height(
        input_file="ALC_Q2764_20250514120000_TIMING.csv",
        recognition_area_size=10.0,  # 10x10米区域
        output_dir="output"
    )
    
    print(f"识别结果:")
    print(f"  作物高度: {result['crop_height']:.3f} 米")
    print(f"  总点数: {result['total_points']}")
    print(f"  地面点数: {result['ground_points']}")
    print(f"  作物点数: {result['crop_points']}")
    print(f"  处理时间: {result['processing_time']:.2f} 秒")
    print(f"  状态: {result['status']}")
    print(f"  结果文件: {result['result_file']}")
    
    return result

def example_2_visualization_generation():
    """
    示例2: 群落图生成
    """
    print("\n=== 示例2: 群落图生成 ===")
    
    # 使用便捷函数生成可视化
    result = generate_point_cloud_visualization(
        input_file="ALC_Q2764_20250514120000_TIMING.csv",
        output_formats=['html', 'jpg', 'gif'],  # 生成所有格式
        output_dir="output",
        sample_size=15000
    )
    
    print(f"可视化结果:")
    print(f"  生成文件: {result['generated_files']}")
    print(f"  作物高度: {result['crop_height']:.3f} 米")
    print(f"  处理时间: {result['processing_time']:.2f} 秒")
    print(f"  状态: {result['status']}")
    
    return result

def example_3_class_based_api():
    """
    示例3: 使用类接口进行分步操作
    """
    print("\n=== 示例3: 使用类接口进行分步操作 ===")
    
    # 创建分析器实例
    analyzer = PointCloudAnalyzer()
    
    # 第一步: 进行高度识别
    print("步骤1: 高度识别...")
    height_result = analyzer.height_recognition(
        input_file="ALC_Q2764_20250514120000_TIMING.csv",
        recognition_area_size=None,  # 使用全部区域
        output_dir="output"
    )
    
    if height_result['status'] == 'success':
        print(f"高度识别成功: {height_result['crop_height']:.3f} 米")
        
        # 第二步: 生成不同格式的可视化
        print("步骤2: 生成HTML可视化...")
        viz_result_html = analyzer.generate_visualization(
            output_formats=['html'],
            output_dir="output"
        )
        
        print("步骤3: 生成JPG图像...")
        viz_result_jpg = analyzer.generate_visualization(
            output_formats=['jpg'],
            output_dir="output"
        )
        
        print("步骤4: 生成GIF动画...")
        viz_result_gif = analyzer.generate_visualization(
            output_formats=['gif'],
            output_dir="output"
        )
        
        print(f"所有操作完成!")
        print(f"HTML文件: {viz_result_html['generated_files']}")
        print(f"JPG文件: {viz_result_jpg['generated_files']}")
        print(f"GIF文件: {viz_result_gif['generated_files']}")
    else:
        print(f"高度识别失败: {height_result['status']}")

def example_4_area_comparison():
    """
    示例4: 不同识别区域的对比
    """
    print("\n=== 示例4: 不同识别区域的对比 ===")
    
    areas = [None, 20.0, 10.0, 5.0]  # None表示全部区域
    
    for area in areas:
        area_name = "全部区域" if area is None else f"{area}x{area}米"
        print(f"\n测试区域: {area_name}")
        
        result = recognize_crop_height(
            input_file="ALC_Q2764_20250514120000_TIMING.csv",
            recognition_area_size=area,
            output_dir="output"
        )
        
        if result['status'] == 'success':
            print(f"  作物高度: {result['crop_height']:.3f} 米")
            print(f"  点数: {result['total_points']} (地面: {result['ground_points']}, 作物: {result['crop_points']})")
            print(f"  处理时间: {result['processing_time']:.2f} 秒")
        else:
            print(f"  识别失败: {result['status']}")

if __name__ == "__main__":
    print("点云分析API使用示例")
    print("=" * 50)
    
    # 运行所有示例
    try:
        example_1_height_recognition()
        example_2_visualization_generation()
        example_3_class_based_api()
        example_4_area_comparison()
        
        print("\n" + "=" * 50)
        print("所有示例运行完成! 请查看output目录中的结果文件。")
        
    except Exception as e:
        print(f"示例运行出错: {e}")
        import traceback
        traceback.print_exc() 