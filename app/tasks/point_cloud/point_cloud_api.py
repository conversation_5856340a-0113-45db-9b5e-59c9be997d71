#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Point Cloud Analysis API
提供作物高度识别和群落图生成的API接口
"""

import os
import pandas as pd
import numpy as np
from typing import Optional, List, Dict, Union, Tuple
import warnings
warnings.filterwarnings('ignore')

# 导入相关模块
from modules.data_loader import load_point_cloud
from modules.preprocessing import preprocess_point_cloud
from modules.pose_estimation import estimate_sensor_pose
from modules.ground_plane_fitting import fit_ground_plane
from modules.crop_height import calculate_crop_height
from modules.visualization import visualize_point_cloud

class PointCloudAnalyzer:
    """
    点云分析器类，提供作物高度识别和可视化功能
    """
    
    def __init__(self):
        """初始化点云分析器"""
        self.raw_data = None
        self.processed_data = None
        self.transformed_data = None
        self.crop_height = None
        
    def height_recognition(
        self,
        input_file: str,
        recognition_area_size: Optional[float] = None,
        output_dir: str = "output"
    ) -> Dict[str, Union[float, int, str]]:
        """
        作物高度识别接口
        
        Args:
            input_file (str): 输入点云数据文件路径
            recognition_area_size (Optional[float]): 识别区域边长(米)，None表示使用全部区域
            output_dir (str): 输出目录
            
        Returns:
            Dict: 包含识别结果的字典
            {
                'crop_height': float,  # 作物高度(米)
                'total_points': int,   # 总点数
                'ground_points': int,  # 地面点数
                'crop_points': int,    # 作物点数
                'processing_time': float,  # 处理时间(秒)
                'status': str,         # 处理状态
                'result_file': str     # 结果文件路径
            }
        """
        import time
        start_time = time.time()
        
        try:
            print(f"开始作物高度识别分析...")
            print(f"输入文件: {input_file}")
            if recognition_area_size:
                print(f"识别区域边长: {recognition_area_size} 米")
            else:
                print("识别区域: 全部区域")
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # Step 1: 加载点云数据
            print("Step 1: 加载点云数据...")
            self.raw_data = load_point_cloud(input_file)
            
            # Step 2: 区域裁剪(如果指定了识别区域大小)
            if recognition_area_size is not None:
                self.raw_data = self._crop_recognition_area(self.raw_data, recognition_area_size)
                print(f"裁剪到 {recognition_area_size}x{recognition_area_size} 米区域，剩余 {len(self.raw_data)} 个点")
            
            # Step 3: 预处理
            print("Step 2: 预处理点云数据...")
            self.processed_data = preprocess_point_cloud(self.raw_data)
            
            # Step 4: 位姿估计
            print("Step 3: 估计传感器位姿...")
            rotation_matrix, translation_vector = estimate_sensor_pose(self.processed_data)
            
            # Step 5: 地面拟合和坐标变换
            print("Step 4: 拟合地面平面并变换坐标系...")
            self.transformed_data, ground_plane_params = fit_ground_plane(
                self.processed_data, rotation_matrix, translation_vector
            )
            
            # Step 6: 计算作物高度
            print("Step 5: 计算作物高度...")
            self.crop_height = calculate_crop_height(self.transformed_data)
            
            # 统计信息
            ground_threshold = 0.05
            ground_points = len(self.transformed_data[self.transformed_data['Z'] <= ground_threshold])
            crop_points = len(self.transformed_data[self.transformed_data['Z'] > ground_threshold])
            
            # 保存结果
            result_file = os.path.join(output_dir, "height_recognition_result.txt")
            with open(result_file, 'w', encoding='utf-8') as f:
                f.write(f"作物高度识别结果\n")
                f.write(f"==================\n")
                f.write(f"输入文件: {input_file}\n")
                f.write(f"识别区域: {'全部区域' if recognition_area_size is None else f'{recognition_area_size}x{recognition_area_size} 米'}\n")
                f.write(f"检测到的作物高度: {self.crop_height:.3f} 米\n")
                f.write(f"总点数: {len(self.raw_data)}\n")
                f.write(f"地面点数: {ground_points}\n")
                f.write(f"作物点数: {crop_points}\n")
                f.write(f"处理时间: {time.time() - start_time:.2f} 秒\n")
            
            result = {
                'crop_height': float(self.crop_height),
                'total_points': len(self.raw_data),
                'ground_points': int(ground_points),
                'crop_points': int(crop_points),
                'processing_time': time.time() - start_time,
                'status': 'success',
                'result_file': result_file
            }
            
            print(f"高度识别完成! 检测到作物高度: {self.crop_height:.3f} 米")
            print(f"结果已保存到: {result_file}")
            
            return result
            
        except Exception as e:
            error_result = {
                'crop_height': 0.0,
                'total_points': 0,
                'ground_points': 0,
                'crop_points': 0,
                'processing_time': time.time() - start_time,
                'status': f'error: {str(e)}',
                'result_file': None
            }
            print(f"高度识别失败: {str(e)}")
            return error_result
    
    def generate_visualization(
        self,
        input_file: Optional[str] = None,
        output_formats: List[str] = ['html', 'jpg', 'gif'],
        output_dir: str = "output",
        sample_size: int = 20000
    ) -> Dict[str, Union[List[str], str, float]]:
        """
        群落图生成接口
        
        Args:
            input_file (Optional[str]): 输入文件路径，如果为None则使用之前处理的数据
            output_formats (List[str]): 输出格式列表，可选: ['html', 'jpg', 'gif']
            output_dir (str): 输出目录
            sample_size (int): 可视化采样点数
            
        Returns:
            Dict: 包含生成结果的字典
            {
                'generated_files': List[str],  # 生成的文件列表
                'crop_height': float,          # 作物高度
                'processing_time': float,      # 处理时间
                'status': str                  # 处理状态
            }
        """
        import time
        start_time = time.time()
        
        try:
            print(f"开始生成群落可视化图...")
            print(f"输出格式: {', '.join(output_formats)}")
            
            # 确保输出目录存在
            os.makedirs(output_dir, exist_ok=True)
            
            # 如果提供了新的输入文件，重新处理
            if input_file is not None:
                print(f"处理新的输入文件: {input_file}")
                result = self.height_recognition(input_file, output_dir=output_dir)
                if result['status'] != 'success':
                    return {
                        'generated_files': [],
                        'crop_height': 0.0,
                        'processing_time': time.time() - start_time,
                        'status': f"数据处理失败: {result['status']}"
                    }
            
            # 检查是否有处理过的数据
            if self.transformed_data is None or self.crop_height is None:
                return {
                    'generated_files': [],
                    'crop_height': 0.0,
                    'processing_time': time.time() - start_time,
                    'status': 'error: 没有可用的处理数据，请先运行height_recognition或提供input_file'
                }
            
            generated_files = []
            
            # 生成HTML可视化
            if 'html' in output_formats:
                html_file = os.path.join(output_dir, "visualization.html")
                print(f"生成HTML可视化: {html_file}")
                visualize_point_cloud(
                    self.transformed_data, 
                    html_file, 
                    sample_size=sample_size, 
                    crop_height=self.crop_height
                )
                generated_files.append(html_file)
            
            # 生成JPG图像
            if 'jpg' in output_formats:
                jpg_file = os.path.join(output_dir, "visualization.jpg")
                print(f"生成JPG图像: {jpg_file}")
                self._generate_static_image(jpg_file, 'jpg', sample_size)
                if os.path.exists(jpg_file):
                    generated_files.append(jpg_file)
            
            # 生成GIF动画
            if 'gif' in output_formats:
                gif_file = os.path.join(output_dir, "visualization.gif")
                print(f"生成GIF动画: {gif_file}")
                self._generate_static_image(gif_file, 'gif', sample_size)
                if os.path.exists(gif_file):
                    generated_files.append(gif_file)
            
            result = {
                'generated_files': generated_files,
                'crop_height': float(self.crop_height),
                'processing_time': time.time() - start_time,
                'status': 'success'
            }
            
            print(f"可视化生成完成! 生成了 {len(generated_files)} 个文件")
            print(f"生成文件: {', '.join(generated_files)}")
            
            return result
            
        except Exception as e:
            error_result = {
                'generated_files': [],
                'crop_height': 0.0,
                'processing_time': time.time() - start_time,
                'status': f'error: {str(e)}'
            }
            print(f"可视化生成失败: {str(e)}")
            return error_result
    
    def _crop_recognition_area(self, point_cloud_df: pd.DataFrame, area_size: float) -> pd.DataFrame:
        """
        裁剪识别区域到指定大小的正方形区域
        
        Args:
            point_cloud_df: 点云数据
            area_size: 区域边长(米)
            
        Returns:
            裁剪后的点云数据
        """
        # 计算点云的中心点
        x_center = (point_cloud_df['X'].min() + point_cloud_df['X'].max()) / 2
        y_center = (point_cloud_df['Y'].min() + point_cloud_df['Y'].max()) / 2
        
        # 计算裁剪边界
        half_size = area_size / 2
        x_min = x_center - half_size
        x_max = x_center + half_size
        y_min = y_center - half_size
        y_max = y_center + half_size
        
        # 裁剪数据
        mask = (
            (point_cloud_df['X'] >= x_min) & (point_cloud_df['X'] <= x_max) &
            (point_cloud_df['Y'] >= y_min) & (point_cloud_df['Y'] <= y_max)
        )
        
        return point_cloud_df[mask].copy()
    
    def _generate_static_image(self, output_file: str, format_type: str, sample_size: int = 20000):
        """
        生成静态图像(JPG/GIF)
        
        Args:
            output_file: 输出文件路径
            format_type: 格式类型 ('jpg' 或 'gif')
            sample_size: 采样点数
        """
        try:
            # 使用Plotly生成图像
            if format_type == 'jpg':
                from modules.visualization import create_plotly_static_image
                create_plotly_static_image(
                    self.transformed_data, 
                    output_file, 
                    crop_height=self.crop_height,
                    sample_size=sample_size  # 使用传入的采样大小
                )
            elif format_type == 'gif':
                from modules.visualization import create_plotly_rotating_gif
                create_plotly_rotating_gif(
                    self.transformed_data, 
                    output_file, 
                    crop_height=self.crop_height,
                    frames=6,  # 优化为6帧
                    sample_size=sample_size  # 使用传入的采样大小
                )
            
        except Exception as e:
            print(f"生成{format_type.upper()}失败: {str(e)}")
            import traceback
            traceback.print_exc()


# 便捷函数接口
def recognize_crop_height(
    input_file: str,
    recognition_area_size: Optional[float] = None,
    output_dir: str = "output"
) -> Dict[str, Union[float, int, str]]:
    """
    便捷的作物高度识别函数
    
    Args:
        input_file: 输入点云文件路径
        recognition_area_size: 识别区域边长(米)，None表示全部区域
        output_dir: 输出目录
        
    Returns:
        识别结果字典
    """
    analyzer = PointCloudAnalyzer()
    return analyzer.height_recognition(input_file, recognition_area_size, output_dir)


def generate_point_cloud_visualization(
    input_file: Optional[str] = None,
    output_formats: List[str] = ['html', 'jpg', 'gif'],
    output_dir: str = "output",
    sample_size: int = 20000
) -> Dict[str, Union[List[str], str, float]]:
    """
    便捷的群落图生成函数
    
    Args:
        input_file: 输入文件路径，None表示使用之前处理的数据
        output_formats: 输出格式列表
        output_dir: 输出目录
        sample_size: 采样点数
        
    Returns:
        生成结果字典
    """
    analyzer = PointCloudAnalyzer()
    return analyzer.generate_visualization(input_file, output_formats, output_dir, sample_size)


if __name__ == "__main__":
    # 示例用法
    import sys
    
    if len(sys.argv) < 2:
        print("用法示例:")
        print("python point_cloud_api.py <input_file> [recognition_area_size]")
        print("")
        print("示例:")
        print("python point_cloud_api.py data.csv")
        print("python point_cloud_api.py data.csv 10.0")
        sys.exit(1)
    
    input_file = sys.argv[1]
    recognition_area_size = float(sys.argv[2]) if len(sys.argv) > 2 else None
    
    # 创建分析器
    analyzer = PointCloudAnalyzer()
    
    # 执行高度识别
    print("=== 执行作物高度识别 ===")
    height_result = analyzer.height_recognition(input_file, recognition_area_size)
    print(f"识别结果: {height_result}")
    
    # 生成可视化
    print("\n=== 生成群落可视化图 ===")
    viz_result = analyzer.generate_visualization(output_formats=['html', 'jpg', 'gif'])
    print(f"可视化结果: {viz_result}") 