#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Sensor pose estimation module
"""

import numpy as np
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

def estimate_sensor_pose(point_cloud_df):
    """
    Estimate sensor pose (orientation and position) from point cloud data
    
    Args:
        point_cloud_df (pandas.DataFrame): DataFrame containing point cloud data
        
    Returns:
        tuple: (rotation_matrix, translation_vector)
    """
    print("Estimating sensor pose...")
    
    # Extract XYZ coordinates
    points = point_cloud_df[['X', 'Y', 'Z']].values
    
    # Step 1: Use PCA to find the principal directions in the point cloud
    # This helps identify the ground plane orientation
    pca = PCA(n_components=3)
    pca.fit(points)
    
    # The eigenvectors (principal components) give us the main directions
    eigenvectors = pca.components_
    
    # Step 2: Determine which principal component corresponds to the vertical direction
    # Typically, in outdoor scenes, the smallest eigenvalue corresponds to the normal of the ground plane
    eigenvalues = pca.explained_variance_
    print(f"PCA eigenvalues: {eigenvalues}")
    
    # Sort indices by eigenvalue (ascending)
    sorted_indices = np.argsort(eigenvalues)
    
    # The eigenvector with the smallest eigenvalue is assumed to be the ground plane normal
    ground_normal = eigenvectors[sorted_indices[0]]
    
    # Make sure the normal points upward (positive Z)
    if ground_normal[2] < 0:
        ground_normal = -ground_normal
    
    print(f"Estimated ground plane normal: {ground_normal}")
    
    # Step 3: Calculate rotation matrix that aligns the ground normal with [0, 0, 1]
    # (the Z axis in the target coordinate system)
    target_normal = np.array([0, 0, 1])
    
    # Using Rodrigues' rotation formula to find rotation matrix
    rotation_matrix = rotation_matrix_from_vectors(ground_normal, target_normal)
    
    # Step 4: Estimate the translation (sensor height)
    # Find the lowest point in the transformed point cloud as an estimate of the ground level
    transformed_points = np.dot(points, rotation_matrix.T)
    min_z = np.min(transformed_points[:, 2])
    
    # Translation vector to move the ground plane to Z=0
    translation_vector = np.array([0, 0, -min_z])
    
    print(f"Estimated rotation matrix:\n{rotation_matrix}")
    print(f"Estimated translation vector: {translation_vector}")
    
    # Optionally, visualize the transformation for debugging
    # visualize_transformation(points, rotation_matrix, translation_vector)
    
    return rotation_matrix, translation_vector

def rotation_matrix_from_vectors(vec1, vec2):
    """
    Calculate the rotation matrix that rotates vec1 to vec2
    
    Args:
        vec1 (numpy.ndarray): Source vector
        vec2 (numpy.ndarray): Target vector
        
    Returns:
        numpy.ndarray: 3x3 rotation matrix
    """
    # Normalize input vectors
    vec1 = vec1 / np.linalg.norm(vec1)
    vec2 = vec2 / np.linalg.norm(vec2)
    
    # Compute the cross product
    cross_product = np.cross(vec1, vec2)
    
    # Compute the dot product
    dot_product = np.dot(vec1, vec2)
    
    # Compute the skew-symmetric matrix
    skew_symmetric = np.array([
        [0, -cross_product[2], cross_product[1]],
        [cross_product[2], 0, -cross_product[0]],
        [-cross_product[1], cross_product[0], 0]
    ])
    
    # Compute the rotation matrix using Rodrigues' rotation formula
    rotation_matrix = np.eye(3) + skew_symmetric + np.dot(skew_symmetric, skew_symmetric) * (1 - dot_product) / (np.linalg.norm(cross_product) ** 2 + 1e-10)
    
    return rotation_matrix

def visualize_transformation(points, rotation_matrix, translation_vector, sample_size=1000):
    """
    Visualize the original and transformed point clouds
    
    Args:
        points (numpy.ndarray): Original points
        rotation_matrix (numpy.ndarray): Rotation matrix
        translation_vector (numpy.ndarray): Translation vector
        sample_size (int): Number of points to sample for visualization
    """
    # Sample points for visualization
    if len(points) > sample_size:
        indices = np.random.choice(len(points), sample_size, replace=False)
        sampled_points = points[indices]
    else:
        sampled_points = points
    
    # Transform points
    transformed_points = np.dot(sampled_points, rotation_matrix.T) + translation_vector
    
    # Create 3D plot
    fig = plt.figure(figsize=(12, 6))
    
    # Original point cloud
    ax1 = fig.add_subplot(121, projection='3d')
    ax1.scatter(sampled_points[:, 0], sampled_points[:, 1], sampled_points[:, 2], s=1, c=sampled_points[:, 2], cmap='viridis')
    ax1.set_title('Original Point Cloud')
    ax1.set_xlabel('X')
    ax1.set_ylabel('Y')
    ax1.set_zlabel('Z')
    
    # Transformed point cloud
    ax2 = fig.add_subplot(122, projection='3d')
    ax2.scatter(transformed_points[:, 0], transformed_points[:, 1], transformed_points[:, 2], s=1, c=transformed_points[:, 2], cmap='viridis')
    ax2.set_title('Transformed Point Cloud')
    ax2.set_xlabel('X')
    ax2.set_ylabel('Y')
    ax2.set_zlabel('Z')
    
    plt.tight_layout()
    plt.savefig('transformation_visualization.png', dpi=300)
    plt.close() 