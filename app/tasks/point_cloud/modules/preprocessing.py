#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Preprocessing module for point cloud data
"""

import numpy as np
import pandas as pd
from tqdm import tqdm
from scipy import stats

# 设置固定随机种子确保每次结果一致
np.random.seed(42)

def preprocess_point_cloud(point_cloud_df):
    """
    Preprocess point cloud data by filtering out noise and outliers
    
    Args:
        point_cloud_df (pandas.DataFrame): DataFrame containing point cloud data
        
    Returns:
        pandas.DataFrame: Filtered point cloud data
    """
    print(f"Preprocessing point cloud with {len(point_cloud_df)} points...")
    
    # Make a copy to avoid modifying the original
    df = point_cloud_df.copy()
    
    # Step 1: Remove points with NaN values
    initial_count = len(df)
    df = df.dropna(subset=['X', 'Y', 'Z'])
    print(f"Removed {initial_count - len(df)} points with NaN values")
    
    # Step 2: Remove statistical outliers using z-score
    # This removes points that are far from the mean in terms of distance
    df_xyz = df[['X', 'Y', 'Z']]
    
    # Calculate Euclidean distance from the mean point
    mean_point = df_xyz.mean().values
    distances = np.sqrt(np.sum((df_xyz.values - mean_point) ** 2, axis=1))
    
    # Calculate z-scores for distances
    z_scores = stats.zscore(distances)
    
    # Filter points with z-score less than threshold (e.g., 3)
    threshold = 3.0
    outlier_mask = np.abs(z_scores) < threshold
    df_filtered = df[outlier_mask]
    
    print(f"Removed {len(df) - len(df_filtered)} statistical outliers (z-score > {threshold})")
    
    # Step 3: Spatial subsampling using a grid-based approach
    # This is an alternative to voxel grid filtering for very dense point clouds
    if len(df_filtered) > 100000:
        df_filtered = spatial_subsample(df_filtered, grid_size=0.1)
        print(f"Performed spatial subsampling to reduce point density")
    
    print(f"Final preprocessed point cloud has {len(df_filtered)} points")
    
    return df_filtered

def spatial_subsample(df, grid_size=0.1):
    """
    Subsample points spatially by taking one point from each grid cell
    
    Args:
        df (pandas.DataFrame): Input point cloud DataFrame
        grid_size (float): Size of grid cells for subsampling
        
    Returns:
        pandas.DataFrame: Subsampled point cloud
    """
    # Create grid cell indices
    df = df.copy()
    df['grid_x'] = (df['X'] / grid_size).astype(int)
    df['grid_y'] = (df['Y'] / grid_size).astype(int)
    df['grid_z'] = (df['Z'] / grid_size).astype(int)
    
    # Create a grid cell ID
    df['grid_id'] = (df['grid_x'].astype(str) + "_" + 
                     df['grid_y'].astype(str) + "_" + 
                     df['grid_z'].astype(str))
    
    # Take the first point from each grid cell
    subsampled = df.drop_duplicates(subset=['grid_id'])
    
    # Remove temporary grid columns
    subsampled = subsampled.drop(columns=['grid_x', 'grid_y', 'grid_z', 'grid_id'])
    
    print(f"Subsampled from {len(df)} to {len(subsampled)} points")
    
    return subsampled 