#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Ground plane fitting module
"""

import numpy as np
from sklearn.linear_model import RANSACRegressor
from sklearn.preprocessing import PolynomialFeatures
from sklearn.pipeline import make_pipeline
from sklearn.linear_model import LinearRegression
import pandas as pd

def fit_ground_plane(point_cloud_df, rotation_matrix, translation_vector):
    """
    Fit a ground plane to the point cloud and transform points to align with XOY plane
    
    Args:
        point_cloud_df (pandas.DataFrame): DataFrame containing point cloud data
        rotation_matrix (numpy.ndarray): Rotation matrix from pose estimation
        translation_vector (numpy.ndarray): Translation vector from pose estimation
        
    Returns:
        tuple: (transformed_point_cloud_df, ground_plane_params)
    """
    print("Fitting ground plane and transforming point cloud...")
    
    # Extract points
    points = point_cloud_df[['X', 'Y', 'Z']].values
    
    # Apply rotation from pose estimation
    rotated_points = np.dot(points, rotation_matrix.T)
    
    # Step 1: Find ground points using RANSAC
    # RANSAC is robust to outliers and can identify the dominant plane
    ground_points, ground_indices = identify_ground_points(rotated_points)
    
    print(f"Identified {len(ground_points)} ground points out of {len(rotated_points)} total points")
    
    # Step 2: Fit a precise plane to the ground points
    ground_plane_params = fit_plane_to_points(ground_points)
    print(f"Ground plane equation: {ground_plane_params[0]:.4f}x + {ground_plane_params[1]:.4f}y + {ground_plane_params[2]:.4f}z + {ground_plane_params[3]:.4f} = 0")
    
    # Step 3: Calculate rotation to align ground plane with XOY plane
    # The normal vector of the ground plane is [a, b, c] from ax + by + cz + d = 0
    ground_normal = np.array([ground_plane_params[0], ground_plane_params[1], ground_plane_params[2]])
    ground_normal = ground_normal / np.linalg.norm(ground_normal)
    
    # Make sure the normal points upward (positive Z)
    if ground_normal[2] < 0:
        ground_normal = -ground_normal
    
    print(f"Refined ground plane normal: {ground_normal}")
    
    # Target normal is [0, 0, 1] (Z-axis)
    target_normal = np.array([0, 0, 1])
    
    # Calculate fine rotation matrix
    fine_rotation_matrix = calculate_rotation_matrix(ground_normal, target_normal)
    
    # Apply fine rotation
    final_rotated_points = np.dot(rotated_points, fine_rotation_matrix.T)
    
    # Step 4: Calculate Z-offset to set ground plane at Z=0
    # Find the average Z value of ground points after rotation
    final_ground_points = final_rotated_points[ground_indices]
    z_offset = np.median(final_ground_points[:, 2])
    
    # Apply Z translation to set ground to Z=0
    final_translation = np.array([0, 0, -z_offset])
    transformed_points = final_rotated_points + final_translation
    
    # Create a new DataFrame with transformed points
    transformed_df = point_cloud_df.copy()
    transformed_df[['X', 'Y', 'Z']] = transformed_points
    
    # Add transformation info to the DataFrame for reference
    transformed_df['OriginalX'] = points[:, 0]
    transformed_df['OriginalY'] = points[:, 1]
    transformed_df['OriginalZ'] = points[:, 2]
    
    # Calculate and update ground plane parameters in the new coordinate system
    # In the transformed system, the ground plane equation should be z = 0
    transformed_ground_plane_params = np.array([0, 0, 1, 0])
    
    return transformed_df, transformed_ground_plane_params

def identify_ground_points(points, max_distance=0.1, min_points=100):
    """
    Identify points that belong to the ground plane using RANSAC
    
    Args:
        points (numpy.ndarray): 3D points
        max_distance (float): Maximum distance from a point to the ground plane to be considered part of it
        min_points (int): Minimum number of points required to fit the ground plane
        
    Returns:
        tuple: (ground_points, ground_indices)
    """
    # Extract X, Y as features and Z as target for plane fitting
    X = points[:, :2]  # X, Y coordinates
    y = points[:, 2]   # Z coordinates
    
    # Create a RANSAC regressor
    ransac = RANSACRegressor(
        base_estimator=make_pipeline(
            PolynomialFeatures(degree=1),
            LinearRegression()
        ),
        max_trials=100,
        min_samples=min_points,
        residual_threshold=max_distance,
        random_state=42
    )
    
    # Fit RANSAC
    ransac.fit(X, y)
    
    # Get inlier mask
    inlier_mask = ransac.inlier_mask_
    
    # Extract ground points and their indices
    ground_points = points[inlier_mask]
    ground_indices = np.where(inlier_mask)[0]
    
    return ground_points, ground_indices

def fit_plane_to_points(points):
    """
    Fit a plane to 3D points using least squares
    
    Args:
        points (numpy.ndarray): 3D points
        
    Returns:
        numpy.ndarray: Plane parameters [a, b, c, d] for equation ax + by + cz + d = 0
    """
    # Add a column of ones for the constant term
    A = np.column_stack((points[:, 0], points[:, 1], np.ones(points.shape[0])))
    b = points[:, 2]
    
    # Solve the system of equations
    x, residuals, rank, s = np.linalg.lstsq(A, b, rcond=None)
    
    # Extract plane parameters
    a, b, c = -x[0], -x[1], 1.0
    d = -x[2]
    
    # Normalize
    norm = np.sqrt(a*a + b*b + c*c)
    plane_params = np.array([a/norm, b/norm, c/norm, d/norm])
    
    return plane_params

def calculate_rotation_matrix(source_normal, target_normal):
    """
    Calculate rotation matrix to align source_normal with target_normal
    
    Args:
        source_normal (numpy.ndarray): Source normal vector
        target_normal (numpy.ndarray): Target normal vector
        
    Returns:
        numpy.ndarray: 3x3 rotation matrix
    """
    # Normalize vectors
    source_normal = source_normal / np.linalg.norm(source_normal)
    target_normal = target_normal / np.linalg.norm(target_normal)
    
    # Get rotation axis (cross product)
    rotation_axis = np.cross(source_normal, target_normal)
    
    # If vectors are parallel, rotation axis might be zero
    if np.allclose(rotation_axis, 0):
        # If vectors are parallel and in same direction, no rotation needed
        if np.dot(source_normal, target_normal) > 0:
            return np.eye(3)
        # If vectors are antiparallel, rotate 180 degrees around any perpendicular axis
        else:
            # Find a perpendicular vector
            if not np.allclose(source_normal[:2], 0):
                perp = np.array([-source_normal[1], source_normal[0], 0])
            else:
                perp = np.array([0, -source_normal[2], source_normal[1]])
            perp = perp / np.linalg.norm(perp)
            
            # Create rotation matrix for 180 degree rotation
            c = -1  # cos(180°)
            s = 0   # sin(180°)
            rotation_matrix = np.array([
                [c + (1-c)*perp[0]**2, (1-c)*perp[0]*perp[1], (1-c)*perp[0]*perp[2]],
                [(1-c)*perp[1]*perp[0], c + (1-c)*perp[1]**2, (1-c)*perp[1]*perp[2]],
                [(1-c)*perp[2]*perp[0], (1-c)*perp[2]*perp[1], c + (1-c)*perp[2]**2]
            ])
            return rotation_matrix
    
    # Normalize rotation axis
    rotation_axis = rotation_axis / np.linalg.norm(rotation_axis)
    
    # Calculate rotation angle
    cos_angle = np.dot(source_normal, target_normal)
    cos_angle = np.clip(cos_angle, -1.0, 1.0)  # Ensure cos_angle is in [-1, 1]
    angle = np.arccos(cos_angle)
    
    # Create rotation matrix using Rodrigues' rotation formula
    K = np.array([
        [0, -rotation_axis[2], rotation_axis[1]],
        [rotation_axis[2], 0, -rotation_axis[0]],
        [-rotation_axis[1], rotation_axis[0], 0]
    ])
    
    rotation_matrix = np.eye(3) + np.sin(angle) * K + (1 - np.cos(angle)) * np.dot(K, K)
    
    return rotation_matrix 