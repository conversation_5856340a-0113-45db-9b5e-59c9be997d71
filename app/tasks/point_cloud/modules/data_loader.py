#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Data loader module for point cloud processing
"""

import pandas as pd
import numpy as np
from tqdm import tqdm

# 设置固定随机种子确保每次结果一致
np.random.seed(42)

def load_point_cloud(file_path):
    """
    Load point cloud data from a CSV file
    
    Args:
        file_path (str): Path to the CSV file containing point cloud data
        
    Returns:
        pandas.DataFrame: DataFrame containing the point cloud data
    """
    print(f"Loading point cloud data from {file_path}...")
    
    # Read the CSV file
    try:
        # For large files, use chunking to avoid memory issues
        chunk_size = 100000  # Adjust based on available memory
        chunks = []
        
        # Use tqdm for progress bar
        with tqdm(desc="Reading CSV chunks") as pbar:
            for chunk in pd.read_csv(file_path, chunksize=chunk_size):
                chunks.append(chunk)
                pbar.update(1)
        
        df = pd.concat(chunks)
        
        # Check if X, Y, Z columns exist
        required_columns = ['X', 'Y', 'Z']
        for col in required_columns:
            if col not in df.columns:
                raise ValueError(f"Required column '{col}' not found in CSV file")
        
        print(f"Loaded {len(df)} points from {file_path}")
        print(f"Columns: {df.columns.tolist()}")
        
        # Display basic statistics
        print("\nPoint cloud statistics:")
        for col in required_columns:
            print(f"{col}: min={df[col].min():.3f}, max={df[col].max():.3f}, mean={df[col].mean():.3f}")
        
        return df
    
    except Exception as e:
        print(f"Error loading point cloud data: {e}")
        raise 