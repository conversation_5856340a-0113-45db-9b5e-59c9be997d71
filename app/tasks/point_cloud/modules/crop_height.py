#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Crop height calculation module
"""

import numpy as np
import pandas as pd
from sklearn.cluster import DBSCAN
from scipy.spatial import ConvexHull

# 设置固定随机种子确保每次结果一致
np.random.seed(42)

def calculate_crop_height(transformed_point_cloud_df):
    """
    Calculate crop height from transformed point cloud
    
    Args:
        transformed_point_cloud_df (pandas.DataFrame): DataFrame containing transformed point cloud data
        
    Returns:
        float: Estimated crop height in meters
    """
    print("Calculating crop height...")
    
    # 设置固定随机种子确保每次结果一致
    np.random.seed(42)
    
    # Extract XYZ coordinates
    points = transformed_point_cloud_df[['X', 'Y', 'Z']].values
    
    # Step 1: Filter out ground points (assuming ground is at Z ≈ 0)
    # We consider points with Z > ground_threshold as potential crop points
    ground_threshold = 0.05  # 5 cm above ground
    crop_points = points[points[:, 2] > ground_threshold]
    
    print(f"Found {len(crop_points)} points above ground threshold of {ground_threshold}m")
    
    # If no crop points found, return 0
    if len(crop_points) == 0:
        print("No crop points found above ground threshold")
        return 0.0
    
    # Step 2: Remove noise and outliers using DBSCAN clustering
    crop_clusters = cluster_crop_points(crop_points)
    
    # If no valid clusters found, use simple statistics
    if len(crop_clusters) == 0:
        # Use percentile to avoid extreme outliers
        crop_height = np.percentile(crop_points[:, 2], 95)
        print(f"No valid clusters found. Using 95th percentile for height: {crop_height:.3f}m")
        return crop_height
    
    # Step 3: Calculate height for each cluster
    cluster_heights = []
    
    for cluster in crop_clusters:
        if len(cluster) < 10:  # Skip very small clusters
            continue
        
        # Calculate the 95th percentile height for the cluster
        # (using 95th percentile instead of max to avoid outliers)
        cluster_height = np.percentile(cluster[:, 2], 95)
        cluster_heights.append(cluster_height)
        
        print(f"Cluster with {len(cluster)} points: height = {cluster_height:.3f}m")
    
    # Step 4: Calculate final crop height
    # Use the maximum cluster height as the crop height
    if cluster_heights:
        crop_height = max(cluster_heights)
    else:
        # Fallback to simple percentile if no clusters were large enough
        crop_height = np.percentile(crop_points[:, 2], 95)
    
    print(f"Final estimated crop height: {crop_height:.3f}m")
    
    return crop_height

def cluster_crop_points(crop_points, eps=0.1, min_samples=10):
    """
    Cluster crop points using DBSCAN to identify individual plants or plant groups
    
    Args:
        crop_points (numpy.ndarray): Array of crop points
        eps (float): DBSCAN epsilon parameter (maximum distance between points in a cluster)
        min_samples (int): DBSCAN min_samples parameter
        
    Returns:
        list: List of clusters (each cluster is a numpy array of points)
    """
    # 设置固定随机种子确保每次结果一致
    np.random.seed(42)
    
    # If there are too many points, sample them for clustering
    max_points_for_clustering = 10000
    if len(crop_points) > max_points_for_clustering:
        # 使用固定随机种子进行采样
        indices = np.random.choice(len(crop_points), max_points_for_clustering, replace=False)
        sample_points = crop_points[indices]
        print(f"Sampling {max_points_for_clustering} points for clustering")
    else:
        sample_points = crop_points
    
    # Apply DBSCAN clustering
    db = DBSCAN(eps=eps, min_samples=min_samples, n_jobs=1).fit(sample_points)  # 使用单线程确保一致性
    labels = db.labels_
    
    # Count number of clusters (excluding noise with label -1)
    n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
    print(f"DBSCAN found {n_clusters} clusters")
    
    # Group points by cluster
    clusters = []
    
    # If we sampled the points, we need to apply the clustering to all points
    if len(crop_points) > max_points_for_clustering:
        # Train a new DBSCAN with the cluster centers
        cluster_centers = []
        for i in range(n_clusters):
            cluster_points = sample_points[labels == i]
            cluster_centers.append(np.mean(cluster_points, axis=0))
        
        # Assign each point to the nearest cluster center
        if cluster_centers:
            from scipy.spatial import cKDTree
            tree = cKDTree(cluster_centers)
            distances, indices = tree.query(crop_points)
            
            # Create clusters
            for i in range(n_clusters):
                cluster_points = crop_points[indices == i]
                if len(cluster_points) > 0:
                    clusters.append(cluster_points)
    else:
        # Create clusters directly from DBSCAN results
        for i in range(n_clusters):
            cluster_points = sample_points[labels == i]
            if len(cluster_points) > 0:
                clusters.append(cluster_points)
    
    return clusters

def calculate_plant_metrics(crop_clusters):
    """
    Calculate additional metrics about plants from the clusters
    
    Args:
        crop_clusters (list): List of point clusters representing plants
        
    Returns:
        dict: Dictionary of plant metrics
    """
    metrics = {
        'num_plants': len(crop_clusters),
        'plant_heights': [],
        'plant_volumes': [],
        'plant_areas': []
    }
    
    for cluster in crop_clusters:
        # Skip very small clusters
        if len(cluster) < 10:
            continue
        
        # Height: 95th percentile of Z values
        height = np.percentile(cluster[:, 2], 95)
        metrics['plant_heights'].append(height)
        
        # Try to calculate approximate volume using convex hull
        try:
            hull = ConvexHull(cluster)
            volume = hull.volume
            metrics['plant_volumes'].append(volume)
            
            # Calculate approximate ground area
            # Project points to the XY plane and calculate area
            xy_points = cluster[:, :2]
            xy_hull = ConvexHull(xy_points)
            area = xy_hull.volume  # In 2D, "volume" is actually area
            metrics['plant_areas'].append(area)
        except Exception:
            # If convex hull fails (e.g., not enough points), skip these metrics
            pass
    
    # Calculate averages
    if metrics['plant_heights']:
        metrics['avg_height'] = np.mean(metrics['plant_heights'])
        metrics['max_height'] = np.max(metrics['plant_heights'])
    else:
        metrics['avg_height'] = 0.0
        metrics['max_height'] = 0.0
        
    if metrics['plant_volumes']:
        metrics['avg_volume'] = np.mean(metrics['plant_volumes'])
    else:
        metrics['avg_volume'] = 0.0
        
    if metrics['plant_areas']:
        metrics['avg_area'] = np.mean(metrics['plant_areas'])
        metrics['total_area'] = np.sum(metrics['plant_areas'])
    else:
        metrics['avg_area'] = 0.0
        metrics['total_area'] = 0.0
    
    return metrics 