#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Visualization module for point cloud data using Plotly
"""

import numpy as np
import pandas as pd
import plotly.graph_objects as go
import plotly.io as pio
import os
import imageio
import warnings
warnings.filterwarnings('ignore')

# 设置固定随机种子确保每次结果一致
np.random.seed(42)

# 设置Plotly的默认渲染器 - 服务器环境使用静态渲染
try:
    # 在服务器环境中，不使用浏览器渲染器
    pio.renderers.default = "png"
except:
    # 如果设置失败，使用默认设置
    pass

# 设置字体配置，解决中文乱码问题
import plotly.io as pio
pio.kaleido.scope.mathjax = None

# 特殊配置Kaleido引擎支持中文
try:
    # 设置Kaleido使用系统字体
    import plotly.io.kaleido
    # 强制刷新字体缓存
    pio.kaleido.scope._shutdown()
    pio.kaleido.scope.default_format = "png"
    pio.kaleido.scope.default_engine = "kaleido"
except:
    pass

# 配置支持中文的字体设置
def get_chinese_font_config():
    """
    获取支持中文的字体配置
    
    Returns:
        str: 字体配置字符串
    """
    # Linux系统优先使用已安装的文泉驿字体
    font_families = [
        'WenQuanYi Micro Hei',  # 文泉驿微米黑 (已安装)
        'WenQuanYi Zen Hei',    # 文泉驿正黑 (已安装)
        'Droid Sans Fallback',  # Android字体 (已安装)
        'SimHei',               # 黑体 (Windows)
        'Microsoft YaHei',      # 微软雅黑 (Windows)
        'Noto Sans CJK SC',     # Google Noto字体
        'Source Han Sans SC',   # 思源黑体
        'PingFang SC',          # 苹果系统字体 (macOS)
        'Arial Unicode MS',     # 包含中文的Arial
        'DejaVu Sans',          # 备用字体
        'sans-serif'            # 系统默认字体
    ]
    
    return ', '.join(font_families)

# 全局字体配置
CHINESE_FONT = get_chinese_font_config()

# 配置matplotlib后端以支持中文（用于备用方案）
def setup_chinese_matplotlib():
    """设置matplotlib支持中文"""
    try:
        import matplotlib
        matplotlib.use('Agg')  # 使用非交互式后端
        import matplotlib.pyplot as plt
        import matplotlib.font_manager as fm
        
        # 寻找系统中的中文字体
        chinese_fonts = []
        for font in fm.fontManager.ttflist:
            if 'WenQuanYi' in font.name or 'SimHei' in font.name or 'Microsoft YaHei' in font.name:
                chinese_fonts.append(font.name)
        
        if chinese_fonts:
            plt.rcParams['font.sans-serif'] = chinese_fonts
            plt.rcParams['axes.unicode_minus'] = False
            print(f"设置matplotlib中文字体: {chinese_fonts[0]}")
        
    except ImportError:
        print("matplotlib未安装，跳过字体配置")
    except Exception as e:
        print(f"设置matplotlib中文字体失败: {e}")

# 初始化中文字体支持
setup_chinese_matplotlib()

def create_base_figure(point_cloud_df, crop_height=None, sample_size=50000):
    """
    创建基础的Plotly 3D图形
    
    Args:
        point_cloud_df (pandas.DataFrame): DataFrame containing point cloud data
        crop_height (float): Calculated crop height to display
        sample_size (int): Number of points to sample for visualization
        
    Returns:
        plotly.graph_objects.Figure: 配置好的Plotly图形对象
    """
    # 增加采样点数，提高点云细粒度
    if len(point_cloud_df) > sample_size:
        # 使用固定种子进行采样，确保每次结果一致
        sampled_df = point_cloud_df.sample(sample_size, random_state=42)
    else:
        sampled_df = point_cloud_df
    
    # Extract coordinates
    x = sampled_df['X'].values
    y = sampled_df['Y'].values
    z = sampled_df['Z'].values
    
    # Create figure
    fig = go.Figure()
    
    # Define color scheme based on height (Z coordinate)
    ground_threshold = 0.05
    ground_mask = z <= ground_threshold
    
    # Filter crop points based on detected crop height
    if crop_height is not None and crop_height > 0:
        max_reasonable_height = min(crop_height * 1.5, crop_height + 0.3)
        crop_mask = (z > ground_threshold) & (z <= max_reasonable_height)
        
        # Count filtered points for reporting
        total_above_ground = np.sum(z > ground_threshold)
        filtered_crop_points = np.sum(crop_mask)
        excluded_points = total_above_ground - filtered_crop_points
        
        if excluded_points > 0:
            print(f"过滤掉 {excluded_points} 个异常高度点 (高于 {max_reasonable_height:.3f}m)")
            print(f"显示 {filtered_crop_points} 个合理范围内的作物点")
    else:
        crop_mask = z > ground_threshold
    
    # Add ground points - 使用更小的点尺寸提高精细度
    if np.any(ground_mask):
        fig.add_trace(
            go.Scatter3d(
                x=x[ground_mask],
                y=y[ground_mask],
                z=z[ground_mask],
                mode='markers',
                marker=dict(
                    size=1.2,  # 减小点尺寸
                    color='#8B4513',  # 棕色
                    opacity=0.7,
                    symbol='circle'
                ),
                name='Ground Points',  # 使用英文避免乱码
                legendgroup="ground",
                showlegend=True
            )
        )
    
    # Add crop points with darker green colors (filtered based on crop height)
    if np.any(crop_mask):
        # Create custom darker green colorscale
        custom_colorscale = [
            [0.0, '#1B5E20'],    # 深绿色 (Dark Green)
            [0.2, '#2E7D32'],    # 较深绿色
            [0.4, '#388E3C'],    # 中等绿色
            [0.6, '#43A047'],    # 较亮绿色
            [0.8, '#4CAF50'],    # 亮绿色
            [1.0, '#66BB6A']     # 最亮绿色
        ]
        
        fig.add_trace(
            go.Scatter3d(
                x=x[crop_mask],
                y=y[crop_mask],
                z=z[crop_mask],
                mode='markers',
                marker=dict(
                    size=1.5,  # 减小点尺寸
                    color=z[crop_mask],  # Color based on height
                    colorscale=custom_colorscale,
                    opacity=0.9,
                    colorbar=dict(
                        title=dict(
                            text='Height (m)',  # 使用英文避免乱码
                            font=dict(size=14, color='#2F4F4F', family=CHINESE_FONT)
                        ),
                        thickness=20,
                        len=0.7,
                        x=1.02,
                        xpad=10,
                        tickfont=dict(size=12, color='#2F4F4F', family=CHINESE_FONT),
                        bgcolor='rgba(255, 255, 255, 0.8)',
                        bordercolor='#2F4F4F',
                        borderwidth=1
                    ),
                    symbol='circle'
                ),
                name='Crop Points',  # 使用英文避免乱码
                legendgroup="crop",
                showlegend=True
            )
        )
    
    return fig

def visualize_point_cloud(point_cloud_df, output_file, sample_size=50000, crop_height=None):
    """
    Create an interactive 3D visualization of the point cloud using Plotly
    
    Args:
        point_cloud_df (pandas.DataFrame): DataFrame containing point cloud data
        output_file (str): Path to save the HTML visualization
        sample_size (int): Number of points to sample for visualization
        crop_height (float): Calculated crop height to display
    """
    print(f"Generating 3D visualization with {sample_size} sample points...")
    
    # Create base figure
    fig = create_base_figure(point_cloud_df, crop_height, sample_size)
    
    # Add coordinate system axes for reference
    axis_length = 1.0
    
    # X-axis (red)
    fig.add_trace(
        go.Scatter3d(
            x=[0, axis_length],
            y=[0, 0],
            z=[0, 0],
            mode='lines+text',
            line=dict(color='#DC143C', width=6),
            text=['', 'X'],
            textposition='middle right',
            textfont=dict(size=14, color='#DC143C', family=CHINESE_FONT),
            name='X轴',
            legendgroup="axes",
            showlegend=True
        )
    )
    
    # Y-axis (green)
    fig.add_trace(
        go.Scatter3d(
            x=[0, 0],
            y=[0, axis_length],
            z=[0, 0],
            mode='lines+text',
            line=dict(color='#228B22', width=6),
            text=['', 'Y'],
            textposition='middle right',
            textfont=dict(size=14, color='#228B22', family=CHINESE_FONT),
            name='Y轴',
            legendgroup="axes",
            showlegend=True
        )
    )
    
    # Z-axis (blue)
    fig.add_trace(
        go.Scatter3d(
            x=[0, 0],
            y=[0, 0],
            z=[0, axis_length],
            mode='lines+text',
            line=dict(color='#4169E1', width=6),
            text=['', 'Z'],
            textposition='top center',
            textfont=dict(size=14, color='#4169E1', family=CHINESE_FONT),
            name='Z轴',
            legendgroup="axes",
            showlegend=True
        )
    )
    
    # Set layout with improved styling - 使用支持中文的字体
    fig.update_layout(
        title=dict(
            text='群落三维可视化分析',  # 中文标题
            x=0.5,
            xanchor='center',
            font=dict(size=22, color='#2F4F4F', family=CHINESE_FONT),
            pad=dict(t=20, b=20)
        ),
        scene=dict(
            xaxis=dict(
                title='X坐标(米)', 
                titlefont=dict(size=14, family=CHINESE_FONT), 
                tickfont=dict(size=12, family=CHINESE_FONT),
                gridcolor='lightgray',
                showbackground=True,
                backgroundcolor='rgba(245, 245, 245, 0.8)'
            ),
            yaxis=dict(
                title='Y坐标(米)', 
                titlefont=dict(size=14, family=CHINESE_FONT), 
                tickfont=dict(size=12, family=CHINESE_FONT),
                gridcolor='lightgray',
                showbackground=True,
                backgroundcolor='rgba(245, 245, 245, 0.8)'
            ),
            zaxis=dict(
                title='Z坐标(米)', 
                titlefont=dict(size=14, family=CHINESE_FONT), 
                tickfont=dict(size=12, family=CHINESE_FONT),
                gridcolor='lightgray',
                showbackground=True,
                backgroundcolor='rgba(245, 245, 245, 0.8)'
            ),
            aspectmode='data',
            camera=dict(
                eye=dict(x=1.5, y=1.5, z=1.2)
            )
        ),
        legend=dict(
            orientation="v",
            yanchor="top",
            y=0.95,
            xanchor="left",
            x=0.02,
            font=dict(size=12, family=CHINESE_FONT),
            bgcolor='rgba(255, 255, 255, 0.9)',
            bordercolor='#2F4F4F',
            borderwidth=1
        ),
        margin=dict(l=20, r=20, b=20, t=60),
        paper_bgcolor='white',
        plot_bgcolor='white',
        showlegend=True
    )
    
    # Calculate and display statistics
    if crop_height is not None:
        display_height = crop_height
    else:
        display_height = 0.0
    
    fig.add_annotation(
        text=f"作物高度: {display_height:.3f} 米",  # 中文标注
        xref="paper", yref="paper",
        x=0.5, y=0.93,
        xanchor="center", yanchor="top",
        showarrow=False,
        font=dict(size=18, color='white', family=CHINESE_FONT),
        bgcolor='#32CD32',
        bordercolor='#228B22',
        borderwidth=3,
        borderpad=15,
        opacity=0.95
    )
    
    # 确保输出目录存在
    os.makedirs(os.path.dirname(os.path.abspath(output_file)), exist_ok=True)
    
    # 保存为HTML文件，使用完整的plotly.js库而不是CDN
    fig.write_html(
        output_file,
        include_plotlyjs=True,
        full_html=True,
        auto_open=False
    )
    
    print(f"Visualization saved to {output_file}")
    
    return fig

def create_plotly_static_image(point_cloud_df, output_file, crop_height=None, sample_size=40000):
    """
    使用Plotly内置接口生成静态图像 (JPG/PNG)
    
    Args:
        point_cloud_df (pandas.DataFrame): DataFrame containing point cloud data
        output_file (str): Path to save the image
        crop_height (float): Calculated crop height to display
        sample_size (int): Number of points to sample for visualization
    """
    print(f"Generating static image with {sample_size} sample points...")
    
    # Create base figure
    fig = create_base_figure(point_cloud_df, crop_height, sample_size)
    
    # Set layout optimized for static export
    fig.update_layout(
        title=dict(
            text='Point Cloud Visualization',  # 使用英文标题避免乱码
            x=0.5,
            font=dict(size=20, color='#2F4F4F', family=CHINESE_FONT)
        ),
        scene=dict(
            xaxis=dict(
                title='X (m)',  # 使用英文避免乱码
                titlefont=dict(size=12, family=CHINESE_FONT),
                tickfont=dict(size=10, family=CHINESE_FONT),
                gridcolor='lightgray',
                showbackground=True,
                backgroundcolor='rgba(245, 245, 245, 0.8)'
            ),
            yaxis=dict(
                title='Y (m)',  # 使用英文避免乱码
                titlefont=dict(size=12, family=CHINESE_FONT),
                tickfont=dict(size=10, family=CHINESE_FONT),
                gridcolor='lightgray',
                showbackground=True,
                backgroundcolor='rgba(245, 245, 245, 0.8)'
            ),
            zaxis=dict(
                title='Z (m)',  # 使用英文避免乱码
                titlefont=dict(size=12, family=CHINESE_FONT),
                tickfont=dict(size=10, family=CHINESE_FONT),
                gridcolor='lightgray',
                showbackground=True,
                backgroundcolor='rgba(245, 245, 245, 0.8)'
            ),
            aspectmode='data',
            camera=dict(
                eye=dict(x=1.5, y=1.5, z=1.2)
            )
        ),
        legend=dict(
            orientation="v",
            yanchor="top",
            y=0.95,
            xanchor="left",
            x=0.02,
            font=dict(size=10, family=CHINESE_FONT),
            bgcolor='rgba(255, 255, 255, 0.9)',
            bordercolor='#2F4F4F',
            borderwidth=1
        ),
        margin=dict(l=20, r=20, b=20, t=60),
        paper_bgcolor='white',
        plot_bgcolor='white',
        width=1200,
        height=900
    )
    
    # Add crop height annotation - 使用英文文本
    # if crop_height is not None:
    #     fig.add_annotation(
    #         text=f"Crop Height: {crop_height:.3f} m",  # 使用英文避免乱码
    #         xref="paper", yref="paper",
    #         x=0.5, y=0.95,
    #         xanchor="center", yanchor="top",
    #         showarrow=False,
    #         font=dict(size=16, color='white', family=CHINESE_FONT),
    #         bgcolor='#32CD32',
    #         bordercolor='#228B22',
    #         borderwidth=2,
    #         borderpad=10
    #     )
    
    # 使用特殊配置的Plotly引擎生成图像
    try:
        # 确定输出格式
        file_ext = os.path.splitext(output_file)[1].lower()
        if file_ext in ['.jpg', '.jpeg']:
            format_type = "jpeg"
        elif file_ext == '.png':
            format_type = "png"
        else:
            # 默认使用PNG格式，然后转换为JPG
            format_type = "png"
        
        # 强制设置字体环境变量
        os.environ['FONTCONFIG_PATH'] = '/etc/fonts'
        os.environ['FONTCONFIG_FILE'] = '/etc/fonts/fonts.conf'
        
        # 使用Plotly的write_image方法
        fig.write_image(
            output_file,
            format=format_type,
            width=1200,
            height=900,
            scale=1,  # 修改scale为1，确保与GIF尺寸一致
            engine="kaleido"
        )
        
        print(f"Plotly static image saved to {output_file}")
        
        # 如果原始格式是JPG但我们生成了PNG，进行转换
        if file_ext in ['.jpg', '.jpeg'] and format_type == "png":
            try:
                from PIL import Image
                png_file = output_file.replace('.jpg', '.png').replace('.jpeg', '.png')
                if os.path.exists(png_file):
                    # 转换PNG到JPG
                    img = Image.open(png_file)
                    # 转换为RGB模式（JPG不支持透明度）
                    if img.mode in ('RGBA', 'LA', 'P'):
                        img = img.convert('RGB')
                    img.save(output_file, 'JPEG', quality=95)
                    os.remove(png_file)  # 删除临时PNG文件
                    print(f"Converted PNG to JPG: {output_file}")
            except ImportError:
                print("PIL not available for PNG to JPG conversion, keeping PNG format")
            except Exception as e:
                print(f"Error converting PNG to JPG: {e}")
        
    except Exception as e:
        print(f"Error saving static image: {e}")
        print(f"Attempting fallback method...")
        # 尝试使用matplotlib作为备选方案
        try:
            create_matplotlib_fallback_image(point_cloud_df, output_file, crop_height, sample_size)
        except Exception as e2:
            print(f"Matplotlib fallback also failed: {e2}")

def create_matplotlib_fallback_image(point_cloud_df, output_file, crop_height=None, sample_size=40000):
    """
    使用matplotlib作为备选方案生成静态图像
    """
    try:
        import matplotlib.pyplot as plt
        from mpl_toolkits.mplot3d import Axes3D
        
        print("Using matplotlib fallback for image generation...")
        
        # Sample data
        if len(point_cloud_df) > sample_size:
            sampled_df = point_cloud_df.sample(sample_size, random_state=42)
        else:
            sampled_df = point_cloud_df
        
        x = sampled_df['X'].values
        y = sampled_df['Y'].values
        z = sampled_df['Z'].values
        
        # Create figure
        fig = plt.figure(figsize=(12, 9), dpi=100)
        ax = fig.add_subplot(111, projection='3d')
        
        # Define ground and crop points
        ground_threshold = 0.05
        ground_mask = z <= ground_threshold
        crop_mask = z > ground_threshold
        
        # Plot ground points
        if np.any(ground_mask):
            ax.scatter(x[ground_mask], y[ground_mask], z[ground_mask], 
                      c='#8B4513', s=1, alpha=0.7, label='Ground Points')
        
        # Plot crop points
        if np.any(crop_mask):
            scatter = ax.scatter(x[crop_mask], y[crop_mask], z[crop_mask], 
                               c=z[crop_mask], s=2, alpha=0.9, 
                               cmap='Greens', label='Crop Points')
            plt.colorbar(scatter, ax=ax, label='Height (m)', shrink=0.5)
        
        # Set labels and title
        ax.set_xlabel('X (m)')
        ax.set_ylabel('Y (m)')
        ax.set_zlabel('Z (m)')
        ax.set_title('Point Cloud Visualization')
        
        # Add crop height text
        if crop_height is not None:
            ax.text2D(0.5, 0.95, f"Crop Height: {crop_height:.3f} m", 
                     transform=ax.transAxes, ha='center', va='top',
                     bbox=dict(boxstyle="round,pad=0.5", facecolor='lightgreen', alpha=0.8))
        
        ax.legend()
        
        # Save image
        plt.tight_layout()
        plt.savefig(output_file, dpi=100, bbox_inches='tight')
        plt.close()
        
        print(f"Matplotlib fallback image saved to {output_file}")
        
    except Exception as e:
        print(f"Matplotlib fallback failed: {e}")
        raise

def create_plotly_rotating_gif(point_cloud_df, gif_file, crop_height=None, frames=6, sample_size=25000):
    """
    使用Plotly生成旋转GIF动画
    
    Args:
        point_cloud_df (pandas.DataFrame): DataFrame containing point cloud data
        gif_file (str): Path to save the GIF
        crop_height (float): Calculated crop height to display
        frames (int): Number of frames for 360-degree rotation
        sample_size (int): Number of points to sample for animation
    """
    print(f"Creating {frames} frame GIF with 360-degree rotation using Plotly...")
    
    # Create base figure
    fig = create_base_figure(point_cloud_df, crop_height, sample_size)
    
    # Set layout optimized for GIF export - 统一图像尺寸与JPG保持一致
    fig.update_layout(
        title=dict(
            text='Point Cloud Visualization',  # 使用英文标题避免乱码
            x=0.5,
            font=dict(size=20, color='#2F4F4F', family=CHINESE_FONT)  # 增大字体
        ),
        scene=dict(
            xaxis=dict(
                title='X (m)',  # 使用英文避免乱码
                titlefont=dict(size=12, family=CHINESE_FONT),  # 增大字体
                tickfont=dict(size=10, family=CHINESE_FONT),
                gridcolor='lightgray',
                showbackground=True,
                backgroundcolor='rgba(245, 245, 245, 0.8)'
            ),
            yaxis=dict(
                title='Y (m)',  # 使用英文避免乱码
                titlefont=dict(size=12, family=CHINESE_FONT),  # 增大字体
                tickfont=dict(size=10, family=CHINESE_FONT),
                gridcolor='lightgray',
                showbackground=True,
                backgroundcolor='rgba(245, 245, 245, 0.8)'
            ),
            zaxis=dict(
                title='Z (m)',  # 使用英文避免乱码
                titlefont=dict(size=12, family=CHINESE_FONT),  # 增大字体
                tickfont=dict(size=10, family=CHINESE_FONT),
                gridcolor='lightgray',
                showbackground=True,
                backgroundcolor='rgba(245, 245, 245, 0.8)'
            ),
            aspectmode='data'
        ),
        legend=dict(
            orientation="v",
            yanchor="top",
            y=0.95,
            xanchor="left",
            x=0.02,
            font=dict(size=10, family=CHINESE_FONT),
            bgcolor='rgba(255, 255, 255, 0.9)',
            bordercolor='#2F4F4F',
            borderwidth=1
        ),
        margin=dict(l=20, r=20, b=20, t=60),  # 增加边距，与JPG保持一致
        paper_bgcolor='white',
        plot_bgcolor='white',
        width=1200,  # 与JPG保持一致的尺寸
        height=900   # 与JPG保持一致的尺寸
    )
    
    # Add crop height annotation - 使用英文文本
    # if crop_height is not None:
    #     fig.add_annotation(
    #         text=f"Crop Height: {crop_height:.3f} m",  # 使用英文避免乱码
    #         xref="paper", yref="paper",
    #         x=0.5, y=0.95,
    #         xanchor="center", yanchor="top",
    #         showarrow=False,
    #         font=dict(size=14, color='white', family=CHINESE_FONT),  # 增大字体
    #         bgcolor='#32CD32',
    #         bordercolor='#228B22',
    #         borderwidth=2,
    #         borderpad=10
    #     )
    
    # 设置字体环境变量
    os.environ['FONTCONFIG_PATH'] = '/etc/fonts'
    os.environ['FONTCONFIG_FILE'] = '/etc/fonts/fonts.conf'
    
    # Generate frames with different camera angles using Plotly's write_image
    temp_files = []
    for frame in range(frames):
        # Calculate rotation angle for full 360-degree rotation
        azim_angle = frame * 360.0 / frames
        
        # Convert to radians for camera position calculation
        azim_rad = np.radians(azim_angle)
        elev_rad = np.radians(20)  # Fixed elevation
        
        # Calculate camera position
        radius = 2.5
        camera_x = radius * np.cos(elev_rad) * np.cos(azim_rad)
        camera_y = radius * np.cos(elev_rad) * np.sin(azim_rad)
        camera_z = radius * np.sin(elev_rad)
        
        # Update camera position
        fig.update_layout(
            scene_camera=dict(
                eye=dict(x=camera_x, y=camera_y, z=camera_z),
                center=dict(x=0, y=0, z=0),
                up=dict(x=0, y=0, z=1)
            )
        )
        
        # Save frame as PNG using Plotly's write_image (内置接口)
        temp_file = f"temp_gif_frame_{frame:03d}.png"
        try:
            fig.write_image(
                temp_file,
                format="png",
                width=1200,  # 与JPG保持一致的尺寸
                height=900,  # 与JPG保持一致的尺寸
                scale=1,
                engine="kaleido"
            )
            temp_files.append(temp_file)
            
            # Show progress
            print(f"  Generated frame {frame+1}/{frames} (angle: {azim_angle:.1f}°)")
                
        except Exception as e:
            print(f"Error generating frame {frame}: {e}")
            print(f"Attempting matplotlib fallback for frame {frame}...")
            try:
                create_matplotlib_gif_frame(point_cloud_df, temp_file, crop_height, sample_size, azim_angle)
                temp_files.append(temp_file)
                print(f"  Generated frame {frame+1}/{frames} using matplotlib (angle: {azim_angle:.1f}°)")
            except Exception as e2:
                print(f"Matplotlib fallback also failed for frame {frame}: {e2}")
                break
    
    # Create GIF from temp files
    if temp_files:
        print("Assembling GIF animation...")
        try:
            images = []
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    images.append(imageio.imread(temp_file))
                    os.remove(temp_file)  # Clean up immediately
            
            if images:
                # Save as GIF - 设置1秒间隔
                imageio.mimsave(
                    gif_file,
                    images,
                    duration=1.0,  # 1秒每帧
                    loop=0  # Infinite loop
                )
                print(f"GIF animation saved to {gif_file}")
                print(f"Animation: {len(images)} frames, 360° rotation, 1s per frame")
                print(f"GIF size: 1200x900 (consistent with JPG)")
            else:
                print("No valid frames generated for GIF")
                
        except Exception as e:
            print(f"Error creating GIF: {e}")
            # Clean up any remaining temp files
            for temp_file in temp_files:
                if os.path.exists(temp_file):
                    os.remove(temp_file)
    else:
        print("No frames generated for GIF animation")

def create_matplotlib_gif_frame(point_cloud_df, output_file, crop_height=None, sample_size=25000, angle=0):
    """
    使用matplotlib生成GIF帧作为备选方案
    """
    try:
        import matplotlib.pyplot as plt
        from mpl_toolkits.mplot3d import Axes3D
        
        # Sample data
        if len(point_cloud_df) > sample_size:
            sampled_df = point_cloud_df.sample(sample_size, random_state=42)
        else:
            sampled_df = point_cloud_df
        
        x = sampled_df['X'].values
        y = sampled_df['Y'].values
        z = sampled_df['Z'].values
        
        # Create figure with exact size
        fig = plt.figure(figsize=(12, 9), dpi=100)
        ax = fig.add_subplot(111, projection='3d')
        
        # Define ground and crop points
        ground_threshold = 0.05
        ground_mask = z <= ground_threshold
        crop_mask = z > ground_threshold
        
        # Plot ground points
        if np.any(ground_mask):
            ax.scatter(x[ground_mask], y[ground_mask], z[ground_mask], 
                      c='#8B4513', s=0.5, alpha=0.7, label='Ground')
        
        # Plot crop points
        if np.any(crop_mask):
            ax.scatter(x[crop_mask], y[crop_mask], z[crop_mask], 
                      c=z[crop_mask], s=1, alpha=0.9, cmap='Greens', label='Crop')
        
        # Set viewing angle
        ax.view_init(elev=20, azim=angle)
        
        # Set labels and title
        ax.set_xlabel('X (m)', fontsize=10)
        ax.set_ylabel('Y (m)', fontsize=10)
        ax.set_zlabel('Z (m)', fontsize=10)
        ax.set_title('Point Cloud Analysis', fontsize=16)
        
        # Add crop height text
        if crop_height is not None:
            ax.text2D(0.5, 0.95, f"Crop Height: {crop_height:.3f} m", 
                     transform=ax.transAxes, ha='center', va='top', fontsize=12,
                     bbox=dict(boxstyle="round,pad=0.3", facecolor='lightgreen', alpha=0.8))
        
        # Save frame
        plt.tight_layout()
        plt.savefig(output_file, dpi=100, bbox_inches='tight', 
                   facecolor='white', edgecolor='none')
        plt.close()
        
    except Exception as e:
        print(f"Matplotlib GIF frame generation failed: {e}")
        raise

def visualize_crops_and_ground(transformed_point_cloud_df, crop_clusters, ground_plane_params, output_file):
    """
    Create an advanced visualization showing crops and ground plane
    
    Args:
        transformed_point_cloud_df (pandas.DataFrame): DataFrame with transformed point cloud
        crop_clusters (list): List of crop point clusters
        ground_plane_params (numpy.ndarray): Ground plane parameters [a, b, c, d]
        output_file (str): Path to save the HTML visualization
    """
    # 设置固定随机种子确保每次结果一致
    np.random.seed(42)
    
    # Sample points for visualization - 增加采样数量
    sample_size = 30000
    if len(transformed_point_cloud_df) > sample_size:
        sampled_df = transformed_point_cloud_df.sample(sample_size, random_state=42)
    else:
        sampled_df = transformed_point_cloud_df
    
    # Extract coordinates
    points = sampled_df[['X', 'Y', 'Z']].values
    
    # Create figure
    fig = go.Figure()
    
    # Ground points (Z ≤ 0.05) - 使用更小的点尺寸
    ground_threshold = 0.05
    ground_mask = points[:, 2] <= ground_threshold
    ground_points = points[ground_mask]
    
    if len(ground_points) > 0:
        fig.add_trace(
            go.Scatter3d(
                x=ground_points[:, 0],
                y=ground_points[:, 1],
                z=ground_points[:, 2],
                mode='markers',
                marker=dict(
                    size=1.2,  # 减小点尺寸
                    color='#8B4513',
                    opacity=0.7
                ),
                name='地面'  # 去掉emoji
            )
        )
    
    # Add each cluster with a different color
    cluster_colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#FF9FF3', '#54A0FF', '#5F27CD']
    
    for i, cluster in enumerate(crop_clusters[:8]):  # Limit to 8 clusters for clarity
        cluster_color = cluster_colors[i % len(cluster_colors)]
        
        # Sample cluster points if too many - 使用固定种子
        if len(cluster) > 2000:  # 增加聚类点数量
            np.random.seed(42 + i)  # 为每个聚类使用不同但固定的种子
            indices = np.random.choice(len(cluster), 2000, replace=False)
            cluster_points = cluster[indices]
        else:
            cluster_points = cluster
        
        fig.add_trace(
            go.Scatter3d(
                x=cluster_points[:, 0],
                y=cluster_points[:, 1],
                z=cluster_points[:, 2],
                mode='markers',
                marker=dict(
                    size=1.5,  # 减小点尺寸
                    color=cluster_color,
                    opacity=0.8
                ),
                name=f'集群 {i+1}'  # 去掉emoji，简化名称
            )
        )
    
    # Add a ground plane visualization
    # Create a grid of points on the XY plane
    x_range = [np.min(points[:, 0]), np.max(points[:, 0])]
    y_range = [np.min(points[:, 1]), np.max(points[:, 1])]
    
    xx, yy = np.meshgrid(
        np.linspace(x_range[0], x_range[1], 10),
        np.linspace(y_range[0], y_range[1], 10)
    )
    
    # Z is 0 for all points (ground plane)
    zz = np.zeros_like(xx)
    
    # Add the ground plane
    fig.add_trace(
        go.Surface(
            x=xx, y=yy, z=zz,
            colorscale=[[0, 'rgba(210, 180, 140, 0.3)'], [1, 'rgba(210, 180, 140, 0.3)']],
            showscale=False,
            name='地平面'  # 去掉emoji
        )
    )
    
    # Set layout with improved styling - 使用支持中文的字体
    fig.update_layout(
        title=dict(
            text='群落聚类和地平面分析',  # 去掉emoji
            x=0.5,
            xanchor='center',
            font=dict(size=20, color='#2F4F4F', family='DejaVu Sans')
        ),
        scene=dict(
            xaxis=dict(
                title=dict(text='X坐标(米)', font=dict(size=14, color='#2F4F4F', family='DejaVu Sans')),
                tickfont=dict(size=12, color='#2F4F4F', family='DejaVu Sans')
            ),
            yaxis=dict(
                title=dict(text='Y坐标(米)', font=dict(size=14, color='#2F4F4F', family='DejaVu Sans')),
                tickfont=dict(size=12, color='#2F4F4F', family='DejaVu Sans')
            ),
            zaxis=dict(
                title=dict(text='Z坐标(米)', font=dict(size=14, color='#2F4F4F', family='DejaVu Sans')),
                tickfont=dict(size=12, color='#2F4F4F', family='DejaVu Sans')
            ),
            aspectmode='data'
        ),
        legend=dict(
            orientation="v",
            yanchor="top",
            y=0.98,
            xanchor="left",
            x=0.02,
            bgcolor='rgba(255, 255, 255, 0.9)',
            bordercolor='#2F4F4F',
            borderwidth=2,
            font=dict(size=12, color='#2F4F4F', family='DejaVu Sans')
        ),
        margin=dict(l=20, r=20, b=20, t=60),
        paper_bgcolor='#F8F8FF'
    )
    
    # 保存为HTML文件，使用完整的plotly.js库而不是CDN
    fig.write_html(
        output_file,
        include_plotlyjs=True,
        full_html=True,
        auto_open=False
    )
    
    print(f"Advanced visualization saved to {output_file}")
    
    return fig 