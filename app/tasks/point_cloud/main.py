#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Point Cloud Processing for Crop Height Detection
"""

import os
import time
import argparse
from modules.data_loader import load_point_cloud
from modules.preprocessing import preprocess_point_cloud
from modules.pose_estimation import estimate_sensor_pose
from modules.ground_plane_fitting import fit_ground_plane
from modules.crop_height import calculate_crop_height
from modules.visualization import visualize_point_cloud

def main():
    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Crop height detection from point cloud data')
    parser.add_argument('--input', type=str, default='point.csv', help='Input point cloud CSV file')
    parser.add_argument('--output', type=str, default='output', help='Output directory for results')
    parser.add_argument('--visualization', action='store_true', help='Generate 3D visualization')
    args = parser.parse_args()
    
    # Create output directory if it doesn't exist
    os.makedirs(args.output, exist_ok=True)
    
    print(f"Processing point cloud data from {args.input}...")
    start_time = time.time()
    
    # Step 1: Load point cloud data
    print("Step 1: Loading point cloud data...")
    point_cloud = load_point_cloud(args.input)
    
    # Step 2: Preprocess point cloud data
    print("Step 2: Preprocessing point cloud data...")
    filtered_point_cloud = preprocess_point_cloud(point_cloud)
    
    # Step 3: Estimate sensor pose
    print("Step 3: Estimating sensor pose...")
    rotation_matrix, translation_vector = estimate_sensor_pose(filtered_point_cloud)
    
    # Step 4: Transform point cloud to horizontal ground plane coordinate system
    print("Step 4: Fitting ground plane and transforming coordinate system...")
    transformed_point_cloud, ground_plane_params = fit_ground_plane(filtered_point_cloud, rotation_matrix, translation_vector)
    
    # Step 5: Calculate crop height
    print("Step 5: Calculating crop height...")
    crop_height = calculate_crop_height(transformed_point_cloud)
    
    # Save results
    result_file = os.path.join(args.output, 'result.txt')
    with open(result_file, 'w') as f:
        f.write(f"Crop Height: {crop_height:.3f} meters\n")
        f.write(f"Ground Plane Parameters: {ground_plane_params}\n")
        f.write(f"Rotation Matrix:\n{rotation_matrix}\n")
        f.write(f"Translation Vector:\n{translation_vector}\n")
    
    # Step 6: Visualize point cloud if requested
    if args.visualization:
        print("Step 6: Generating 3D visualization...")
        visualization_file = os.path.join(args.output, 'visualization.html')
        visualize_point_cloud(transformed_point_cloud, visualization_file, crop_height=crop_height)
    
    elapsed_time = time.time() - start_time
    print(f"Processing completed in {elapsed_time:.2f} seconds")
    print(f"Detected crop height: {crop_height:.3f} meters")
    print(f"Results saved to {result_file}")
    if args.visualization:
        print(f"Visualization saved to {os.path.join(args.output, 'visualization.html')}")

if __name__ == "__main__":
    main() 