{"class_name": ["04000115", "04000113", "04000106", "04000104", "04000108", "04000110", "04000112", "04000103", "04000105", "04000116", "04000182", "04000114", "04000111", "04000321", "04000117", "04000118", "04000119", "04000120", "04000121", "04000122", "04000123", "04000124", "04000125", "04000126", "04000127", "04000128", "04000129", "04000130", "04000131", "04000132", "04000133", "04000134", "04000135", "04000136", "04000137", "04000138", "04000139", "04000140", "04000141", "04000142", "04000143", "04000144", "04000145", "04000146", "04000147", "04000148", "04000149", "04000150", "04000151", "04000152", "04000153", "04000154", "04000155", "04000156", "04000157", "04000158", "04000159", "04000160", "04000161", "04000162", "04000163", "04000164", "04000165", "04000166", "04000167", "04000168", "04000169", "04000170", "04000171", "04000172", "04000173", "04000174", "04000175", "04000177", "04000178", "04000179", "04000180", "04000181", "04000183", "04000184", "04000185", "04000186", "04000187", "04000188", "04000189", "04000190", "04000191", "04000194", "04000195", "04000197", "04000198", "04000199", "04000200", "04000201", "04000202", "04000203", "04000204", "04000205", "04000206", "04000207", "04000208", "04000209", "04000210", "04000211", "04000212", "04000213", "04000214", "04000215", "04000216", "04000217", "04000218", "04000219", "04000220", "04000221", "04000222", "04000223", "04000224", "04000225", "04000226", "04000227", "04000228", "04000229", "04000230", "04000231", "04000232", "04000233", "04000234", "04000235", "04000236", "04000237", "04000238", "04000239", "04000240", "04000241", "04000243", "04000244", "04000245", "04000246", "04000247", "04000248", "04000249", "04000250", "04000251", "04000252", "04000253", "04000254", "04000255", "04000256", "04000257", "04000258", "04000259", "04000260", "04000261", "04000263", "04000264", "04000266", "04000267", "04000268", "04000269", "04000270", "04000271", "04000272", "04000273", "04000274", "04000275", "04000276", "04000277", "04000278", "04000279", "04000280", "04000281", "04000282", "04000283", "04000285", "04000286", "04000287", "04000288", "04000289", "04000290", "04000291", "04000292", "04000293", "04000294", "04000295", "04000296", "04000297", "04000298", "04000299", "04000300", "04000301", "04000302", "04000303", "04000304", "04000305", "04000306", "04000307", "04000308", "04000309", "04000310", "04000311", "04000312", "04000313", "04000314", "04000315", "04000316", "04000317", "04000318", "04000319", "04000320", "04000322", "04000323", "04000324", "04000325", "04000326", "04000328", "04000329", "04000330", "04000331", "04000332", "04000333", "04000334", "04000335", "04000336", "04000337", "04000338", "04000339", "04000340", "04000341", "04000342", "04000343", "04000344", "04000345", "04000346", "04000347", "04000348", "04000349", "04000350", "04000351", "04000352", "04000353", "04000354", "04000355", "04000356", "04000357", "04000358", "04000359", "04000360", "04000362", "04000363", "04000364", "04000365", "04000366", "04000367", "04000368", "04000369", "04000370", "04000371", "04000372", "04000373", "04000374", "04000375", "04000376", "04000377", "04000378", "04000379", "04000380", "04000381", "04000382", "04000383", "04000384", "04000385", "04000386", "04000387", "04000388", "04000389", "04000390", "04000391", "04000392", "04000393", "04000394", "04000395", "04000396", "04000397", "04000398", "04000399", "04000400", "04000401", "04000402", "04000403", "04000404", "04000405", "04000406", "04000407", "04000408", "04000409", "04000410", "04000411", "04000412", "04000413", "04000414", "04000415", "04000416", "04000417", "04000418", "04000419", "04000420", "04000421", "04000422", "04000423", "04000424", "04000426", "04000427", "04000428", "04000429", "04000430", "04000431", "04000433", "04000434", "04000435", "04000436", "04000437", "04000438", "04000439", "04000440", "04000441", "04000442", "04000443", "04000444", "04000445", "04000446", "04000447", "04000448", "04000449", "04000450", "04000451", "04000452", "04000453", "04000454", "04000455", "04000456", "04000457", "04000458", "04000459", "04000460", "04000461", "04000462", "04000463", "04000464", "04000465", "04000466", "04000467", "04000468", "04000469", "04000470", "04000471", "04000472", "04000473", "04000474", "04000475", "04000476", "04000477", "04000478", "04000479", "04000480", "04000481", "04000482", "04000483", "04000484", "04000485", "04000486", "04000487", "04000488", "04000489", "04000490", "04000491", "04000492", "04000493", "04000494", "04000495", "04000496", "04000497", "04000498", "04000499", "04000500", "04000501", "04000502", "04000503", "04000505", "04000506", "04000507", "04000508", "04000509", "04000510", "04000511", "04000512", "04000513", "04000514", "04000515", "04000516", "04000517", "04000518", "04000519", "04000520", "04000521", "04000522", "04000523", "04000524", "04000525", "04000526", "04000527", "04000528", "04000529", "04000531", "04000532", "04000533", "04000534", "04000535", "04000536", "04000537", "04000538", "04000539", "04000540", "04000541", "04000542", "04000543", "04000544", "04000545", "04000546", "04000547", "04000548", "04000549", "04000550", "04000551", "04000552", "04000553", "04000554", "04000555", "04000556", "04000557", "04000558", "04000559", "04000560", "04000561", "04000562", "04000563", "04000564", "04000565", "04000566", "04000567", "04000568", "04000569", "04000570", "04000571", "04000572", "04000574", "04000575", "04000576", "04000577", "04000578", "04000579", "04000580", "04000581", "04000582", "04000583", "04000584", "04000585", "04000586", "04000587", "04000588", "04000589", "04000590", "04000591", "04000592", "04000593", "04000594", "04000595", "04000596", "04000597", "04000598", "04000599", "04000600", "04000601", "04000602", "04000603", "04000604", "04000605", "04000606", "04000607", "04000608", "04000609", "04000610", "04000611", "04000102", "04000101", "04999999", "04000109", "04000176"], "threshold": 1, "mel_generator": {"N MEL": 128, "FMIN": 0, "FMAX": 16009, "N_FFT": 3209, "HOP_LENGTH": 809}, "pytorch_model": "./model/PytorchModel/NS500_modelpth", "rknn_model": "app/deploy/models/sound/v3/rknn/NS500_model_V1.4.rknn", "numclass": 497, "debugMode": "True", "threshold_value": 0, "low_value_flag": false, "segment": 5, "path": "./Dataset/Test", "mode_singledetect": "True", "version": "1.0.0"}