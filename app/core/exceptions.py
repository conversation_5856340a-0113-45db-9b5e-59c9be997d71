# coding:utf-8
"""
Custom exception classes and error handling
"""

import logging
from typing import Optional, Dict, Any
from flask import jsonify, request, g
from werkzeug.exceptions import HTTPException


logger = logging.getLogger(__name__)


class BaseAPIException(Exception):
    """Base exception for API errors"""
    
    def __init__(
        self,
        message: str,
        status_code: int = 500,
        error_code: str = "INTERNAL_ERROR",
        details: Optional[Dict[str, Any]] = None
    ):
        super().__init__(message)
        self.message = message
        self.status_code = status_code
        self.error_code = error_code
        self.details = details or {}
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for JSON response"""
        result = {
            'status': 'EXCEPTION',
            'statusCode': self.error_code,
            'statusMsg': self.message,
            'timestamp': g.get('start_time', '').isoformat() if hasattr(g, 'start_time') else None,
            'request_id': g.get('request_id', 'unknown')
        }
        
        if self.details:
            result.update(self.details)
        
        return result


class ValidationError(BaseAPIException):
    """Validation error exception"""
    
    def __init__(self, message: str, field: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=400,
            error_code="VALIDATION_ERROR",
            **kwargs
        )
        if field:
            self.details['field'] = field


class AuthenticationError(BaseAPIException):
    """Authentication error exception"""
    
    def __init__(self, message: str = "Authentication required", **kwargs):
        super().__init__(
            message=message,
            status_code=401,
            error_code="AUTHENTICATION_ERROR",
            **kwargs
        )


class AuthorizationError(BaseAPIException):
    """Authorization error exception"""
    
    def __init__(self, message: str = "Insufficient permissions", **kwargs):
        super().__init__(
            message=message,
            status_code=403,
            error_code="AUTHORIZATION_ERROR",
            **kwargs
        )


class ResourceNotFoundError(BaseAPIException):
    """Resource not found exception"""
    
    def __init__(self, message: str = "Resource not found", resource_type: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=404,
            error_code="RESOURCE_NOT_FOUND",
            **kwargs
        )
        if resource_type:
            self.details['resource_type'] = resource_type


class ConflictError(BaseAPIException):
    """Conflict error exception"""
    
    def __init__(self, message: str = "Resource conflict", **kwargs):
        super().__init__(
            message=message,
            status_code=409,
            error_code="CONFLICT_ERROR",
            **kwargs
        )


class RateLimitError(BaseAPIException):
    """Rate limit exceeded exception"""
    
    def __init__(self, message: str = "Rate limit exceeded", retry_after: int = None, **kwargs):
        super().__init__(
            message=message,
            status_code=429,
            error_code="RATE_LIMIT_EXCEEDED",
            **kwargs
        )
        if retry_after:
            self.details['retry_after'] = retry_after


class ExternalServiceError(BaseAPIException):
    """External service error exception"""
    
    def __init__(self, message: str, service_name: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=502,
            error_code="EXTERNAL_SERVICE_ERROR",
            **kwargs
        )
        if service_name:
            self.details['service_name'] = service_name


class ProcessingError(BaseAPIException):
    """Data processing error exception"""
    
    def __init__(self, message: str, processing_type: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=422,
            error_code="PROCESSING_ERROR",
            **kwargs
        )
        if processing_type:
            self.details['processing_type'] = processing_type


class FileError(BaseAPIException):
    """File operation error exception"""
    
    def __init__(self, message: str, file_path: str = None, operation: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=400,
            error_code="FILE_ERROR",
            **kwargs
        )
        if file_path:
            self.details['file_path'] = file_path
        if operation:
            self.details['operation'] = operation


class ModelError(BaseAPIException):
    """AI model error exception"""
    
    def __init__(self, message: str, model_name: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=500,
            error_code="MODEL_ERROR",
            **kwargs
        )
        if model_name:
            self.details['model_name'] = model_name


class ConfigurationError(BaseAPIException):
    """Configuration error exception"""
    
    def __init__(self, message: str, config_key: str = None, **kwargs):
        super().__init__(
            message=message,
            status_code=500,
            error_code="CONFIGURATION_ERROR",
            **kwargs
        )
        if config_key:
            self.details['config_key'] = config_key


def register_error_handlers(app):
    """Register error handlers with Flask app"""
    
    @app.errorhandler(BaseAPIException)
    def handle_api_exception(error: BaseAPIException):
        """Handle custom API exceptions"""
        logger.error(
            f"API Exception: {error.message}",
            extra={
                'error_code': error.error_code,
                'status_code': error.status_code,
                'details': error.details,
                'method': request.method if request else 'unknown',
                'url': request.url if request else 'unknown'
            }
        )
        
        response = jsonify(error.to_dict())
        response.status_code = error.status_code
        return response
    
    @app.errorhandler(HTTPException)
    def handle_http_exception(error: HTTPException):
        """Handle HTTP exceptions"""
        logger.warning(
            f"HTTP Exception: {error.description}",
            extra={
                'status_code': error.code,
                'method': request.method if request else 'unknown',
                'url': request.url if request else 'unknown'
            }
        )
        
        response_data = {
            'status': 'EXCEPTION',
            'statusCode': f"HTTP_{error.code}",
            'statusMsg': error.description,
            'timestamp': g.get('start_time', '').isoformat() if hasattr(g, 'start_time') else None,
            'request_id': g.get('request_id', 'unknown')
        }
        
        response = jsonify(response_data)
        response.status_code = error.code
        return response
    
    @app.errorhandler(Exception)
    def handle_generic_exception(error: Exception):
        """Handle unexpected exceptions"""
        logger.error(
            f"Unexpected exception: {str(error)}",
            exc_info=True,
            extra={
                'error_type': type(error).__name__,
                'method': request.method if request else 'unknown',
                'url': request.url if request else 'unknown'
            }
        )
        
        # Don't expose internal error details in production
        if app.config.get('DEBUG', False):
            message = str(error)
        else:
            message = "An internal error occurred"
        
        response_data = {
            'status': 'EXCEPTION',
            'statusCode': 'INTERNAL_ERROR',
            'statusMsg': message,
            'timestamp': g.get('start_time', '').isoformat() if hasattr(g, 'start_time') else None,
            'request_id': g.get('request_id', 'unknown')
        }
        
        response = jsonify(response_data)
        response.status_code = 500
        return response
    
    @app.errorhandler(404)
    def handle_not_found(error):
        """Handle 404 errors"""
        response_data = {
            'status': 'EXCEPTION',
            'statusCode': 'NOT_FOUND',
            'statusMsg': 'The requested resource was not found',
            'timestamp': g.get('start_time', '').isoformat() if hasattr(g, 'start_time') else None,
            'request_id': g.get('request_id', 'unknown')
        }
        
        response = jsonify(response_data)
        response.status_code = 404
        return response
    
    @app.errorhandler(405)
    def handle_method_not_allowed(error):
        """Handle 405 errors"""
        response_data = {
            'status': 'EXCEPTION',
            'statusCode': 'METHOD_NOT_ALLOWED',
            'statusMsg': 'The requested method is not allowed for this resource',
            'timestamp': g.get('start_time', '').isoformat() if hasattr(g, 'start_time') else None,
            'request_id': g.get('request_id', 'unknown')
        }
        
        response = jsonify(response_data)
        response.status_code = 405
        return response
