# coding:utf-8

from . import api
import os
import datetime
import pandas as pd
from pathlib import Path
from flask import make_response, send_from_directory, current_app, jsonify, request
from app import cache, executor
from app.utils.response_code import RET, msg_map
from app.utils.commons import deldir, zip_compress, get_file_info
from app.utils.parse_ini import ParseIniFile
from app.tasks.handler import Handler
import cv2
import json
import base64
import time

message = {
    'sound-category':'正在识别动物类别(含阈值)',
    'sound-category_3':'正在识别动物类别',
    'sound-date':'正在识别动物始鸣终鸣日期',
    'wheat-stage':'正在识别小麦发育期',
    'wheat-coverage':'正在识别小麦覆盖度',
    'wheat-density':'正在识别小麦密度',
    'wheat-state':'正在识别小麦生长状况',
    'wheat-leaf':'正在识别小麦叶面积指数',
    'wheat-dry':'正在识别小麦干物质质量',
    'wheat-height':'正在识别小麦高度',
    'wheat-community':'正在识别小麦群落结构',
    'corn-stage':'正在识别玉米发育期',
    'corn-coverage':'正在识别玉米覆盖度',
    'corn-density':'正在识别玉米密度',
    'corn-state':'正在识别玉米生长状况',
    'corn-leaf':'正在识别玉米叶面积指数',
    'corn-dry':'正在识别玉米干物质质量',
    'corn-height':'正在识别玉米高度',
    'corn-community':'正在识别玉米群落结构',
    'rice-stage':'正在识别水稻发育期',
    'rice-coverage':'正在识别水稻覆盖度',
    'rice-density':'正在识别水稻密度',
    'rice-state':'正在识别水稻生长状况',
    'rice-leaf':'正在识别水稻叶面积指数',
    'rice-dry':'正在识别水稻干物质质量',
    'rice-height':'正在识别水稻高度',
    'rice-community':'正在识别水稻群落结构',
    'cotton-stage':'正在识别棉花发育期',
    'cotton-coverage':'正在识别棉花覆盖度',
    'cotton-density':'正在识别棉花密度',
    'cotton-state':'正在识别棉花生长状况',
    'cotton-leaf':'正在识别棉花叶面积指数',
    'cotton-dry':'正在识别棉花干物质质量',
    'cotton-height':'正在识别棉花高度',
    'cotton-community':'正在识别棉花群落结构'
}

@api.route('/chunk_upload', methods=["GET"])
def chunk_upload():
    '''
    单个分片处理
    '''
    file_name = request.form.get('identifier')
    chunk_index = int(request.form.get("chunkNumber"))
    total_chunk = int(request.form.get("totalChunks"))
    upload_file = request.files["chunk"] # 二进制数据

    # 校验参数
    if not all([file_name, chunk_index, total_chunk, upload_file]):
        return jsonify(status="EXCEPTION", code=RET.UPLOADPARAMS, msg=msg_map[RET.UPLOADPARAMS])
    
    try:
        file_path = os.path.join('app/static/uploads', 'chunk_file', file_name)
        chunk_path = os.path.join(file_path, str(chunk_index))
        if not os.path.exists(file_path):
            os.makedirs(file_path)
        with open(chunk_path, 'wb+') as destination:
            # for chunk in upload_file.chunks():
            destination.write(upload_file.read())
    except Exception as e:
        current_app.logger.error(e)
        return jsonify(status="EXCEPTION", code=RET.UPLOADRUNERR, msg=msg_map[RET.UPLOADRUNERR])
    else:
        res = {
            'file_name': file_name,
            "chunk_index": chunk_index,
            'status': 1
        }
        return jsonify(status="OK", code=RET.OK, msg=msg_map[RET.OK], data=res)

@api.route('/merge')
def chunk_merge():
    '''
    分片合并
    '''
    file_name = request.args.get("hash") # 文件hash
    suffix = request.args.get("suffix")
    total_chunk = int(request.args.get("totalChunks")) # 总共分片

    # 校验参数
    if not all([file_name, suffix, total_chunk]):
        return jsonify(status="EXCEPTION", code=RET.UPLOADPARAMS, msg=msg_map[RET.UPLOADPARAMS])
    
    try:
        file_path = os.path.join('app/api_v1_0/static/uploads', 'chunk_file', file_name)
        chunks_list = list(set(os.listdir(file_path)))
        is_over = False
        # print('total_chunk===', total_chunk, 'chunks_list', len(chunks_list))
        if len(chunks_list) == total_chunk:
            is_over = True
        if is_over:
            # 所有的分片 必须按照分块顺序排序，否则 可能合并的文件顺序被打乱
            all_chunk = os.listdir(file_path)
            all_chunk.sort(key=lambda x: int(x))  # fig bug:  默认是按 '0' '11'这种字符串类型排序，会导致分片顺序错乱。
            target_path = os.path.join('app/api_v1_0/static/uploads', 'chunk_file', file_name+"."+suffix)
            target_path_temp = os.path.join('app/api_v1_0/static/uploads', 'chunk_file', file_name+"temp")
            with open(target_path, "wb+") as f:
                for chunk in all_chunk:
                    chunk_path = os.path.join(file_path, chunk)
                    with open(chunk_path, "rb") as g:
                        data = g.read()
                        f.write(data)
    except Exception as e:
        deldir(file_path)
        current_app.logger.error(e)
        return jsonify(status="EXCEPTION", code=RET.UPLOADMERGEERR, msg=msg_map[RET.UPLOADMERGEERR])
    else:        
        deldir(file_path)
        # 上传的文件在服务端的存储地址
        file_url = os.sep.join(['app/api_v1_0/static/uploads', 'chunk_file', file_name+"."+suffix])
        res = {
            "url": file_url,  # os.path.join(settings.MEDIA_ROOT, 'chunk_file', file_name+".zip")
            "fileName": file_name,
        }
        return jsonify(status="OK", code=RET.OK, msg=msg_map[RET.OK], data=res)

@api.route('/get_running_status', methods=["GET"])
def status():
    '''
    获取运行进度
    '''
    token = request.args.get("token")

    # 校验参数
    if not all([token]):
        return jsonify(status="EXCEPTION", code=RET.PROCESSPARAMSERR, msg=msg_map[RET.PROCESSPARAMSERR])
    
    try:
        status = cache.get(token) if cache.get(token) else 0
    except Exception as e:
        current_app.logger.error(e)
        return jsonify(status="EXCEPTION", code=RET.PROCESSRUNERR, msg=msg_map[RET.PROCESSRUNERR])
    else:
        return jsonify(status="OK", code=RET.OK, msg=msg_map[RET.OK], data=status)

@api.route('/download', methods=["POST"])
def download():
    '''
    下载结果文档
    '''
    # data = request.get_json()
    # print(data)
    # shell_list = data.get("shell")
    # print(shell_list)
    # try:
    #     for command in shell_list:
    #         os.system(command)
    # except:
    #     return jsonify(status="EXCEPTION", code='405', msg='failure')
    # else:
    #     return jsonify(status="OK", code='200', msg='success')
    data = request.get_json()
    filename = data.get("filename")
    print(filename)
    # 校验参数
    if not all([filename]):
        return jsonify(status="EXCEPTION", code=RET.DWLOADPARAMS, msg=msg_map[RET.DWLOADPARAMS])
    
    try:
        path_str = str(Path("app/static/results").absolute())
        # 这里的地址要绝对地址
        res = make_response(send_from_directory(path_str, filename.encode('utf-8').decode('utf-8'),as_attachment=True))
        res.headers["Content-Disposition"] = "attachment; filename={}".format(filename.encode().decode('latin-1'))
    except Exception as e:
        current_app.logger.error(e)
        return jsonify(status="EXCEPTION", code=RET.DWLOADRUNERR, msg=msg_map[RET.DWLOADRUNERR])
    else:
        return res
    
def run_v1(token, img, pc, sound):
    try:
        p = Path('app/static/data_sets')
        file_list = list(p.glob("**/*.*"))
        total_num = len(file_list) # 获取文件总数

        total_index = 0
        csv_dict = {}

        if total_num == 0:
            cache.set(token, -3)
        else:
            for c_dir, dir_list, file_list in os.walk('app/static/data_sets'):
                if len(file_list) > 0:
                    info = c_dir.split('/')
                    item = info[-1]
                    typer = info[-2]

                    key =  typer + '-' + item

                    object_handler = Handler(typer, item)

                    csv_dict[key] = []
                    sound_type = {}

                    env = sound.get('env')
                    params = {
                        'env': int(env),
                    }


                    for index, filename in enumerate(sorted(file_list)):
                        print(key)
                        print(filename)
                        data = {}

                        # 获取文件名上的时间信息
                        tz, year, month, day, h, m, s, jw, ext_, num = get_file_info(filename)

                        time_str = '-'.join([year, month, day])

                        # 数据文件地址
                        file_path = os.path.join(c_dir, filename)
                        params['file_path'] = file_path

                        res = object_handler.get_results(params)


                        if item.startswith('date'):
                            if res and res != 'n':
                                if res not in sound_type.keys():
                                    sound_type[res]= [time_str, time_str]
                                else:
                                    sound_type[res][1] = time_str

                        else:
                            data['filename'] = filename
                            data[item] = str(res)
                            print(str(res))
                            csv_dict[key].append(data)

                        obj = {
                            'base64':'',
                            'percent': round(total_index * 100 / total_num, 1) if round(total_index * 100 / total_num, 1) != 0 else 0,
                            'msg': message[key]
                        }
                        
                        obj_str = json.dumps(obj)

                        cache.set(token, obj_str)
                        total_index += 1

                    if sound_type:
                        for sound_key in sound_type.keys():
                            csv_dict[key].append({'category': sound_key, 'start_time': sound_type[sound_key][0], 'end_time': sound_type[sound_key][1]})

    
    except Exception as e:
        cache.set(token, -1)
        current_app.logger.error(e)
    else:
        try:
            for key in csv_dict:
                info = key.split('-')[-1]
                if info.startswith('date'):
                    df = pd.DataFrame(csv_dict[key], columns=['category', 'start_time', 'end_time'])
                else:
                    df = pd.DataFrame(csv_dict[key], columns=['filename', info])

                # 后续优化就是针对DataFrame数据进行优化，如果有开始时间识别
                now_time = datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S') # 时间戳
                csv_filename = str(key) + '.csv'
                token_result_dir = os.path.join('app/static/results', token)
                if not os.path.exists(token_result_dir):
                    os.makedirs(token_result_dir)
                csv_path = os.path.join(token_result_dir, csv_filename)
                df.to_csv(csv_path, index=False)

            # 压缩结果文件
            zip_filename = zip_compress(token_result_dir, token)
        except Exception as e:
            deldir(token_result_dir)
            cache.set(token, -1)
        else:
            deldir(token_result_dir)
            cache.set(token, -2)

@api.route('/run', methods=["GET"])
def start_task():
    '''
    后端算法模型运行
    '''
    # img = eval(request.args.get("image"))
    img = {}
    pc = eval(request.args.get("pc"))
    sound = eval(request.args.get("sound"))


    now_time = datetime.datetime.now().strftime('%Y-%m-%d-%H-%M-%S') # 时间戳作为token
    token = str(now_time)
    try:
        executor.submit(run_v1, token, img, pc, sound)
    except Exception as e:
        current_app.logger.error(e)
        return jsonify(status='EXCEPTION',  code=RET.STATICRUNERR, msg=msg_map[RET.STATICRUNERR])
    else:
        return jsonify(status="OK", code=RET.OK, msg=msg_map[RET.OK], data=token)
