# coding:utf-8

from . import api
import os
import time
import json
from flask import request, current_app
from app import executor
from app.tasks.temperature.get_temperature import get_temperature,get_temperature_v1

# @app.route()修饰器来把函数绑定到URL，相当于建立URL网络地址和程序函数的映射关系
@api.route('/')
def init():
    return "hello world!"

@api.route('/shutdown', methods=['POST'])
def getjson_shutdown():
    result = {"ShutdownState":"1"}
    current_app.logger.info("{} /v1/shutdown".format(request.remote_addr))
    def shutdown():
        time.sleep(3)
        current_app.logger.info("I will shutdown the systemc")
        os.system('echo 123456|sudo -S shutdown -h now "I will shutdown the systemc"')
    try:
        executor.submit(shutdown)
    except Exception as e:
        current_app.logger.error("/v1/shutdown {}".format(e))
    current_app.logger.info("{} /v1/shutdown 已返回响应".format(request.remote_addr))
    return json.dumps(result)

@api.route('/state', methods=['POST'])
def getjson_state():
    result={}
    try:
        result['statusCode'] = "600"
        result['statusMsg'] = "Identification service is ready!"
        # 注释掉FTP相关调用，避免连接超时
        # get_temperature("20220615141000","20220615143000","second","cF")
        # get_temperature_v1("cF")
        current_app.logger.info("{} /v1/state {}".format(request.remote_addr, result))
    except Exception as e:
        result['statusCode'] = "603"
        result['statusMsg'] = "Identification service unknown  error"
        current_app.logger.warning("{} /v1/state {}".format(request.remote_addr, result))
    return json.dumps(result)