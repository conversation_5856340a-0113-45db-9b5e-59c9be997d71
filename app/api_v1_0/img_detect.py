# coding:utf-8

from . import api
import os
import json
import datetime
import ast
import time
from flask import request, current_app
from app.utils.commons import check_file, del_file, get_file_info, average
from app.utils.response_code import RET, msg_map
from app.tasks.stage.stage_optimization import StageJudger
from app.tasks.handler import Handler
from app.utils.parse_ini import ParseIniFile
from app.utils.ftp import FTP_OP
# 导入异常处理（如果可用）
try:
    from app.core.exceptions import ValidationError, FileError, ProcessingError
    ENHANCED_EXCEPTIONS = True
except ImportError:
    ENHANCED_EXCEPTIONS = False
    # 定义简单的异常类作为后备
    class ValidationError(Exception):
        def __init__(self, message, **kwargs):
            super().__init__(message)
            self.message = message

    class FileError(Exception):
        def __init__(self, message, **kwargs):
            super().__init__(message)
            self.message = message

    class ProcessingError(Exception):
        def __init__(self, message, **kwargs):
            super().__init__(message)
            self.message = message

pif = ParseIniFile('app/config.ini')

def validate_request_data(data):
    """验证请求数据的完整性和有效性"""
    if not data:
        raise ValidationError("请求数据为空")

    # 基本任务参数验证
    required_fields = ['datetime', 'detectType', 'name', 'code']
    missing_fields = [field for field in required_fields if not data.get(field)]
    if missing_fields:
        raise ValidationError(f"基本任务参数不完整，缺少: {', '.join(missing_fields)}")

    # FTP参数验证
    ftp_fields = ['srcURLList', 'srcLoginUser', 'srcLoginPasswd']
    missing_ftp_fields = [field for field in ftp_fields if not data.get(field)]
    if missing_ftp_fields:
        raise ValidationError(f"FTP请求参数不完整，缺少: {', '.join(missing_ftp_fields)}")

    # 作物类型验证（仅对物候期和密度识别有效，覆盖度不受限制）
    name = data.get('name')
    supported_crops = ['wheat', 'corn', 'cotton', 'rice']
    if name not in supported_crops:
        current_app.logger.warning(f"作物类型 {name} 不在支持列表中，但覆盖度识别仍可进行")

    return True


def download_ftp_files(urllist, log_user, log_pwd, dst_file_path):
    """下载FTP文件"""
    try:
        if not os.path.exists(dst_file_path):
            os.makedirs(dst_file_path, exist_ok=True)

        downloaded_files = []
        for urlindex in urllist.keys():
            url = urllist[urlindex]
            try:
                url_host = url.split(':')[1][2:]
                url_port = url.split(':')[2].split('/')[0]
                ftp_file_path = '/'.join(url.split(':')[2].split('/')[1:])
                file_name = ftp_file_path.split("/")[-1]
                dst_file = os.path.join(dst_file_path, file_name)

                log_ftp = FTP_OP(url_host, log_user, log_pwd, int(url_port))
                log_ftp.download_file(ftp_file_path, dst_file)
                downloaded_files.append(dst_file)
                current_app.logger.info(f"FTP文件下载成功: {ftp_file_path} -> {dst_file}")
            except Exception as e:
                current_app.logger.error(f"下载文件失败 {url}: {str(e)}")
                raise FileError(f"FTP文件下载失败: {str(e)}")

        return downloaded_files
    except Exception as e:
        current_app.logger.error(f"FTP下载过程失败: {str(e)}")
        raise FileError(f"FTP下载过程失败: {str(e)}")


@api.route('/detect_image', methods=['POST'])
def getjson_image():
    """
    图像识别接口 - 改进版本
    修改逻辑：覆盖度计算不受作物类型约束，其余识别项依然按原逻辑
    """
    start_time = time.time()
    dst_file_path = ''
    result = {}

    try:
        # 获取请求数据
        data = request.get_json()
        current_app.logger.info(f"收到图像识别请求: {data.get('name', 'unknown')}")

        # 验证请求数据
        validate_request_data(data)

        # 提取参数
        urllist = data.get("srcURLList")
        log_user = data.get("srcLoginUser")
        log_pwd = data.get("srcLoginPasswd")
        date_time = data.get('datetime')
        detect_type = data.get('detectType')
        name = data.get('name')
        stage_flag = data.get('stageFlag')
        stage_days = data.get('stageDays')
        sowing_mode = data.get('sowingMode')
        code = data.get('code')

        # 检查作物类型是否支持（用于后续逻辑判断）
        supported_crops = ['wheat', 'corn', 'cotton', 'rice']
        crop_supported = name in supported_crops

        # FTP文件下载
        static_dir = pif.get_data("common", "static_dir")
        dst_file_path = os.path.join(static_dir, 'ftp/img')
        download_ftp_files(urllist, log_user, log_pwd, dst_file_path)

        # 文件下载成功之后，进行智能识别
        # 机位号配置
        stage_jw = pif.get_data("settings", "stage_jw")
        coverage_jw = pif.get_data("settings", "coverage_jw")
        density_jw = pif.get_data("settings", "density_jw")
        jw_config_list = [stage_jw, coverage_jw, density_jw]

        file_list = []

        # 文件格式判断
        for filename in os.listdir(dst_file_path):
            # 获取文件信息
            tz, year, month, day, h, m, s, jw, ext, num = get_file_info(filename)
            dst_filename = os.path.join(dst_file_path, filename)
            if check_file(dst_filename, 'jpg') and jw in jw_config_list:
                file_list.append((tz, jw, dst_filename))

        # difference_list = list(set(jw_config_list).difference(set(file_config_list)))
        if file_list:
            result = {}
            # 物候、覆盖度、植株密度识别，互不干扰
            for file_info in file_list:
                # 判断台站是否配置,请求里没有台站号
                tz, jw, dst_filename = file_info
                pif.set_data(tz, ('crop', name)) if not pif.get_data(tz, "crop") else None
                crop = pif.get_data(tz, "crop")

                # 判断轮作
                if crop != name:
                    pif.del_data(tz)
                    pif.set_data(tz, ("crop", name))
                stage_time = time.time()

                # 物候识别 - 仅对支持的作物类型进行
                if stage_jw == jw and crop_supported:
                    if not all([stage_flag, stage_days]):
                        current_app.logger.warning("植物物候期识别参数不完整，跳过物候识别")
                    else:
                        try:
                            current_app.logger.info(f"开始物候识别: {name}")
                            object_handler = Handler(name, 'stage')
                            current_time = datetime.datetime.strptime(date_time, "%Y%m%d%H%M%S")

                            last_time_stage = pif.get_data(tz, "last_time_stage")

                            # 查看当前识别任务与上次任务的时间间隔,时间间隔3天
                            if (last_time_stage != None and (current_time - datetime.datetime.strptime(last_time_stage, "%Y%m%d%H%M%S")).days >= 3):
                                pif.set_data(tz, ("last_5_stages", "[]"))

                            pif.set_data(tz, ("last_time_stage", date_time))
                            # 获取配置文件中存储的发育期列表
                            last_5_stages = pif.get_data(tz, "last_5_stages")
                            last_5_stages = [] if last_5_stages == None else ast.literal_eval(last_5_stages)

                            active_stage_time = current_time + datetime.timedelta(days=-stage_days)
                            current_app.logger.info("配置文件中发育期{}的开始时间为 {}".format(stage_flag, active_stage_time))

                            sj = StageJudger(name, stage_flag, last_5_stages, active_stage_time.strftime("%Y%m%d%H%M%S"))

                            now_stage_name = object_handler.get_results({"file_path": dst_filename})
                            result['stageFlag'], res_stage_time, result['stageDays'], module_stage_code = sj.get_stage_info(now_stage_name, date_time)
                            result['qcStageFlag'] = 0
                            result['qcStageDays'] = 0

                            # 更新配置文件中的发育期列表
                            if len(last_5_stages) < 5:
                                last_5_stages.append(module_stage_code)
                            else:
                                last_5_stages.pop(0)
                                last_5_stages.append(module_stage_code)
                            last_5_stages = list(map(lambda x: str(x), last_5_stages))
                            pif.set_data(tz, ["last_5_stages", "[{}]".format(','.join(last_5_stages))])

                            # 判断是否为出苗时间，如果有出苗时间则保存
                            pif.set_data(tz, ["chumiao_start_time", res_stage_time]) if result['stageFlag'] == 21 else None
                            current_app.logger.info("物候识别完成")
                        except Exception as e:
                            current_app.logger.error(f"物候识别失败: {str(e)}")
                            # 物候识别失败不影响其他识别项
                elif stage_jw == jw and not crop_supported:
                    current_app.logger.info(f"作物类型 {name} 不支持物候识别，跳过")
                cover_time = time.time()
                current_app.logger.info("物候识别用时:{}s".format(cover_time-stage_time))

                # 覆盖度识别 - 不受作物类型约束，所有作物都可以进行覆盖度计算
                if coverage_jw == jw:
                    try:
                        current_app.logger.info(f"开始覆盖度识别: {name}")
                        # 覆盖度识别使用通用的覆盖度计算方法，不依赖具体作物类型
                        if crop_supported:
                            object_handler = Handler(name, 'coverage')
                        else:
                            # 对于不支持的作物类型，使用通用的覆盖度计算
                            # 可以使用默认作物类型或通用算法
                            current_app.logger.info(f"作物类型 {name} 使用通用覆盖度算法")
                            object_handler = Handler('wheat', 'coverage')  # 使用wheat作为默认类型进行覆盖度计算

                        cov = object_handler.get_results({"file_path": dst_filename})
                        current_app.logger.info("覆盖度计算完成:{}".format(cov))

                        current_time = datetime.datetime.strptime(date_time, "%Y%m%d%H%M%S")

                        last_time_cov = pif.get_data(tz, "last_time_cov")
                        # 查看当前识别任务与上次任务的时间间隔,时间间隔3天
                        if (last_time_cov != None and (current_time - datetime.datetime.strptime(last_time_cov, "%Y%m%d%H%M%S")).days >= 3):
                            pif.set_data(tz, ("last_5_coverage", "[]"))

                        pif.set_data(tz, ("last_time_cov", date_time))

                        # 获取配置文件中存储的覆盖度列表
                        last_5_coverage = pif.get_data(tz, "last_5_coverage")
                        last_5_coverage = [] if last_5_coverage == None else ast.literal_eval(last_5_coverage)
                        result['preCoverage'] = average(last_5_coverage) if last_5_coverage else cov
                        if len(last_5_coverage) < 5:
                            last_5_coverage.append(cov)
                        else:
                            last_5_coverage.pop(0)
                            last_5_coverage.append(cov)

                        result['currentCoverage'] = average(last_5_coverage)
                        result['currentCoverage'] = float('%.2f'%result['currentCoverage'])
                        result['qcCurrentCoverage'] = 0
                        current_app.logger.info("优化后的覆盖度计算完成:{}".format(result['currentCoverage']))

                        last_5_coverage = list(map(lambda x:str(x), last_5_coverage))
                        pif.set_data(tz, ("last_5_coverage", "[{}]".format(','.join(last_5_coverage))))
                        current_app.logger.info("覆盖度识别完成")
                    except Exception as e:
                        current_app.logger.error(f"覆盖度计算失败: {str(e)}")
                        # 覆盖度计算失败不影响其他识别项
                density_time = time.time()
                current_app.logger.info("覆盖度识别用时:{}s".format(density_time-cover_time))

                # 植株密度识别 - 仅对支持的作物类型进行
                if density_jw == jw and crop_supported:
                    try:
                        current_app.logger.info(f"开始密度识别: {name}")
                        density_params = {
                            'file_path': dst_filename,
                            'current_stage': result.get('stageFlag'),
                            'days': result.get('stageDays'),
                            'pre_mean_cov': result.get('currentCoverage'),
                            'cotton_area': pif.get_data("settings", "cotton_area"),
                            'rice_area': pif.get_data("settings", "rice_area"),
                            'corn_area': pif.get_data("settings", "corn_area")
                        }

                        object_handler = Handler(name, 'density')
                        density = object_handler.get_results(density_params)
                        density = float(density)
                        current_app.logger.info("密度计算完成:{}".format(density))

                        current_time = datetime.datetime.strptime(date_time, "%Y%m%d%H%M%S")
                        last_time_density = pif.get_data(tz, "last_time_density")

                        # 查看当前识别任务与上次任务的时间间隔,时间间隔3天
                        if (last_time_density != None and (current_time - datetime.datetime.strptime(last_time_density, "%Y%m%d%H%M%S")).days >= 3):
                            pif.set_data(tz, ("last_5_density", "[]"))

                        pif.set_data(tz, ("last_time_density", date_time))

                        # 获取配置文件中存储的密度列表
                        last_5_density = pif.get_data(tz, "last_5_density")
                        last_5_density = [] if last_5_density == None else ast.literal_eval(last_5_density)

                        if len(last_5_density) < 5:
                            last_5_density.append(density)
                        else:
                            last_5_density.pop(0)
                            last_5_density.append(density)

                        result['density'] = average(last_5_density)
                        result['density'] = '%.1f'%result['density']
                        result['qcDensity'] = 0
                        current_app.logger.info("优化后的密度计算完成:{}".format(result['density']))

                        last_5_density = list(map(lambda x:str(x), last_5_density))
                        pif.set_data(tz, ("last_5_density", "[{}]".format(','.join(last_5_density))))
                        current_app.logger.info("密度识别完成")
                    except Exception as e:
                        current_app.logger.error(f"密度计算失败: {str(e)}")
                        # 密度计算失败不影响其他识别项
                elif density_jw == jw and not crop_supported:
                    current_app.logger.info(f"作物类型 {name} 不支持密度识别，跳过")

        # 其余生长量计算 - 仅对支持的作物类型进行
        if crop_supported and file_list:
            try:
                # 出苗日期的判定
                if name == 'rice':
                    pinzhong = code[-2:]
                    if pinzhong in ['01', '02', '03']:
                        chumiao_time = date_time[0:4] + pif.get_data("settings", 'rice_zao')
                    elif pinzhong in ['04', '05', '06']:
                        chumiao_time = date_time[0:4] + pif.get_data("settings", 'rice_dan')
                    elif pinzhong in ['07', '08', '09']:
                        chumiao_time = date_time[0:4] + pif.get_data("settings", 'rice_wan')
                else:
                    chumiao_time = date_time[0:4] + pif.get_data("settings", name)

                params = {
                    'current_time': date_time,
                    'pre_mean_cov': result.get('preCoverage')/100 if result.get('preCoverage') is not None else None,
                    'current_stage': result.get('stageFlag'),
                    'days': result.get('stageDays'),
                    'area': '2',
                    'chumiao_start_time': pif.get_data(tz, "chumiao_start_time") if pif.get_data(tz, "chumiao_start_time") else chumiao_time
                }

                # 植株密度计算（如果之前没有计算）
                if density_jw is None and 'density' not in result:
                    try:
                        current_app.logger.info("使用参数方式计算密度")
                        density_object_handler = Handler(name, 'density')
                        result['density'] = density_object_handler.get_results(params)
                        result['qcDensity'] = 0
                    except Exception as e:
                        current_app.logger.error(f"参数方式密度计算失败: {str(e)}")

                dry_time = time.time()
                current_app.logger.info("密度识别用时:{}s".format(dry_time-density_time))

                # 干物质重量计算
                try:
                    current_app.logger.info("开始干物质重量计算")
                    dry_object_handler = Handler(name, 'dry')
                    if params['chumiao_start_time'] is None:
                        params['chumiao_start_time'] = "20230301120000"
                    result['drymatterWeight'] = dry_object_handler.get_results(params)
                    result['drymatterWeight'] = '%.1f'%result['drymatterWeight']
                    result['qcDrymatterWeight'] = 0
                    current_app.logger.info("干物质重量计算完成")
                except Exception as e:
                    current_app.logger.error(f"干物质重量计算失败: {str(e)}")

                leaf_time = time.time()
                current_app.logger.info("干物质重量识别用时:{}s".format(leaf_time-dry_time))

                # 叶面积指数计算
                try:
                    current_app.logger.info("开始叶面积指数计算")
                    leaf_object_handler = Handler(name, 'leaf')
                    result['leafareaIndex'] = leaf_object_handler.get_results(params)
                    result['leafareaIndex'] = '%.1f'%result['leafareaIndex']
                    result['qcLeafareaIndex'] = 0
                    current_app.logger.info("叶面积指数计算完成")
                except Exception as e:
                    current_app.logger.error(f"叶面积指数计算失败: {str(e)}")

                sum_time = time.time()
                current_app.logger.info("叶面积指数识别用时:{}s".format(sum_time-leaf_time))

                # 生长状况计算
                try:
                    result['growthState'] = 2
                except Exception as e:
                    current_app.logger.error(f"生长状况计算失败: {str(e)}")

            except Exception as e:
                current_app.logger.error(f"生长量计算过程失败: {str(e)}")

        # 文件清理
        try:
            if dst_file_path and os.path.exists(dst_file_path):
                del_file(dst_file_path)
                current_app.logger.info("文件清理完成")
        except Exception as e:
            current_app.logger.error(f"文件清理失败: {str(e)}")

        # 检查文件处理结果
        if not file_list:
            current_app.logger.error("文件格式有误或文件缺少")
            return create_error_response(RET.FILEERR, msg_map[RET.FILEERR], date_time, detect_type, name, sowing_mode, code)

        # 记录总用时
        total_time = time.time() - start_time
        current_app.logger.info(f"图像识别总用时: {total_time:.2f}s")

        # 检查识别结果完整性
        # 修改逻辑：覆盖度是必须的，其他项根据作物类型支持情况判断
        required_results = ['currentCoverage']  # 覆盖度是必须的
        if crop_supported:
            # 对于支持的作物类型，检查所有识别项
            required_results.extend(['stageFlag', 'density', 'leafareaIndex', 'drymatterWeight'])

        missing_results = [key for key in required_results if result.get(key) is None]

        if missing_results:
            current_app.logger.error(f"识别结果不完整，缺少: {missing_results}")
            return create_error_response(RET.GROWTHERR, msg_map[RET.GROWTHERR], date_time, detect_type, name, sowing_mode, code, result)
        else:
            current_app.logger.info("识别完成，结果正常")
            return create_success_response(result, date_time, detect_type, name, sowing_mode, code)

    except ValidationError as e:
        current_app.logger.error(f"请求验证失败: {str(e)}")
        return create_error_response(RET.RPNFTASK, str(e), data.get('datetime'), data.get('detectType'), data.get('name'), data.get('sowingMode'), data.get('code'))

    except FileError as e:
        current_app.logger.error(f"文件处理失败: {str(e)}")
        return create_error_response(RET.FRFTPD, str(e), data.get('datetime'), data.get('detectType'), data.get('name'), data.get('sowingMode'), data.get('code'))

    except ProcessingError as e:
        current_app.logger.error(f"处理过程失败: {str(e)}")
        return create_error_response(RET.GROWTHERR, str(e), data.get('datetime'), data.get('detectType'), data.get('name'), data.get('sowingMode'), data.get('code'))

    except Exception as e:
        current_app.logger.error(f"未预期的错误: {str(e)}", exc_info=True)
        # 确保文件清理
        try:
            if dst_file_path and os.path.exists(dst_file_path):
                del_file(dst_file_path)
        except:
            pass
        return create_error_response(RET.GROWTHERR, "系统内部错误", data.get('datetime') if data else None, data.get('detectType') if data else None, data.get('name') if data else None, data.get('sowingMode') if data else None, data.get('code') if data else None)


def create_success_response(result, date_time, detect_type, name, sowing_mode, code):
    """创建成功响应"""
    response_data = {
        'datetime': date_time,
        'detectType': detect_type,
        'status': "OK",
        'statusCode': RET.OK,
        'statusMsg': msg_map[RET.OK],
        'name': name,
        'sowingMode': sowing_mode,
        'code': code
    }
    response_data.update(result)
    return json.dumps(response_data)


def create_error_response(status_code, status_msg, date_time=None, detect_type=None, name=None, sowing_mode=None, code=None, partial_result=None):
    """创建错误响应"""
    response_data = {
        'status': 'EXCEPTION',
        'statusCode': status_code,
        'statusMsg': status_msg
    }

    if date_time:
        response_data['datetime'] = date_time
    if detect_type:
        response_data['detectType'] = detect_type
    if name:
        response_data['name'] = name
    if sowing_mode:
        response_data['sowingMode'] = sowing_mode
    if code:
        response_data['code'] = code
    if partial_result:
        response_data.update(partial_result)

    return json.dumps(response_data)