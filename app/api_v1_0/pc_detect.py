# coding:utf-8

from . import api
import os
import json
import time
import numpy as np
import pandas as pd
from flask import request, current_app, jsonify
# 移除老的导入，只使用先进算法
from app.utils.parse_ini import ParseIniFile
from app.utils.commons import check_file, del_file, get_file_info
from app.utils.response_code import RET, msg_map
from app.utils.ftp import FTP_OP

# 导入point_cloud项目的先进模块
ADVANCED_MODULES_AVAILABLE = False
load_point_cloud = None
preprocess_point_cloud = None
estimate_sensor_pose = None
fit_ground_plane = None
calculate_crop_height = None
create_plotly_static_image = None
create_plotly_rotating_gif = None

def init_advanced_modules():
    """初始化先进模块"""
    global ADVANCED_MODULES_AVAILABLE, load_point_cloud, preprocess_point_cloud
    global estimate_sensor_pose, fit_ground_plane, calculate_crop_height
    global create_plotly_static_image, create_plotly_rotating_gif

    try:
        import sys
        # 更新路径指向app/tasks/point_cloud
        point_cloud_path = os.path.join(os.path.dirname(__file__), '../tasks/point_cloud')
        if point_cloud_path not in sys.path:
            sys.path.append(point_cloud_path)

        from app.tasks.point_cloud.modules.data_loader import load_point_cloud as _load_point_cloud
        from app.tasks.point_cloud.modules.preprocessing import preprocess_point_cloud as _preprocess_point_cloud
        from app.tasks.point_cloud.modules.pose_estimation import estimate_sensor_pose as _estimate_sensor_pose
        from app.tasks.point_cloud.modules.ground_plane_fitting import fit_ground_plane as _fit_ground_plane
        from app.tasks.point_cloud.modules.crop_height import calculate_crop_height as _calculate_crop_height
        from app.tasks.point_cloud.modules.visualization import create_plotly_static_image as _create_plotly_static_image
        from app.tasks.point_cloud.modules.visualization import create_plotly_rotating_gif as _create_plotly_rotating_gif

        # 赋值给全局变量
        load_point_cloud = _load_point_cloud
        preprocess_point_cloud = _preprocess_point_cloud
        estimate_sensor_pose = _estimate_sensor_pose
        fit_ground_plane = _fit_ground_plane
        calculate_crop_height = _calculate_crop_height
        create_plotly_static_image = _create_plotly_static_image
        create_plotly_rotating_gif = _create_plotly_rotating_gif

        ADVANCED_MODULES_AVAILABLE = True
        return True
    except ImportError:
        ADVANCED_MODULES_AVAILABLE = False
        return False

# 导入异常处理（如果可用）
try:
    from app.core.exceptions import ValidationError, FileError, ProcessingError
except ImportError:
    # 定义简单的异常类作为后备
    class ValidationError(Exception):
        def __init__(self, message, **kwargs):
            super().__init__(message)
            self.message = message

    class FileError(Exception):
        def __init__(self, message, **kwargs):
            super().__init__(message)
            self.message = message

    class ProcessingError(Exception):
        def __init__(self, message, **kwargs):
            super().__init__(message)
            self.message = message

pif = ParseIniFile('app/config.ini')

# 尝试在模块加载时初始化先进模块
try:
    if init_advanced_modules():
        # print("✓ 成功初始化point_cloud先进模块")
        # print("  - 高度识别使用先进的DBSCAN聚类算法")
        # print("  - 可视化使用Plotly生成高质量图像和GIF")
        # print("  - 系统已移除老的算法，只使用最新的先进算法")
        pass
    else:
        print("✗ 无法初始化point_cloud先进模块")
        print("  - 系统将无法正常工作，请检查模块安装")
except Exception as e:
    print(f"✗ 初始化point_cloud先进模块时出错: {e}")
    print("  - 系统将无法正常工作，请检查模块安装")
    ADVANCED_MODULES_AVAILABLE = False


def height_recognition(csv_file, install_height=None, install_angle=None, target_length=None):
    """
    使用point_cloud项目的先进算法进行高度识别

    Args:
        csv_file (str): CSV文件路径
        install_height (float, optional): 安装高度，默认为None
        install_angle (float, optional): 安装角度，默认为None
        target_length (float, optional): 目标长度，默认为None

    Returns:
        tuple: (canopy_height, target_depth) 单位为毫米
    """
    # 确保先进模块已初始化
    if not ADVANCED_MODULES_AVAILABLE:
        init_success = init_advanced_modules()
        if not init_success:
            raise ProcessingError("无法初始化先进模块，高度识别失败")

    current_app.logger.info("使用point_cloud先进算法进行高度识别")

    # Step 1: 加载点云数据
    raw_data = load_point_cloud(csv_file)
    current_app.logger.info(f"加载点云数据: {len(raw_data)} 个点")

    # Step 2: 区域裁剪(如果指定了目标长度)
    if target_length is not None and target_length > 0:
        recognition_area_size = target_length / 1000.0  # 转换为米
        # 计算点云的中心点
        x_center = (raw_data['X'].min() + raw_data['X'].max()) / 2
        y_center = (raw_data['Y'].min() + raw_data['Y'].max()) / 2

        # 计算裁剪边界
        half_size = recognition_area_size / 2
        x_min = x_center - half_size
        x_max = x_center + half_size
        y_min = y_center - half_size
        y_max = y_center + half_size

        # 裁剪数据
        mask = (
            (raw_data['X'] >= x_min) & (raw_data['X'] <= x_max) &
            (raw_data['Y'] >= y_min) & (raw_data['Y'] <= y_max)
        )
        raw_data = raw_data[mask].copy()
        current_app.logger.info(f"裁剪到 {recognition_area_size}x{recognition_area_size} 米区域，剩余 {len(raw_data)} 个点")
    else:
        current_app.logger.info("未指定目标长度，使用全部点云数据进行处理")

    # Step 3: 预处理
    processed_data = preprocess_point_cloud(raw_data)
    current_app.logger.info("完成点云预处理")

    # Step 4: 位姿估计
    rotation_matrix, translation_vector = estimate_sensor_pose(processed_data)
    current_app.logger.info("完成传感器位姿估计")

    # Step 5: 地面拟合和坐标变换
    transformed_data, ground_plane_params = fit_ground_plane(
        processed_data, rotation_matrix, translation_vector
    )
    current_app.logger.info("完成地面拟合和坐标变换")

    # Step 6: 计算作物高度
    crop_height = calculate_crop_height(transformed_data)
    current_app.logger.info(f"计算得到作物高度: {crop_height:.3f} 米")

    # 转换为毫米并计算深度
    canopy_height_mm = crop_height * 1000

    # 如果提供了安装高度，计算目标深度；否则返回0
    if install_height is not None:
        target_depth_mm = install_height * 1000 - canopy_height_mm
    else:
        target_depth_mm = 0
        current_app.logger.info("未提供安装高度，目标深度设为0")

    return (canopy_height_mm, target_depth_mm)


def visualization_generation(csv_file, output_file, crop_height_m, isgif):
    """
    使用point_cloud项目的先进算法生成可视化图像

    Args:
        csv_file (str): CSV文件路径
        output_file (str): 输出文件路径
        crop_height_m (float): 作物高度(米)
        isgif (str): 是否生成GIF ("1"或"3"表示生成GIF)

    Returns:
        bool: 是否成功生成
    """
    # 确保先进模块已初始化
    if not ADVANCED_MODULES_AVAILABLE:
        init_success = init_advanced_modules()
        if not init_success:
            raise ProcessingError("无法初始化先进模块，可视化生成失败")

    current_app.logger.info("使用point_cloud先进算法生成可视化图像")

    # Step 1: 加载和处理点云数据
    raw_data = load_point_cloud(csv_file)
    processed_data = preprocess_point_cloud(raw_data)
    rotation_matrix, translation_vector = estimate_sensor_pose(processed_data)
    transformed_data, _ = fit_ground_plane(processed_data, rotation_matrix, translation_vector)

    # Step 2: 根据输出格式生成图像
    if isgif in ["1", "3"]:
        # 生成GIF动画
        create_plotly_rotating_gif(
            transformed_data,
            output_file,
            crop_height=crop_height_m,
            frames=6,  # 6帧360度旋转
            sample_size=25000
        )
        current_app.logger.info(f"成功生成GIF动画: {output_file}")
    else:
        # 生成静态图像
        create_plotly_static_image(
            transformed_data,
            output_file,
            crop_height=crop_height_m,
            sample_size=40000
        )
        current_app.logger.info(f"成功生成静态图像: {output_file}")

    return True


def validate_pointcloud_request(data):
    """验证点云识别请求数据"""
    if not data:
        raise ValidationError("请求数据为空")

    # 基本参数验证
    required_fields = ['datetime', 'detectType']
    missing_fields = [field for field in required_fields if not data.get(field)]
    if missing_fields:
        raise ValidationError(f"基本任务参数不完整，缺少: {', '.join(missing_fields)}")

    # FTP参数验证
    ftp_fields = ['srcURLList', 'srcLoginUser', 'srcLoginPasswd']
    missing_ftp_fields = [field for field in ftp_fields if not data.get(field)]
    if missing_ftp_fields:
        raise ValidationError(f"FTP请求参数不完整，缺少: {', '.join(missing_ftp_fields)}")

    # 高度参数验证（可选参数）
    install_height = data.get("installHeight")
    install_angle = data.get("installAngle")
    target_length = data.get("targetLength")

    # 如果提供了高度参数，则进行验证
    if install_height is not None:
        if not (0 <= install_height <= 50):
            raise ValidationError(f"安装高度参数违规: {install_height}，应在0-50范围内")

    if install_angle is not None:
        if not (0 <= install_angle <= 180):
            raise ValidationError(f"安装角度参数违规: {install_angle}，应在0-180范围内")

    if target_length is not None:
        if not (target_length > 0):
            raise ValidationError(f"目标长度参数违规: {target_length}，应大于0")

    return True


def download_pointcloud_files(urllist, log_user, log_pwd, dst_file_path):
    """下载点云文件"""
    try:
        if not os.path.exists(dst_file_path):
            os.makedirs(dst_file_path, exist_ok=True)

        downloaded_files = []
        for urlindex in urllist.keys():
            url = urllist[urlindex]
            try:
                url_host = url.split(':')[1][2:]
                url_port = url.split(':')[2].split('/')[0]
                ftp_file_path = '/'.join(url.split(':')[2].split('/')[1:])
                file_name = ftp_file_path.split("/")[-1]
                dst_file = os.path.join(dst_file_path, file_name)

                log_ftp = FTP_OP(url_host, log_user, log_pwd, int(url_port))
                log_ftp.download_file(ftp_file_path, dst_file)
                downloaded_files.append(dst_file)
                current_app.logger.info(f"点云文件下载成功: {ftp_file_path} -> {dst_file}")
            except Exception as e:
                current_app.logger.error(f"下载点云文件失败 {url}: {str(e)}")
                raise FileError(f"点云文件下载失败: {str(e)}")

        return downloaded_files
    except Exception as e:
        current_app.logger.error(f"点云FTP下载过程失败: {str(e)}")
        raise FileError(f"点云FTP下载过程失败: {str(e)}")


def create_pointcloud_success_response(result, date_time, detect_type, img_cloud):
    """创建点云识别成功响应"""
    response_data = {
        'datetime': date_time,
        'detectType': detect_type,
        'pointcloudImage': img_cloud,
        'status': "OK",
        'statusCode': RET.OK,
        'statusMsg': msg_map[RET.OK]
    }
    response_data.update(result)
    return json.dumps(response_data)


def create_pointcloud_error_response(status_code, status_msg, date_time=None, detect_type=None, img_cloud=None, partial_result=None):
    """创建点云识别错误响应"""
    response_data = {
        'status': 'EXCEPTION',
        'statusCode': status_code,
        'statusMsg': status_msg
    }

    if date_time:
        response_data['datetime'] = date_time
    if detect_type:
        response_data['detectType'] = detect_type
    if img_cloud is not None:
        response_data['pointcloudImage'] = img_cloud
    if partial_result:
        response_data.update(partial_result)

    return json.dumps(response_data)

@api.route('/detect_pointcloud', methods=['POST'])
def getjson_point():
    """
    点云识别接口 - 改进版本
    增强异常处理和稳定性
    """
    start_time = time.time()
    dst_file_path = ''
    result = {}
    data = None

    try:
        # 获取请求数据
        data = request.get_json()
        current_app.logger.info("收到点云识别请求")

        # 验证请求数据
        validate_pointcloud_request(data)

        # 提取参数
        date_time = data.get('datetime')
        detect_type = data.get('detectType')
        urllist = data.get("srcURLList")
        log_user = data.get("srcLoginUser")
        log_pwd = data.get("srcLoginPasswd")

        # 高度参数
        install_height = data.get("installHeight")
        install_angle = data.get("installAngle")
        target_length = data.get("targetLength")
        img_cloud = data.get("pointcloudImage", 0)

        # FTP文件下载
        static_dir = pif.get_data("common", "static_dir")
        dst_file_path = os.path.join(static_dir, 'ftp/pc')
        download_pointcloud_files(urllist, log_user, log_pwd, dst_file_path)

        # 文件格式判断和处理
        file_list = []
        for filename in os.listdir(dst_file_path):
            try:
                # 获取文件信息
                tz, _, _, _, _, _, _, jw, _, _ = get_file_info(filename)
                dst_filename = os.path.join(dst_file_path, filename)
                if check_file(dst_filename, 'csv'):
                    file_list.append((tz, jw, dst_filename))
                    current_app.logger.info(f"找到有效点云文件: {filename}")
            except Exception as e:
                current_app.logger.warning(f"解析文件信息失败 {filename}: {str(e)}")
                continue

        if not file_list:
            raise FileError("没有找到有效的点云文件")

        # 点云识别处理
        successful_files = 0

        for file_info in file_list:
            tz, jw, dst_filename = file_info
            try:
                current_app.logger.info(f"开始处理点云文件: {dst_filename}")

                # 高度识别 - 使用先进算法
                h_time = time.time()
                canopy_height, target_depth = height_recognition(
                    dst_filename, install_height, install_angle, target_length
                )
                current_app.logger.info("使用先进算法完成高度识别")

                result["canopyHeight"] = canopy_height
                result["targetDepth"] = target_depth
                result['qcCanopyHeight'] = 0
                current_app.logger.info(f"高度计算完成，用时: {time.time()-h_time:.2f}s")
                successful_files += 1

                # 群落图相关
                if img_cloud == 1:
                    try:
                        current_app.logger.info("开始生成群落图")
                        # 是否生成动图
                        isgif = pif.get_data("settings", "isgif")
                        current_app.logger.info(f"动图设置: {isgif}")

                        if isgif in ["1", "3"]:
                            out_file = f"pointcloudImage_{date_time}.gif" if date_time else "pointcloudImage.gif"
                        else:
                            out_file = f"pointcloudImage_{date_time}.jpg" if date_time else "pointcloudImage.jpg"

                        out_file_path = os.path.join(dst_file_path, out_file)
                        current_app.logger.info(f"输出文件路径: {out_file_path}")

                        p_time = time.time()

                        # 使用先进的可视化算法
                        crop_height_m = canopy_height / 1000.0  # 转换为米
                        visualization_generation(dst_filename, out_file_path, crop_height_m, isgif)
                        current_app.logger.info(f"三维点云图像已生成: {out_file_path}，用时: {time.time()-p_time:.2f}s")

                        # 群落图上传
                        dst_url = data.get("dstURL")
                        dst_user = data.get("dstLoginUser")
                        dst_pwd = data.get("dstLoginPasswd")

                        if all([dst_url, dst_user, dst_pwd]):
                            try:
                                current_app.logger.info("开始上传群落图")
                                url_host = dst_url.split(':')[1][2:]
                                url_port = dst_url.split(':')[2].split('/')[0]
                                ftp_path = '/'.join(dst_url.split(':')[2].split('/')[1:])

                                if isgif in ["1", "3"]:
                                    ftp_path = f"{ftp_path.split('.')[0]}.gif"

                                current_app.logger.info(f"上传路径: {ftp_path}")
                                log_ftp = FTP_OP(url_host, dst_user, dst_pwd, int(url_port))
                                log_ftp.upload_file(ftp_path, out_file_path)
                                current_app.logger.info("群落图上传成功")
                            except Exception as upload_e:
                                current_app.logger.error(f"群落图上传失败: {str(upload_e)}")
                        else:
                            current_app.logger.warning("群落图上传参数不完整，跳过上传")

                    except Exception as e:
                        current_app.logger.error(f"群落图生成失败: {str(e)}")
                        # 群落图生成失败不影响主要识别结果

            except Exception as e:
                current_app.logger.error(f"点云文件处理失败 {dst_filename}: {str(e)}")
                # 继续处理其他文件
                continue

        # 检查处理结果
        if successful_files == 0:
            raise ProcessingError("所有点云文件处理失败")

        if not all([result.get('canopyHeight') is not None, result.get('targetDepth') is not None]):
            raise ProcessingError("点云识别未返回有效结果")

        # 记录处理时间
        total_time = time.time() - start_time
        current_app.logger.info(f"点云识别完成，用时: {total_time:.2f}s，成功处理 {successful_files} 个文件")

        return create_pointcloud_success_response(result, date_time, detect_type, img_cloud)

    except ValidationError as e:
        current_app.logger.error(f"点云请求验证失败: {str(e)}")
        return create_pointcloud_error_response(RET.RPNFTASK, str(e),
                                              data.get('datetime') if data else None,
                                              data.get('detectType') if data else None,
                                              data.get('pointcloudImage') if data else None)

    except FileError as e:
        current_app.logger.error(f"点云文件处理失败: {str(e)}")
        return create_pointcloud_error_response(RET.FRFTPD, str(e),
                                              data.get('datetime') if data else None,
                                              data.get('detectType') if data else None,
                                              data.get('pointcloudImage') if data else None)

    except ProcessingError as e:
        current_app.logger.error(f"点云识别处理失败: {str(e)}")
        return create_pointcloud_error_response(RET.PCERR, str(e),
                                              data.get('datetime') if data else None,
                                              data.get('detectType') if data else None,
                                              data.get('pointcloudImage') if data else None,
                                              result if result else None)

    except Exception as e:
        current_app.logger.error(f"点云识别未预期错误: {str(e)}", exc_info=True)
        return create_pointcloud_error_response(RET.PCERR, "系统内部错误",
                                              data.get('datetime') if data else None,
                                              data.get('detectType') if data else None,
                                              data.get('pointcloudImage') if data else None)

    finally:
        # 确保文件清理
        try:
            if dst_file_path and os.path.exists(dst_file_path):
                del_file(dst_file_path)
                current_app.logger.info("点云文件清理完成")
        except Exception as cleanup_e:
            current_app.logger.error(f"文件清理失败: {str(cleanup_e)}")