# coding:utf-8

from . import api
import os
import json
import time
import shutil
from flask import request, current_app, jsonify
from app.utils.parse_ini import ParseIniFile
from app.tasks.handler import Handler
from app.utils.commons import check_file, del_file, get_file_info
from app.utils.response_code import RET, msg_map
from app.utils.ftp import FTP_OP
# 导入异常处理（如果可用）
try:
    from app.core.exceptions import ValidationError, FileError, ProcessingError
except ImportError:
    # 定义简单的异常类作为后备
    class ValidationError(Exception):
        def __init__(self, message, **kwargs):
            super().__init__(message)
            self.message = message

    class FileError(Exception):
        def __init__(self, message, **kwargs):
            super().__init__(message)
            self.message = message

    class ProcessingError(Exception):
        def __init__(self, message, **kwargs):
            super().__init__(message)
            self.message = message

pif = ParseIniFile('app/config.ini')


def validate_audio_request(data):
    """验证音频识别请求数据"""
    if not data:
        raise ValidationError("请求数据为空")

    # 基本参数验证
    required_fields = ['datetime', 'detectType']
    missing_fields = [field for field in required_fields if not data.get(field)]
    if missing_fields:
        raise ValidationError(f"基本任务参数不完整，缺少: {', '.join(missing_fields)}")

    # FTP参数验证
    ftp_fields = ['srcURLList', 'srcLoginUser', 'srcLoginPasswd']
    missing_ftp_fields = [field for field in ftp_fields if not data.get(field)]
    if missing_ftp_fields:
        raise ValidationError(f"FTP请求参数不完整，缺少: {', '.join(missing_ftp_fields)}")

    return True


def download_audio_files(urllist, log_user, log_pwd, dst_file_path):
    """下载音频文件"""
    try:
        if not os.path.exists(dst_file_path):
            os.makedirs(dst_file_path, exist_ok=True)

        downloaded_files = []
        for urlindex in urllist.keys():
            url = urllist[urlindex]
            try:
                url_host = url.split(':')[1][2:]
                url_port = url.split(':')[2].split('/')[0]
                ftp_file_path = '/'.join(url.split(':')[2].split('/')[1:])
                file_name = ftp_file_path.split("/")[-1]
                dst_file = os.path.join(dst_file_path, file_name)

                log_ftp = FTP_OP(url_host, log_user, log_pwd, int(url_port))
                log_ftp.download_file(ftp_file_path, dst_file)
                downloaded_files.append(dst_file)
                current_app.logger.info(f"音频文件下载成功: {ftp_file_path} -> {dst_file}")
            except Exception as e:
                current_app.logger.error(f"下载音频文件失败 {url}: {str(e)}")
                raise FileError(f"音频文件下载失败: {str(e)}")

        return downloaded_files
    except Exception as e:
        current_app.logger.error(f"音频FTP下载过程失败: {str(e)}")
        raise FileError(f"音频FTP下载过程失败: {str(e)}")


def create_audio_success_response(result, date_time, detect_type):
    """创建音频识别成功响应"""
    response_data = {
        'datetime': date_time,
        'detectType': detect_type,
        'status': "OK",
        'statusCode': RET.OK,
        'statusMsg': msg_map[RET.OK]
    }
    response_data.update(result)
    return json.dumps(response_data)


def create_audio_error_response(status_code, status_msg, date_time=None, detect_type=None, partial_result=None):
    """创建音频识别错误响应"""
    response_data = {
        'status': 'EXCEPTION',
        'statusCode': status_code,
        'statusMsg': status_msg
    }

    if date_time:
        response_data['datetime'] = date_time
    if detect_type:
        response_data['detectType'] = detect_type
    if partial_result:
        response_data.update(partial_result)

    return json.dumps(response_data)

@api.route('/detect_audio', methods=['POST'])
def getjson_audio():
    """
    音频识别接口 - 改进版本
    增强异常处理和稳定性
    """
    start_time = time.time()
    dst_file_path = ''
    result = {}
    data = None

    try:
        # 获取请求数据
        data = request.get_json()
        current_app.logger.info("收到音频识别请求")

        # 验证请求数据
        validate_audio_request(data)

        # 提取参数
        date_time = data.get('datetime')
        detect_type = data.get('detectType')
        urllist = data.get("srcURLList")
        log_user = data.get("srcLoginUser")
        log_pwd = data.get("srcLoginPasswd")

        # FTP文件下载
        static_dir = pif.get_data("common", "static_dir")
        dst_file_path = os.path.join(static_dir, 'ftp/sound')
        download_audio_files(urllist, log_user, log_pwd, dst_file_path)

        # 文件格式判断和处理
        file_list = []
        for filename in os.listdir(dst_file_path):
            try:
                # 获取文件信息
                tz, _, _, _, _, _, _, jw, _, _ = get_file_info(filename)
                dst_filename = os.path.join(dst_file_path, filename)
                if check_file(dst_filename, ['ogg', 'wav', 'mp3']):
                    file_list.append((tz, jw, dst_filename))
                    current_app.logger.info(f"找到有效音频文件: {filename}")
            except Exception as e:
                current_app.logger.warning(f"解析文件信息失败 {filename}: {str(e)}")
                continue

        if not file_list:
            raise FileError("没有找到有效的音频文件")

        # 音频识别处理
        result['soundList'] = []
        successful_files = 0

        for file_info in file_list:
            tz, jw, dst_filename = file_info
            try:
                current_app.logger.info(f"开始处理音频文件: {dst_filename}")
                sound_params = {
                    'file_path': dst_filename,
                    'env': 1
                }

                object_handler = Handler('sound', 'sound_list')
                sound_list = object_handler.get_results(sound_params)

                # 备份处理成功的文件
                try:
                    backup_dir = 'app/static/tmp'
                    if not os.path.exists(backup_dir):
                        os.makedirs(backup_dir, exist_ok=True)
                    shutil.copy(dst_filename, backup_dir)
                    current_app.logger.info(f"文件备份成功: {dst_filename}")
                except Exception as backup_e:
                    current_app.logger.warning(f"文件备份失败: {str(backup_e)}")

                result['soundList'] = sound_list
                successful_files += 1
                current_app.logger.info(f"音频识别成功: {dst_filename}")

            except Exception as e:
                current_app.logger.error(f"音频识别失败 {dst_filename}: {str(e)}")
                # 继续处理其他文件，不因单个文件失败而终止
                continue

        # 检查处理结果
        if successful_files == 0:
            raise ProcessingError("所有音频文件处理失败")

        if not result.get('soundList'):
            raise ProcessingError("音频识别未返回有效结果")

        # 记录处理时间
        total_time = time.time() - start_time
        current_app.logger.info(f"音频识别完成，用时: {total_time:.2f}s，成功处理 {successful_files} 个文件")

        return create_audio_success_response(result, date_time, detect_type)

    except ValidationError as e:
        current_app.logger.error(f"音频请求验证失败: {str(e)}")
        return create_audio_error_response(RET.RPNFTASK, str(e),
                                         data.get('datetime') if data else None,
                                         data.get('detectType') if data else None)

    except FileError as e:
        current_app.logger.error(f"音频文件处理失败: {str(e)}")
        return create_audio_error_response(RET.FRFTPD, str(e),
                                         data.get('datetime') if data else None,
                                         data.get('detectType') if data else None)

    except ProcessingError as e:
        current_app.logger.error(f"音频识别处理失败: {str(e)}")
        return create_audio_error_response(RET.SOUNDERR, str(e),
                                         data.get('datetime') if data else None,
                                         data.get('detectType') if data else None,
                                         result if result else None)

    except Exception as e:
        current_app.logger.error(f"音频识别未预期错误: {str(e)}", exc_info=True)
        return create_audio_error_response(RET.SOUNDERR, "系统内部错误",
                                         data.get('datetime') if data else None,
                                         data.get('detectType') if data else None)

    finally:
        # 确保文件清理
        try:
            if dst_file_path and os.path.exists(dst_file_path):
                del_file(dst_file_path)
                current_app.logger.info("音频文件清理完成")

            # 清理临时文件
            static_dir = pif.get_data("common", "static_dir")
            temp_dir = os.path.join(static_dir, 'temp/MelspecImage')
            if os.path.exists(temp_dir):
                del_file(temp_dir)
                current_app.logger.info("临时文件清理完成")
        except Exception as cleanup_e:
            current_app.logger.error(f"文件清理失败: {str(cleanup_e)}")

