#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Web Server 离线安装包测试脚本
用于验证所有模块的安装和功能
"""

import sys
import time
import traceback
from typing import Dict, List, Tuple

def print_header(title: str):
    """打印标题"""
    print("\n" + "=" * 60)
    print(f"  {title}")
    print("=" * 60)

def print_section(title: str):
    """打印章节"""
    print(f"\n📋 {title}")
    print("-" * 40)

def test_basic_imports() -> Dict[str, bool]:
    """测试基本导入"""
    print_section("基本导入测试")
    
    packages = {
        'numpy': 'numpy',
        'scipy': 'scipy', 
        'sklearn': 'sklearn',
        'plotly': 'plotly',
        'kaleido': 'kaleido'
    }
    
    results = {}
    
    for display_name, import_name in packages.items():
        try:
            module = __import__(import_name)
            version = getattr(module, '__version__', 'unknown')
            print(f"✅ {display_name:12} {version:10}")
            results[display_name] = True
        except ImportError as e:
            print(f"❌ {display_name:12} 导入失败: {e}")
            results[display_name] = False
        except Exception as e:
            print(f"❌ {display_name:12} 错误: {e}")
            results[display_name] = False
    
    return results

def test_numpy_functionality() -> bool:
    """测试 numpy 功能"""
    print_section("NumPy 功能测试")
    
    try:
        import numpy as np
        
        # 基本数组操作
        data = np.random.rand(1000, 3)
        print(f"✅ 创建随机数组: {data.shape}")
        
        # 数学运算
        mean = np.mean(data, axis=0)
        std = np.std(data, axis=0)
        print(f"✅ 统计计算: mean={mean[:2]}, std={std[:2]}")
        
        # 线性代数
        matrix = np.random.rand(100, 100)
        eigenvals = np.linalg.eigvals(matrix)
        print(f"✅ 线性代数: 特征值计算完成")
        
        return True
        
    except Exception as e:
        print(f"❌ NumPy 测试失败: {e}")
        return False

def test_scipy_functionality() -> bool:
    """测试 scipy 功能"""
    print_section("SciPy 功能测试")
    
    try:
        import numpy as np
        import scipy
        from scipy.spatial.distance import pdist, squareform
        from scipy import stats
        
        # 距离计算
        data = np.random.rand(100, 3)
        distances = pdist(data)
        print(f"✅ 距离计算: {len(distances)} 个距离")
        
        # 统计测试
        sample1 = np.random.normal(0, 1, 100)
        sample2 = np.random.normal(0.5, 1, 100)
        t_stat, p_value = stats.ttest_ind(sample1, sample2)
        print(f"✅ 统计测试: t={t_stat:.3f}, p={p_value:.3f}")
        
        return True
        
    except Exception as e:
        print(f"❌ SciPy 测试失败: {e}")
        return False

def test_sklearn_functionality() -> bool:
    """测试 scikit-learn 功能"""
    print_section("Scikit-learn 功能测试")
    
    try:
        import numpy as np
        import sklearn
        from sklearn.cluster import DBSCAN
        from sklearn.datasets import make_blobs
        
        # 生成测试数据
        X, _ = make_blobs(n_samples=300, centers=4, n_features=2, 
                         random_state=42, cluster_std=0.60)
        print(f"✅ 生成测试数据: {X.shape}")
        
        # DBSCAN 聚类
        clustering = DBSCAN(eps=0.3, min_samples=10)
        labels = clustering.fit_predict(X)
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        print(f"✅ DBSCAN 聚类: 发现 {n_clusters} 个聚类")
        
        return True
        
    except Exception as e:
        print(f"❌ Scikit-learn 测试失败: {e}")
        return False

def test_plotly_functionality() -> bool:
    """测试 plotly 功能"""
    print_section("Plotly 功能测试")
    
    try:
        import numpy as np
        import plotly
        import plotly.graph_objects as go
        
        # 生成3D数据
        n_points = 1000
        x = np.random.randn(n_points)
        y = np.random.randn(n_points)
        z = np.random.randn(n_points)
        colors = np.sqrt(x**2 + y**2 + z**2)
        
        # 创建3D散点图
        fig = go.Figure(data=go.Scatter3d(
            x=x, y=y, z=z,
            mode='markers',
            marker=dict(
                size=3,
                color=colors,
                colorscale='Viridis',
                showscale=True
            )
        ))
        
        fig.update_layout(
            title="3D 散点图测试",
            scene=dict(
                xaxis_title='X 轴',
                yaxis_title='Y 轴',
                zaxis_title='Z 轴'
            ),
            width=800,
            height=600
        )
        
        print(f"✅ 创建3D散点图: {n_points} 个点")
        print(f"✅ 图表配置: 800x600 像素")
        
        return True
        
    except Exception as e:
        print(f"❌ Plotly 测试失败: {e}")
        return False

def test_kaleido_functionality() -> bool:
    """测试 kaleido 功能"""
    print_section("Kaleido 功能测试")
    
    try:
        import numpy as np
        import plotly.graph_objects as go
        import kaleido
        
        # 创建简单图表
        x = np.linspace(0, 10, 100)
        y = np.sin(x)
        
        fig = go.Figure(data=go.Scatter(x=x, y=y, mode='lines'))
        fig.update_layout(
            title="正弦波测试",
            width=800,
            height=400
        )
        
        # 测试图像导出
        img_bytes = fig.to_image(format="png", width=800, height=400)
        print(f"✅ PNG 导出: {len(img_bytes)} 字节")
        
        img_bytes = fig.to_image(format="jpg", width=800, height=400)
        print(f"✅ JPG 导出: {len(img_bytes)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ Kaleido 测试失败: {e}")
        return False

def test_point_cloud_integration() -> bool:
    """测试 Point Cloud 集成功能"""
    print_section("Point Cloud 集成测试")
    
    try:
        import numpy as np
        import plotly.graph_objects as go
        from sklearn.cluster import DBSCAN
        from scipy.spatial.distance import pdist
        
        # 模拟点云数据
        np.random.seed(42)
        n_points = 5000
        
        # 生成地面点
        ground_x = np.random.uniform(-10, 10, n_points//2)
        ground_y = np.random.uniform(-10, 10, n_points//2)
        ground_z = np.random.normal(0, 0.1, n_points//2)
        
        # 生成作物点
        crop_x = np.random.uniform(-5, 5, n_points//2)
        crop_y = np.random.uniform(-5, 5, n_points//2)
        crop_z = np.random.uniform(0.5, 2.0, n_points//2)
        
        # 合并数据
        x = np.concatenate([ground_x, crop_x])
        y = np.concatenate([ground_y, crop_y])
        z = np.concatenate([ground_z, crop_z])
        
        points = np.column_stack([x, y, z])
        print(f"✅ 生成模拟点云: {points.shape[0]} 个点")
        
        # DBSCAN 聚类
        clustering = DBSCAN(eps=0.5, min_samples=20)
        labels = clustering.fit_predict(points)
        n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
        print(f"✅ DBSCAN 聚类: {n_clusters} 个聚类")
        
        # 计算作物高度
        crop_points = points[z > 0.3]  # 假设高于30cm的为作物
        if len(crop_points) > 0:
            crop_height = np.percentile(crop_points[:, 2], 95)
            print(f"✅ 作物高度计算: {crop_height:.3f} 米")
        
        # 创建3D可视化
        colors = labels + 1  # 避免负数颜色
        fig = go.Figure(data=go.Scatter3d(
            x=x, y=y, z=z,
            mode='markers',
            marker=dict(
                size=2,
                color=colors,
                colorscale='Viridis',
                showscale=True
            )
        ))
        
        fig.update_layout(
            title="Point Cloud 聚类可视化",
            scene=dict(
                xaxis_title='X (米)',
                yaxis_title='Y (米)',
                zaxis_title='Z (米)'
            ),
            width=1200,
            height=900
        )
        
        print(f"✅ 3D可视化: 1200x900 分辨率")
        
        # 测试图像导出
        img_bytes = fig.to_image(format="png", width=1200, height=900)
        print(f"✅ 高分辨率导出: {len(img_bytes)} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ Point Cloud 集成测试失败: {e}")
        traceback.print_exc()
        return False

def run_performance_test() -> Dict[str, float]:
    """运行性能测试"""
    print_section("性能测试")
    
    performance = {}
    
    # 测试导入时间
    modules = ['numpy', 'scipy', 'sklearn', 'plotly', 'kaleido']
    
    for module in modules:
        start_time = time.time()
        try:
            __import__(module)
            import_time = time.time() - start_time
            performance[f"{module}_import"] = import_time
            print(f"✅ {module:12} 导入时间: {import_time:.3f} 秒")
        except Exception as e:
            print(f"❌ {module:12} 导入失败: {e}")
            performance[f"{module}_import"] = -1
    
    return performance

def main():
    """主测试函数"""
    print_header("Web Server 离线安装包测试")
    
    # 收集测试结果
    results = {}
    
    # 1. 基本导入测试
    import_results = test_basic_imports()
    results.update(import_results)
    
    # 2. 功能测试
    if import_results.get('numpy', False):
        results['numpy_functionality'] = test_numpy_functionality()
    
    if import_results.get('scipy', False):
        results['scipy_functionality'] = test_scipy_functionality()
    
    if import_results.get('sklearn', False):
        results['sklearn_functionality'] = test_sklearn_functionality()
    
    if import_results.get('plotly', False):
        results['plotly_functionality'] = test_plotly_functionality()
    
    if import_results.get('kaleido', False):
        results['kaleido_functionality'] = test_kaleido_functionality()
    
    # 3. 集成测试
    if all(import_results.values()):
        results['point_cloud_integration'] = test_point_cloud_integration()
    
    # 4. 性能测试
    performance = run_performance_test()
    
    # 5. 总结报告
    print_header("测试总结报告")
    
    passed = sum(1 for v in results.values() if v)
    total = len(results)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    print("-" * 40)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name:25} {status}")
    
    print(f"\n⏱️  性能统计:")
    print("-" * 40)
    for perf_name, perf_time in performance.items():
        if perf_time >= 0:
            print(f"  {perf_name:25} {perf_time:.3f} 秒")
    
    if passed == total:
        print(f"\n🎉 所有测试通过！离线安装包工作正常。")
        print(f"✅ Point Cloud 先进算法所需的所有依赖都已正确安装。")
        return 0
    else:
        print(f"\n⚠️  {total - passed} 个测试失败，请检查安装。")
        return 1

if __name__ == "__main__":
    sys.exit(main())
