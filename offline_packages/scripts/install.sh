#!/bin/bash

# Web Server 离线安装包安装脚本
# 版本: 1.0
# 日期: 2025-06-30

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Python版本
check_python() {
    log_info "检查Python版本..."
    
    if ! command -v python3 &> /dev/null; then
        log_error "Python3 未安装，请先安装Python 3.7+"
        exit 1
    fi
    
    PYTHON_VERSION=$(python3 -c "import sys; print('.'.join(map(str, sys.version_info[:2])))")
    log_info "当前Python版本: $PYTHON_VERSION"
    
    # 检查是否为Python 3.7+
    if python3 -c "import sys; exit(0 if sys.version_info >= (3, 7) else 1)"; then
        log_success "Python版本检查通过"
    else
        log_error "需要Python 3.7或更高版本"
        exit 1
    fi
}

# 检查pip
check_pip() {
    log_info "检查pip..."
    
    if ! command -v pip3 &> /dev/null; then
        log_error "pip3 未安装，请先安装pip"
        exit 1
    fi
    
    log_success "pip检查通过"
}

# 安装离线包
install_packages() {
    log_info "开始安装离线包..."
    
    # 获取脚本所在目录
    SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
    WHEELS_DIR="$SCRIPT_DIR/../wheels"
    
    if [ ! -d "$WHEELS_DIR" ]; then
        log_error "wheels目录不存在: $WHEELS_DIR"
        exit 1
    fi
    
    log_info "从目录安装: $WHEELS_DIR"
    
    # 安装所有wheel文件
    pip3 install --no-index --find-links "$WHEELS_DIR" \
        kaleido==0.2.1 \
        plotly==5.18.0 \
        scikit-learn==1.0.2 \
        scipy==1.7.3 \
        numpy==1.21.6
    
    if [ $? -eq 0 ]; then
        log_success "所有包安装成功！"
    else
        log_error "包安装失败"
        exit 1
    fi
}

# 验证安装
verify_installation() {
    log_info "验证安装..."
    
    # 测试导入
    python3 -c "
import sys
import traceback

packages = {
    'kaleido': '0.2.1',
    'plotly': '5.18.0',
    'sklearn': '1.0.2',
    'scipy': '1.7.3',
    'numpy': '1.21.6'
}

failed = []
for package, expected_version in packages.items():
    try:
        if package == 'sklearn':
            import sklearn
            version = sklearn.__version__
            print(f'✓ {package}: {version}')
        else:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            print(f'✓ {package}: {version}')
    except ImportError as e:
        print(f'✗ {package}: 导入失败 - {e}')
        failed.append(package)
    except Exception as e:
        print(f'✗ {package}: 错误 - {e}')
        failed.append(package)

if failed:
    print(f'\\n失败的包: {failed}')
    sys.exit(1)
else:
    print('\\n✓ 所有包验证成功！')
"
    
    if [ $? -eq 0 ]; then
        log_success "安装验证通过！"
    else
        log_error "安装验证失败"
        exit 1
    fi
}

# 显示使用说明
show_usage() {
    echo ""
    log_info "安装完成！现在您可以使用以下功能："
    echo ""
    echo "  📊 Plotly 高质量3D可视化"
    echo "  🎯 DBSCAN聚类算法 (scikit-learn)"
    echo "  🔬 科学计算 (scipy)"
    echo "  📈 数值计算 (numpy)"
    echo "  🖼️  静态图像导出 (kaleido)"
    echo ""
    echo "这些模块支持 point_cloud 项目的先进算法："
    echo "  • 高精度作物高度识别"
    echo "  • 360度旋转GIF动画生成"
    echo "  • 高分辨率静态图像输出"
    echo ""
}

# 主函数
main() {
    echo "=================================================="
    echo "    Web Server 离线安装包安装程序"
    echo "=================================================="
    echo ""
    
    check_python
    check_pip
    install_packages
    verify_installation
    show_usage
    
    log_success "安装程序执行完成！"
}

# 执行主函数
main "$@"
