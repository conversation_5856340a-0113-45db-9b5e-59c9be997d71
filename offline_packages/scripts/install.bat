@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: Web Server 离线安装包安装脚本 (Windows)
:: 版本: 1.0
:: 日期: 2025-06-30

echo ==================================================
echo     Web Server 离线安装包安装程序 (Windows)
echo ==================================================
echo.

:: 检查Python
echo [INFO] 检查Python版本...
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python未安装，请先安装Python 3.7+
    pause
    exit /b 1
)

for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [INFO] 当前Python版本: %PYTHON_VERSION%

:: 检查pip
echo [INFO] 检查pip...
pip --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] pip未安装，请先安装pip
    pause
    exit /b 1
)

echo [SUCCESS] Python和pip检查通过

:: 获取脚本目录
set SCRIPT_DIR=%~dp0
set WHEELS_DIR=%SCRIPT_DIR%..\wheels

if not exist "%WHEELS_DIR%" (
    echo [ERROR] wheels目录不存在: %WHEELS_DIR%
    pause
    exit /b 1
)

echo [INFO] 开始安装离线包...
echo [INFO] 从目录安装: %WHEELS_DIR%

:: 安装包
pip install --no-index --find-links "%WHEELS_DIR%" kaleido==0.2.1 plotly==5.18.0 scikit-learn==1.0.2 scipy==1.7.3 numpy==1.21.6

if errorlevel 1 (
    echo [ERROR] 包安装失败
    pause
    exit /b 1
)

echo [SUCCESS] 所有包安装成功！

:: 验证安装
echo [INFO] 验证安装...

python -c "
import sys
packages = {
    'kaleido': '0.2.1',
    'plotly': '5.18.0', 
    'sklearn': '1.0.2',
    'scipy': '1.7.3',
    'numpy': '1.21.6'
}
failed = []
for package, expected_version in packages.items():
    try:
        if package == 'sklearn':
            import sklearn
            version = sklearn.__version__
            print(f'✓ {package}: {version}')
        else:
            module = __import__(package)
            version = getattr(module, '__version__', 'unknown')
            print(f'✓ {package}: {version}')
    except ImportError as e:
        print(f'✗ {package}: 导入失败 - {e}')
        failed.append(package)
    except Exception as e:
        print(f'✗ {package}: 错误 - {e}')
        failed.append(package)
if failed:
    print(f'失败的包: {failed}')
    sys.exit(1)
else:
    print('✓ 所有包验证成功！')
"

if errorlevel 1 (
    echo [ERROR] 安装验证失败
    pause
    exit /b 1
)

echo [SUCCESS] 安装验证通过！
echo.
echo [INFO] 安装完成！现在您可以使用以下功能：
echo.
echo   📊 Plotly 高质量3D可视化
echo   🎯 DBSCAN聚类算法 (scikit-learn)
echo   🔬 科学计算 (scipy)
echo   📈 数值计算 (numpy)
echo   🖼️  静态图像导出 (kaleido)
echo.
echo 这些模块支持 point_cloud 项目的先进算法：
echo   • 高精度作物高度识别
echo   • 360度旋转GIF动画生成  
echo   • 高分辨率静态图像输出
echo.
echo [SUCCESS] 安装程序执行完成！
echo.
pause
