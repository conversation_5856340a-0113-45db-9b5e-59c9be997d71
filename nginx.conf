#user www-data;
user root;
worker_processes auto;
pid /run/nginx.pid;
# pid /usr/local/nginx/nginx.pid;
include /etc/nginx/modules-enabled/*.conf;

events {
	worker_connections 768;
	# multi_accept on;
}

http {

	##
	# Basic Settings
	##

	sendfile on;
	tcp_nopush on;
	tcp_nodelay on;
	#超时时间
	keepalive_timeout 30;
	types_hash_max_size 2048;
	# server_tokens off;

	# server_names_hash_bucket_size 64;
	# server_name_in_redirect off;

	include /etc/nginx/mime.types;
	default_type application/octet-stream;

	client_max_body_size 5m;

	##
	# SSL Settings
	##

	ssl_protocols TLSv1 TLSv1.1 TLSv1.2; # Dropping SSLv3, ref: POODLE
	ssl_prefer_server_ciphers on;

	##
	# Logging Settings
	##

	access_log /var/log/nginx/access.log;
	error_log /var/log/nginx/error.log;

	##
	# Gzip Settings
	##

	gzip on;

	# gzip_vary on;
	# gzip_proxied any;
	# gzip_comp_level 6;
	# gzip_buffers 16 8k;
	# gzip_http_version 1.1;
	# gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

	##
	# Virtual Host Configs
	##

	include /etc/nginx/conf.d/*.conf;
	include /etc/nginx/sites-enabled/*;

	server{
		listen 80;
		listen [::]:80;
		server_name **************;
		#server_name 0.0.0.0;
		location / {
			root /etc/nginx/dist;
			index index.html index.htm;
			#uwsgi_send_timeout 300;        # 指定向uWSGI传送请求的超时时间，完成握手后向uWSGI传送请求的超时时间。
        	#uwsgi_connect_timeout 300;   # 指定连接到后端uWSGI的超时时间。
        	#uwsgi_read_timeout 300; 
			#include uwsgi_params;
			#uwsgi_pass 127.0.0.1:5001;
			#uwsgi_pass **************:5001;
			# proxy_pass http://**************:5001;
		}
		location /ng_status{
			stub_status on;
			access_log  off;
			allow 127.0.0.1;
			allow **************;
			deny all;
		} 
		location ~ /v1/detect_image {
			# uwsgi_pass 127.0.0.1:5000;
			uwsgi_send_timeout 300;        # 指定向uWSGI传送请求的超时时间，完成握手后向uWSGI传送请求的超时时间。
        	uwsgi_connect_timeout 300;   # 指定连接到后端uWSGI的超时时间。
        	uwsgi_read_timeout 300; 
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
			# proxy_pass http://**************:5001;
		}
		location ~ /v1/detect_audio {
			#proxy_pass http://**************:5001;
			uwsgi_send_timeout 300;        # 指定向uWSGI传送请求的超时时间，完成握手后向uWSGI传送请求的超时时间。
        	uwsgi_connect_timeout 300;   # 指定连接到后端uWSGI的超时时间。
        	uwsgi_read_timeout 300;      # 指定接收uWSGI应答的超时时间，完成握手后接收uWSGI应答的超时时间。
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
		}
		location ~ /v1/detect_pointcloud {
			#proxy_pass http://**************:5001;
			uwsgi_send_timeout 600;        # 指定向uWSGI传送请求的超时时间，完成握手后向uWSGI传送请求的超时时间。
        	uwsgi_connect_timeout 600;   # 指定连接到后端uWSGI的超时时间。
        	uwsgi_read_timeout 600;      # 指定接收uWSGI应答的超时时间，完成握手后接收uWSGI应答的超时时间。
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
		}
		location ~ /v1/state {
			#proxy_pass http://**************:5001;
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
		}
		location ~ /v1/shutdown {
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
		}
		location ~ /v1/merge {
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
		}
		location ~ /v1/chunk_upload {
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
		}
		location ~ /v1/run {
			uwsgi_send_timeout 600;        # 指定向uWSGI传送请求的超时时间，完成握手后向uWSGI传送请求的超时时间。
        	uwsgi_connect_timeout 600;   # 指定连接到后端uWSGI的超时时间。
        	uwsgi_read_timeout 600;      # 指定接收uWSGI应答的超时时间，完成握手后接收uWSGI应答的超时时间。
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
		}
		location ~ /v1/get_running_status {
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
		}
		location ~ /v1/download {
			include uwsgi_params;
			#uwsgi_pass **************:5001;
			uwsgi_pass 127.0.0.1:5001;
		}
	}
}
