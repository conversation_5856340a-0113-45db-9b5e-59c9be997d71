*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:11:22 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/web_server/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/web_server
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:12:50 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:13:46 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:14:23 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x556c7d8a90
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x556c7d8a90 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 10057, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:23:42 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:23:46 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5576302830
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:23:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:23:59 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x5576302830 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 11131, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:24:13 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:24:17 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559a6ae8a0
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:24:29 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 14 seconds on interpreter 0x559a6ae8a0 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 11315, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:24:44 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:24:48 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5572ca3900
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:25:00 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x5572ca3900 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 11456, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:25:15 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:25:19 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55a75bf970
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:25:33 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 14 seconds on interpreter 0x55a75bf970 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 11681, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:25:50 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:25:52 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5586ed59e0
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 14 seconds on interpreter 0x5586ed59e0 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 11871, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:26:08 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:26:25 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:26:27 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x558925aa50
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 14 seconds on interpreter 0x558925aa50 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 12077, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:26:43 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:27:00 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:27:04 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x555ea4fac0
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:27:17 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x555ea4fac0 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 12313, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:27:35 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:27:39 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559f4d0b30
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:27:52 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:27:53 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x559f4d0b30 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 12537, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:28:10 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:28:14 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55970a6b90
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:28:27 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 14 seconds on interpreter 0x55970a6b90 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 12753, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:28:45 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:28:49 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55bac2ec00
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:29:02 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x55bac2ec00 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 12898, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:29:20 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:29:20 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:29:24 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55a7c46c70
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:29:37 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 14 seconds on interpreter 0x55a7c46c70 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 13131, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:29:55 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:29:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55c0128ce0
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:30:12 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x55c0128ce0 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 13347, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:30:30 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:30:34 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557fddad50
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x557fddad50 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 13602, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:31:07 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:31:11 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557e883db0
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x557e883db0 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 13869, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...gracefully killing workers...
Gracefully killing worker 1 (pid: 13869)...
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:48:31 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55ade46e80
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x55ade46e80 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 15626, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 02:53:06 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559e89bef0
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 14 seconds on interpreter 0x559e89bef0 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 16158, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...gracefully killing workers...
Gracefully killing worker 1 (pid: 16158)...
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:05:25 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557f870f60
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 22 seconds on interpreter 0x557f870f60 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 17296, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...brutally killing workers...
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:14:07 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
probably another instance of uWSGI is running on the same address (127.0.0.1:5003).
bind(): Address already in use [core/socket.c line 769]
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.13.0
closing all non-uwsgi socket fds > 2 (max_fd = 1048576)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:14:10 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.13.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1048576
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55b4907f70
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x55b4907f70 pid: 9935 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 9935)
spawned uWSGI worker 1 (pid: 18196, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:23:50 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55b1c03350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 458 seconds on interpreter 0x55b1c03350 pid: 524 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 524)
spawned uWSGI worker 1 (pid: 729, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
apps/web_server/current/testcase/point/Y5734_LIDAR_20211101113000_00.csv
I:app:点云文件下载成功: apps/web_server/current/testcase/point/Y5734_LIDAR_20211101113000_00.csv -> app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I:app:找到有效点云文件: Y5734_LIDAR_20211101113000_00.csv
I:app:开始处理点云文件: app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.76it/s]
Reading CSV chunks: 2it [00:00,  2.17it/s]
Reading CSV chunks: 2it [00:00,  2.09it/s]
Loaded 200000 points from app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=0.000, max=11.202, mean=6.484
Y: min=-2.649, max=2.467, mean=-0.069
Z: min=-1.710, max=3.886, mean=0.202
I:app:加载点云数据: 200000 个点
I:app:未指定目标长度，使用全部点云数据进行处理
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 2185 statistical outliers (z-score > 3.0)
Subsampled from 197815 to 14888 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 14888 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [3.51193614 1.49023826 0.0449582 ]
Estimated ground plane normal: [-0.67427949  0.02380157  0.73809258]
Estimated rotation matrix:
[[ 0.73841852  0.00923363  0.67427949]
 [ 0.00923363  0.99967406 -0.02380157]
 [-0.67427949  0.02380157  0.73809258]]
Estimated translation vector: [0.         0.         4.68766034]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 4587 ground points out of 14888 total points
Ground plane equation: -0.0057x + -0.0072y + 1.0000z + 4.3389 = 0
Refined ground plane normal: [-0.00566114 -0.00719727  0.99995807]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 7082 points above ground threshold of 0.05m
DBSCAN found 9 clusters
Cluster with 11 points: height = 0.390m
Cluster with 11 points: height = 0.401m
Cluster with 14 points: height = 0.391m
Cluster with 10 points: height = 0.369m
Cluster with 10 points: height = 0.466m
Cluster with 11 points: height = 0.261m
Cluster with 10 points: height = 0.341m
Cluster with 10 points: height = 0.240m
Cluster with 10 points: height = 0.508m
Final estimated crop height: 0.508m
I:app:计算得到作物高度: 0.508 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 4.68s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.84it/s]
Reading CSV chunks: 2it [00:01,  1.87it/s]
Reading CSV chunks: 2it [00:01,  1.87it/s]
Loaded 200000 points from app/static/ftp/pc/Y5734_LIDAR_20211101113000_00.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=0.000, max=11.202, mean=6.484
Y: min=-2.649, max=2.467, mean=-0.069
Z: min=-1.710, max=3.886, mean=0.202
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 2185 statistical outliers (z-score > 3.0)
Subsampled from 197815 to 14888 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 14888 points
Estimating sensor pose...
PCA eigenvalues: [3.51193614 1.49023826 0.0449582 ]
Estimated ground plane normal: [-0.67427949  0.02380157  0.73809258]
Estimated rotation matrix:
[[ 0.73841852  0.00923363  0.67427949]
 [ 0.00923363  0.99967406 -0.02380157]
 [-0.67427949  0.02380157  0.73809258]]
Estimated translation vector: [0.         0.         4.68766034]
Fitting ground plane and transforming point cloud...
Identified 4587 ground points out of 14888 total points
Ground plane equation: -0.0057x + -0.0072y + 1.0000z + 4.3389 = 0
Refined ground plane normal: [-0.00566114 -0.00719727  0.99995807]
Creating 6 frame GIF with 360-degree rotation using Plotly...
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20211101113000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20211101113000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20211101113000.gif，用时: 85.50s
I:app:开始上传群落图
I:app:上传路径: apps/web_server/current/testcase/output/Y5734_LIDAR_20211101113000_00.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
apps/web_server/current/testcase/output/Y5734_LIDAR_20211101113000_00.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 90.57s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 729|app: 0|req: 1/1] 192.168.14.146 () {36 vars in 481 bytes} [Fri Jul 18 03:34:08 2025] POST /v1/detect_pointcloud => generated 221 bytes in 90606 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55912d6350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55912d6350 pid: 526 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 526)
spawned uWSGI worker 1 (pid: 763, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55a195e350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55a195e350 pid: 524 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 524)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:49:45 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250718/ALC_WX001_20250718140000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718140000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718140000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250718140000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718140000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718140000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.35it/s]
Reading CSV chunks: 2it [00:01,  1.83it/s]
Reading CSV chunks: 2it [00:01,  1.73it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718140000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-189302.915
Y: min=-2147483.750, max=2147483.750, mean=-189474.955
Z: min=-2147483.750, max=2147483.750, mean=-190269.693
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 4492 个点
Preprocessing point cloud with 4492 points...
Removed 0 points with NaN values
Removed 23 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 4469 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [2.98560640e+07 5.50009385e-02 5.10619415e-02]
Estimated ground plane normal: [ 6.16428387e-01 -7.87410975e-01  2.93957577e-06]
Estimated rotation matrix:
[[ 6.20017161e-01  4.85381050e-01 -6.16428387e-01]
 [ 4.85381050e-01  3.79985779e-01  7.87410975e-01]
 [ 6.16428387e-01 -7.87410975e-01  2.93967577e-06]]
Estimated translation vector: [0.         0.         0.26401569]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 2098 ground points out of 4469 total points
Ground plane equation: -0.1501x + -0.1173y + 0.9817z + -25.7216 = 0
Refined ground plane normal: [-0.15014337 -0.11728897  0.98168236]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 1579 points above ground threshold of 0.05m
DBSCAN found 23 clusters
Cluster with 19 points: height = 0.225m
Cluster with 30 points: height = 0.229m
Cluster with 42 points: height = 0.217m
Cluster with 21 points: height = 0.147m
Cluster with 18 points: height = 0.129m
Cluster with 19 points: height = 0.271m
Cluster with 21 points: height = 0.180m
Cluster with 14 points: height = 0.215m
Cluster with 27 points: height = 0.168m
Cluster with 18 points: height = 0.279m
Cluster with 20 points: height = 0.200m
Cluster with 10 points: height = 0.413m
Cluster with 14 points: height = 0.157m
Cluster with 23 points: height = 0.170m
Cluster with 11 points: height = 0.229m
Cluster with 21 points: height = 0.228m
Cluster with 18 points: height = 0.278m
Cluster with 10 points: height = 0.246m
Cluster with 10 points: height = 0.128m
Cluster with 10 points: height = 0.094m
Cluster with 11 points: height = 0.196m
Cluster with 10 points: height = 0.340m
Cluster with 10 points: height = 0.261m
Final estimated crop height: 0.413m
I:app:计算得到作物高度: 0.413 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 2.04s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250718140000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718140000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.70it/s]
Reading CSV chunks: 2it [00:01,  1.70it/s]
Reading CSV chunks: 2it [00:01,  1.70it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718140000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-189302.915
Y: min=-2147483.750, max=2147483.750, mean=-189474.955
Z: min=-2147483.750, max=2147483.750, mean=-190269.693
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 171 statistical outliers (z-score > 3.0)
Subsampled from 199829 to 153540 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 153540 points
Estimating sensor pose...
PCA eigenvalues: [7.11553882e+11 6.95037567e+11 5.25539627e+11]
Estimated ground plane normal: [0.61601622 0.62100161 0.48464525]
Estimated rotation matrix:
[[ 0.74439956 -0.257669   -0.61601622]
 [-0.257669    0.74024569 -0.62100161]
 [ 0.61601622  0.62100161  0.48464525]]
Estimated translation vector: [      0.               0.         3616698.88011642]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 153540 total points
Ground plane equation: 0.0047x + 0.1704y + 0.9854z + 0.0000 = 0
Refined ground plane normal: [0.00470329 0.17043464 0.98535776]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 12740 个异常高度点 (高于 0.620m)
显示 22 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250718140000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250718140000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250718140000.gif，用时: 78.50s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250718/ALC_WX001_20250718140000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250718/ALC_WX001_20250718140000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 82.93s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 722|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:49:45 2025] POST /v1/detect_pointcloud => generated 221 bytes in 82967 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 722|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:56:07 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55b4d04350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x55b4d04350 pid: 535 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 535)
spawned uWSGI worker 1 (pid: 726, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 726|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:37 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250718/ALC_WX001_20250718141000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718141000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718141000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250718141000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718141000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718141000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.21it/s]
Reading CSV chunks: 2it [00:01,  1.47it/s]
Reading CSV chunks: 2it [00:01,  1.43it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718141000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-190308.663
Y: min=-2147483.750, max=2147483.750, mean=-189880.386
Z: min=-2147483.750, max=2147483.750, mean=-189786.462
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 5006 个点
Preprocessing point cloud with 5006 points...
Removed 0 points with NaN values
Removed 16 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 4990 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [3.49670797e+08 5.72113853e-02 5.54457879e-02]
Estimated ground plane normal: [ 9.53379488e-01 -3.01774008e-01  1.23979817e-07]
Estimated rotation matrix:
[[ 9.10676647e-02  2.87705114e-01 -9.53379488e-01]
 [ 2.87705114e-01  9.08932459e-01  3.01774008e-01]
 [ 9.53379488e-01 -3.01774008e-01  1.24079817e-07]]
Estimated translation vector: [0.         0.         0.60115969]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 1883 ground points out of 4990 total points
Ground plane equation: -0.0283x + -0.0895y + 0.9956z + 0.0003 = 0
Refined ground plane normal: [-0.02832956 -0.08950061  0.99558379]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 1921 points above ground threshold of 0.05m
DBSCAN found 37 clusters
Cluster with 20 points: height = 0.243m
Cluster with 22 points: height = 0.176m
Cluster with 17 points: height = 0.166m
Cluster with 20 points: height = 0.208m
Cluster with 15 points: height = 0.198m
Cluster with 16 points: height = 0.215m
Cluster with 18 points: height = 0.287m
Cluster with 27 points: height = 0.267m
Cluster with 25 points: height = 0.177m
Cluster with 21 points: height = 0.234m
Cluster with 10 points: height = 0.217m
Cluster with 12 points: height = 0.181m
Cluster with 12 points: height = 0.131m
Cluster with 19 points: height = 0.205m
Cluster with 13 points: height = 0.215m
Cluster with 15 points: height = 0.236m
Cluster with 21 points: height = 0.204m
Cluster with 12 points: height = 0.458m
Cluster with 13 points: height = 0.217m
Cluster with 16 points: height = 0.375m
Cluster with 12 points: height = 0.165m
Cluster with 11 points: height = 0.131m
Cluster with 13 points: height = 0.417m
Cluster with 20 points: height = 0.248m
Cluster with 10 points: height = 0.178m
Cluster with 14 points: height = 0.201m
Cluster with 10 points: height = 0.208m
Cluster with 16 points: height = 0.216m
Cluster with 13 points: height = 0.364m
Cluster with 11 points: height = 0.205m
Cluster with 10 points: height = 0.293m
Cluster with 10 points: height = 0.225m
Cluster with 10 points: height = 0.206m
Cluster with 11 points: height = 0.207m
Cluster with 14 points: height = 0.276m
Final estimated crop height: 0.458m
I:app:计算得到作物高度: 0.458 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 2.60s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250718141000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718141000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.70it/s]
Reading CSV chunks: 2it [00:00,  2.13it/s]
Reading CSV chunks: 2it [00:01,  1.93it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718141000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-190308.663
Y: min=-2147483.750, max=2147483.750, mean=-189880.386
Z: min=-2147483.750, max=2147483.750, mean=-189786.462
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 172 statistical outliers (z-score > 3.0)
Subsampled from 199828 to 153895 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 153895 points
Estimating sensor pose...
PCA eigenvalues: [7.01086704e+11 6.82413537e+11 5.18128505e+11]
Estimated ground plane normal: [0.61361961 0.61780881 0.4917146 ]
Estimated rotation matrix:
[[ 0.74758642 -0.25413682 -0.61361961]
 [-0.25413682  0.74412818 -0.61780881]
 [ 0.61361961  0.61780881  0.4917146 ]]
Estimated translation vector: [      0.               0.         3469501.31290724]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 153895 total points
Ground plane equation: 0.0047x + 0.0767y + 0.9970z + 0.0000 = 0
Refined ground plane normal: [0.00474857 0.07671378 0.99704185]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 12758 个异常高度点 (高于 0.686m)
显示 28 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250718141000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250718141000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250718141000.gif，用时: 80.01s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250718/ALC_WX001_20250718141000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250718/ALC_WX001_20250718141000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 85.08s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 726|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:37 2025] POST /v1/detect_pointcloud => generated 220 bytes in 85122 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 726|app: 0|req: 3/3] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:35:39 2025] POST /v1/state => generated 70 bytes in 10 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250718/WX001_01_20250718140000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250718/WX001_01_20250718140000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250718140000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250718140000_TIMING.wav
I:app:找到有效音频文件: HB001_20230729210000_TIMING.mp3
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250718140000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 726) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 811)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250718/WX001_01_20250718140000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250718/WX001_01_20250718140000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250718140000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250718140000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250718140000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 811) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 822)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 822|app: 0|req: 6/4] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:55 2025] POST /v1/shutdown => generated 22 bytes in 55 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x558ce61350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x558ce61350 pid: 532 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 532)
spawned uWSGI worker 1 (pid: 723, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 723|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:42 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250718/ALC_WX001_20250718142000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718142000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718142000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250718142000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718142000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718142000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.21it/s]
Reading CSV chunks: 2it [00:01,  1.67it/s]
Reading CSV chunks: 2it [00:01,  1.58it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718142000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-187023.078
Y: min=-2147483.750, max=2147483.750, mean=-187836.171
Z: min=-2147483.750, max=2147483.750, mean=-186444.662
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 5125 个点
Preprocessing point cloud with 5125 points...
Removed 0 points with NaN values
Removed 35 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 5090 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [4.18265540e+07 5.24686766e-02 4.91385070e-02]
Estimated ground plane normal: [-6.18983460e-01 -7.85404021e-01  2.00809543e-06]
Estimated rotation matrix:
[[ 6.16860246e-01 -4.86151122e-01  6.18983460e-01]
 [-4.86151122e-01  3.83141762e-01  7.85404021e-01]
 [-6.18983460e-01 -7.85404021e-01  2.00819543e-06]]
Estimated translation vector: [0.         0.         0.43924696]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 2378 ground points out of 5090 total points
Ground plane equation: 0.1005x + -0.0791y + 0.9918z + -10.2394 = 0
Refined ground plane normal: [ 0.10053838 -0.07913615  0.99178098]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 1730 points above ground threshold of 0.05m
DBSCAN found 33 clusters
Cluster with 55 points: height = 0.372m
Cluster with 59 points: height = 0.304m
Cluster with 24 points: height = 0.250m
Cluster with 38 points: height = 0.379m
Cluster with 25 points: height = 0.299m
Cluster with 18 points: height = 0.118m
Cluster with 20 points: height = 0.183m
Cluster with 48 points: height = 0.354m
Cluster with 35 points: height = 0.389m
Cluster with 31 points: height = 0.425m
Cluster with 15 points: height = 0.412m
Cluster with 16 points: height = 0.175m
Cluster with 10 points: height = 0.128m
Cluster with 15 points: height = 0.073m
Cluster with 16 points: height = 0.134m
Cluster with 18 points: height = 0.234m
Cluster with 13 points: height = 0.133m
Cluster with 17 points: height = 0.203m
Cluster with 31 points: height = 0.322m
Cluster with 33 points: height = 0.349m
Cluster with 14 points: height = 0.177m
Cluster with 14 points: height = 0.170m
Cluster with 13 points: height = 0.126m
Cluster with 19 points: height = 0.243m
Cluster with 12 points: height = 0.161m
Cluster with 14 points: height = 0.182m
Cluster with 12 points: height = 0.114m
Cluster with 11 points: height = 0.385m
Cluster with 15 points: height = 0.175m
Cluster with 11 points: height = 0.225m
Cluster with 10 points: height = 0.198m
Cluster with 10 points: height = 0.235m
Final estimated crop height: 0.425m
I:app:计算得到作物高度: 0.425 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 2.20s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250718142000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718142000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.41it/s]
Reading CSV chunks: 2it [00:00,  2.54it/s]
Reading CSV chunks: 2it [00:00,  2.52it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718142000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-187023.078
Y: min=-2147483.750, max=2147483.750, mean=-187836.171
Z: min=-2147483.750, max=2147483.750, mean=-186444.662
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 207 statistical outliers (z-score > 3.0)
Subsampled from 199793 to 153543 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 153543 points
Estimating sensor pose...
PCA eigenvalues: [6.93075419e+11 6.77475782e+11 5.10971266e+11]
Estimated ground plane normal: [0.61475632 0.62014199 0.48733827]
Estimated rotation matrix:
[[ 0.74590492 -0.25632112 -0.61475632]
 [-0.25632112  0.74143334 -0.62014199]
 [ 0.61475632  0.62014199  0.48733827]]
Estimated translation vector: [      0.               0.         3497971.20320774]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 153543 total points
Ground plane equation: 0.1458x + -0.0262y + 0.9890z + 0.0000 = 0
Refined ground plane normal: [ 0.1457766  -0.02620259  0.98897048]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 13019 个异常高度点 (高于 0.637m)
显示 7 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250718142000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250718142000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250718142000.gif，用时: 78.56s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250718/ALC_WX001_20250718142000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250718/ALC_WX001_20250718142000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 83.12s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 723|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:42 2025] POST /v1/detect_pointcloud => generated 221 bytes in 83147 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 723|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:05 2025] POST /v1/shutdown => generated 22 bytes in 20 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55804ab350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x55804ab350 pid: 529 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 529)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:41 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250718/ALC_WX001_20250718143000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718143000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718143000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250718143000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718143000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718143000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.47it/s]
Reading CSV chunks: 2it [00:01,  1.92it/s]
Reading CSV chunks: 2it [00:01,  1.84it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718143000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-184782.377
Y: min=-2147483.750, max=2147483.750, mean=-184603.106
Z: min=-2147483.750, max=2147483.750, mean=-184547.929
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 5277 个点
Preprocessing point cloud with 5277 points...
Removed 0 points with NaN values
Removed 32 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 5245 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [1.93362489e+07 5.05955113e-02 4.94935646e-02]
Estimated ground plane normal: [ 4.65735822e-02 -9.98914862e-01  5.56220503e-06]
Estimated rotation matrix:
[[ 9.97830914e-01  4.65227846e-02 -4.65735822e-02]
 [ 4.65227846e-02  2.17464879e-03  9.98914862e-01]
 [ 4.65735822e-02 -9.98914862e-01  5.56230503e-06]]
Estimated translation vector: [ 0.          0.         -0.05467968]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 2658 ground points out of 5245 total points
Ground plane equation: 0.1226x + 0.0059y + 0.9924z + -28.1399 = 0
Refined ground plane normal: [0.12263883 0.00593172 0.99243364]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 2011 points above ground threshold of 0.05m
DBSCAN found 31 clusters
Cluster with 30 points: height = 0.243m
Cluster with 35 points: height = 0.236m
Cluster with 38 points: height = 0.173m
Cluster with 26 points: height = 0.115m
Cluster with 41 points: height = 0.233m
Cluster with 17 points: height = 0.207m
Cluster with 11 points: height = 0.260m
Cluster with 40 points: height = 0.238m
Cluster with 54 points: height = 0.186m
Cluster with 27 points: height = 0.149m
Cluster with 53 points: height = 0.241m
Cluster with 25 points: height = 0.170m
Cluster with 35 points: height = 0.189m
Cluster with 32 points: height = 0.303m
Cluster with 16 points: height = 0.202m
Cluster with 33 points: height = 0.254m
Cluster with 24 points: height = 0.261m
Cluster with 19 points: height = 0.368m
Cluster with 27 points: height = 0.267m
Cluster with 17 points: height = 0.214m
Cluster with 16 points: height = 0.266m
Cluster with 15 points: height = 0.154m
Cluster with 11 points: height = 0.414m
Cluster with 22 points: height = 0.233m
Cluster with 21 points: height = 0.227m
Cluster with 21 points: height = 0.223m
Cluster with 10 points: height = 0.211m
Cluster with 16 points: height = 0.179m
Cluster with 10 points: height = 0.208m
Cluster with 14 points: height = 0.256m
Cluster with 19 points: height = 0.135m
Final estimated crop height: 0.414m
I:app:计算得到作物高度: 0.414 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 2.05s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250718143000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718143000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.50it/s]
Reading CSV chunks: 2it [00:01,  1.50it/s]
Reading CSV chunks: 2it [00:01,  1.50it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718143000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-184782.377
Y: min=-2147483.750, max=2147483.750, mean=-184603.106
Z: min=-2147483.750, max=2147483.750, mean=-184547.929
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 226 statistical outliers (z-score > 3.0)
Subsampled from 199774 to 153475 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 153475 points
Estimating sensor pose...
PCA eigenvalues: [6.86671681e+11 6.67766848e+11 5.11130318e+11]
Estimated ground plane normal: [0.62147019 0.61261525 0.48834143]
Estimated rotation matrix:
[[ 0.7404996  -0.25580294 -0.62147019]
 [-0.25580294  0.74784183 -0.61261525]
 [ 0.62147019  0.61261525  0.48834143]]
Estimated translation vector: [      0.               0.         3489739.86633671]
Fitting ground plane and transforming point cloud...
E:app:群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I:app:点云识别完成，用时: 10.22s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 722|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:41 2025] POST /v1/detect_pointcloud => generated 220 bytes in 10230 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 722|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:38:51 2025] POST /v1/shutdown => generated 22 bytes in 12 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55b4818350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 18 seconds on interpreter 0x55b4818350 pid: 524 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 524)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250718/ALC_WX001_20250718150000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718150000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718150000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250718150000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718150000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718150000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.20it/s]
Reading CSV chunks: 2it [00:01,  1.62it/s]
Reading CSV chunks: 2it [00:01,  1.54it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718150000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-188103.818
Y: min=-2147483.750, max=2147483.750, mean=-187768.505
Z: min=-2147483.750, max=2147483.750, mean=-187231.392
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 4593 个点
Preprocessing point cloud with 4593 points...
Removed 0 points with NaN values
Removed 25 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 4568 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [3.74628973e+07 5.67457230e-02 5.56244706e-02]
Estimated ground plane normal: [ 1.02795241e-01 -9.94702538e-01  3.17679803e-06]
Estimated rotation matrix:
[[ 9.89433172e-01  1.02250362e-01 -1.02795241e-01]
 [ 1.02250362e-01  1.05700049e-02  9.94702538e-01]
 [ 1.02795241e-01 -9.94702538e-01  3.17689803e-06]]
Estimated translation vector: [0.         0.         0.12709011]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 2158 ground points out of 4568 total points
Ground plane equation: 0.0775x + 0.0082y + 0.9970z + -25.7898 = 0
Refined ground plane normal: [0.07754674 0.00821288 0.99695489]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 1889 points above ground threshold of 0.05m
DBSCAN found 25 clusters
Cluster with 19 points: height = 0.273m
Cluster with 14 points: height = 0.164m
Cluster with 27 points: height = 0.190m
Cluster with 29 points: height = 0.219m
Cluster with 17 points: height = 0.184m
Cluster with 12 points: height = 0.184m
Cluster with 14 points: height = 0.229m
Cluster with 23 points: height = 0.175m
Cluster with 16 points: height = 0.299m
Cluster with 12 points: height = 0.341m
Cluster with 24 points: height = 0.267m
Cluster with 37 points: height = 0.226m
Cluster with 19 points: height = 0.272m
Cluster with 42 points: height = 0.316m
Cluster with 16 points: height = 0.336m
Cluster with 25 points: height = 0.131m
Cluster with 28 points: height = 0.224m
Cluster with 15 points: height = 0.173m
Cluster with 26 points: height = 0.235m
Cluster with 18 points: height = 0.235m
Cluster with 10 points: height = 0.222m
Cluster with 15 points: height = 0.260m
Cluster with 13 points: height = 0.270m
Cluster with 10 points: height = 0.263m
Cluster with 10 points: height = 0.181m
Final estimated crop height: 0.341m
I:app:计算得到作物高度: 0.341 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 2.18s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250718150000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718150000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.48it/s]
Reading CSV chunks: 2it [00:01,  1.79it/s]
Reading CSV chunks: 2it [00:01,  1.74it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718150000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-188103.818
Y: min=-2147483.750, max=2147483.750, mean=-187768.505
Z: min=-2147483.750, max=2147483.750, mean=-187231.392
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 202 statistical outliers (z-score > 3.0)
Subsampled from 199798 to 154116 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 154116 points
Estimating sensor pose...
PCA eigenvalues: [6.96322543e+11 6.76567214e+11 5.14060099e+11]
Estimated ground plane normal: [0.61988151 0.61168026 0.4915223 ]
Estimated rotation matrix:
[[ 0.74237523 -0.2542163  -0.61988151]
 [-0.2542163   0.74914707 -0.61168026]
 [ 0.61988151  0.61168026  0.4915223 ]]
Estimated translation vector: [      0.               0.         3500366.01014229]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 154116 total points
Ground plane equation: 0.3499x + 0.3455y + 0.8707z + 0.0000 = 0
Refined ground plane normal: [0.34992625 0.34552692 0.87072543]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 13217 个异常高度点 (高于 0.511m)
显示 5 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250718150000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250718150000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250718150000.gif，用时: 78.54s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250718/ALC_WX001_20250718150000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250718/ALC_WX001_20250718150000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 83.10s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 722|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 219 bytes in 83134 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 722|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:03 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557bf60350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x557bf60350 pid: 535 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 535)
spawned uWSGI worker 1 (pid: 720, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 720|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:37 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250718/WX001_01_20250718150000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250718/WX001_01_20250718150000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250718150000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250718150000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250718150000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 720) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 740)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250718/WX001_01_20250718150000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250718/WX001_01_20250718150000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250718150000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250718150000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250718150000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 740) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 751)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 751|app: 0|req: 4/2] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:38:53 2025] POST /v1/shutdown => generated 22 bytes in 55 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55902d4350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55902d4350 pid: 527 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 527)
spawned uWSGI worker 1 (pid: 719, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 719|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:42 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250718/ALC_WX001_20250718152000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250718/ALC_WX001_20250718152000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250718152000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250718152000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250718152000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718152000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.45it/s]
Reading CSV chunks: 2it [00:01,  1.75it/s]
Reading CSV chunks: 2it [00:01,  1.69it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718152000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-189475.168
Y: min=-2147483.750, max=2147483.750, mean=-187739.530
Z: min=-2147483.750, max=2147483.750, mean=-187757.056
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 5334 个点
Preprocessing point cloud with 5334 points...
Removed 0 points with NaN values
Removed 23 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 5311 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [2.97085817e+07 5.39843950e-02 5.11865675e-02]
Estimated ground plane normal: [-8.81295641e-01 -4.72565331e-01  1.72619339e-06]
Estimated rotation matrix:
[[ 2.23319333e-01 -4.16469048e-01  8.81295641e-01]
 [-4.16469048e-01  7.76682393e-01  4.72565331e-01]
 [-8.81295641e-01 -4.72565331e-01  1.72629339e-06]]
Estimated translation vector: [0.         0.         0.44588697]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 2206 ground points out of 5311 total points
Ground plane equation: 0.1465x + -0.2732y + 0.9507z + -7.8909 = 0
Refined ground plane normal: [ 0.14654295 -0.27316413  0.95073999]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 1847 points above ground threshold of 0.05m
DBSCAN found 28 clusters
Cluster with 50 points: height = 0.356m
Cluster with 54 points: height = 0.378m
Cluster with 13 points: height = 0.398m
Cluster with 66 points: height = 0.357m
Cluster with 19 points: height = 0.199m
Cluster with 21 points: height = 0.174m
Cluster with 31 points: height = 0.287m
Cluster with 23 points: height = 0.271m
Cluster with 57 points: height = 0.322m
Cluster with 58 points: height = 0.449m
Cluster with 28 points: height = 0.327m
Cluster with 63 points: height = 0.427m
Cluster with 20 points: height = 0.182m
Cluster with 25 points: height = 0.388m
Cluster with 26 points: height = 0.232m
Cluster with 18 points: height = 0.192m
Cluster with 28 points: height = 0.315m
Cluster with 17 points: height = 0.276m
Cluster with 58 points: height = 0.353m
Cluster with 20 points: height = 0.234m
Cluster with 15 points: height = 0.276m
Cluster with 10 points: height = 0.248m
Cluster with 11 points: height = 0.217m
Cluster with 10 points: height = 0.285m
Cluster with 10 points: height = 0.154m
Cluster with 10 points: height = 0.213m
Cluster with 10 points: height = 0.217m
Cluster with 10 points: height = 0.269m
Final estimated crop height: 0.449m
I:app:计算得到作物高度: 0.449 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 2.10s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250718152000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250718152000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.71it/s]
Reading CSV chunks: 2it [00:00,  2.14it/s]
Reading CSV chunks: 2it [00:00,  2.06it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250718152000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=-189475.168
Y: min=-2147483.750, max=2147483.750, mean=-187739.530
Z: min=-2147483.750, max=2147483.750, mean=-187757.056
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 186 statistical outliers (z-score > 3.0)
Subsampled from 199814 to 153475 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 153475 points
Estimating sensor pose...
PCA eigenvalues: [6.99280590e+11 6.80516739e+11 5.07155188e+11]
Estimated ground plane normal: [0.61248172 0.61766341 0.49331334]
Estimated rotation matrix:
[[ 0.74879093 -0.25333434 -0.61248172]
 [-0.25333434  0.74452242 -0.61766341]
 [ 0.61248172  0.61766341  0.49331334]]
Estimated translation vector: [      0.               0.         3479740.07810046]
Fitting ground plane and transforming point cloud...
E:app:群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I:app:点云识别完成，用时: 9.78s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 719|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:42 2025] POST /v1/detect_pointcloud => generated 222 bytes in 9795 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 719|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:38:51 2025] POST /v1/shutdown => generated 22 bytes in 12 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5593096350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x5593096350 pid: 528 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 528)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559d805350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x559d805350 pid: 536 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 536)
spawned uWSGI worker 1 (pid: 724, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
Gracefully killing worker 1 (pid: 724)...
...gracefully killing workers...
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.15.0
closing all non-uwsgi socket fds > 2 (max_fd = 1024)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 04:31:59 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559e42b250
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 15 seconds on interpreter 0x559e42b250 pid: 536 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 536)
spawned uWSGI worker 1 (pid: 779, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...gracefully killing workers...
Gracefully killing worker 1 (pid: 779)...
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.15.0
closing all non-uwsgi socket fds > 2 (max_fd = 1024)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 04:32:57 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559d7b42c0
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 13 seconds on interpreter 0x559d7b42c0 pid: 536 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 536)
spawned uWSGI worker 1 (pid: 810, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
Gracefully killing worker 1 (pid: 810)...
...gracefully killing workers...
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.15.0
closing all non-uwsgi socket fds > 2 (max_fd = 1024)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 04:33:31 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x558aefc380
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 13 seconds on interpreter 0x558aefc380 pid: 536 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 536)
spawned uWSGI worker 1 (pid: 833, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55ac731350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55ac731350 pid: 530 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 530)
spawned uWSGI worker 1 (pid: 723, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559c0b1350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x559c0b1350 pid: 526 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 526)
spawned uWSGI worker 1 (pid: 723, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55813df350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55813df350 pid: 529 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 529)
spawned uWSGI worker 1 (pid: 727, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55a3701350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x55a3701350 pid: 531 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 531)
spawned uWSGI worker 1 (pid: 724, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
Gracefully killing worker 1 (pid: 724)...
...gracefully killing workers...
worker 1 buried after 6 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.15.0
closing all non-uwsgi socket fds > 2 (max_fd = 1024)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:34:38 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x557e37c250
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_web_server_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_web_server_manage:Application will run on 127.0.0.1:5001
WSGI app 0 (mountpoint='') ready in 14 seconds on interpreter 0x557e37c250 pid: 531 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 531)
spawned uWSGI worker 1 (pid: 758, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
...gracefully killing workers...
Gracefully killing worker 1 (pid: 758)...
worker 1 buried after 5 seconds
binary reloading uWSGI...
chdir() to /home/<USER>/apps/web_server/versions/v1.15.0
closing all non-uwsgi socket fds > 2 (max_fd = 1024)...
found fd 3 mapped to socket 0 (127.0.0.1:5003)
running /home/<USER>/.local/bin/uwsgi
[uWSGI] getting INI configuration from /home/<USER>/apps/web_server/current/uwsgi.ini
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:34:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 inherited INET address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55938a0320
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 13 seconds on interpreter 0x55938a0320 pid: 531 (default app)
*** uWSGI is running in multiple interpreter mode ***
gracefully (RE)spawned uWSGI master process (pid: 531)
spawned uWSGI worker 1 (pid: 780, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x558e6d2350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x558e6d2350 pid: 528 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 528)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:42 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722141000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722141000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722141000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722141000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722141000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722141000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.52it/s]
Reading CSV chunks: 2it [00:01,  2.08it/s]
Reading CSV chunks: 2it [00:01,  1.97it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722141000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=52456.668
Y: min=-2147483.750, max=2147483.750, mean=52285.825
Z: min=-2147483.750, max=2147483.750, mean=52445.273
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 66875 个点
Preprocessing point cloud with 66875 points...
Removed 0 points with NaN values
Removed 448 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 66427 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [4.91137730e+07 1.24989966e-02 3.58604133e-03]
Estimated ground plane normal: [6.48062063e-03 9.99979001e-01 2.70886134e-06]
Estimated rotation matrix:
[[ 9.99958002e-01 -6.48046699e-03 -6.48062063e-03]
 [-6.48046699e-03  4.47072913e-05 -9.99979001e-01]
 [ 6.48062063e-03  9.99979001e-01  2.70896134e-06]]
Estimated translation vector: [0.         0.         0.26085125]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 61452 ground points out of 66427 total points
Ground plane equation: -0.0395x + 0.0003y + 0.9992z + -0.0021 = 0
Refined ground plane normal: [-3.94869391e-02  2.56674664e-04  9.99220054e-01]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6586 points above ground threshold of 0.05m
DBSCAN found 27 clusters
Cluster with 4755 points: height = 0.331m
Cluster with 22 points: height = 0.077m
Cluster with 11 points: height = 0.074m
Cluster with 14 points: height = 0.074m
Cluster with 22 points: height = 0.075m
Cluster with 14 points: height = 0.077m
Cluster with 11 points: height = 0.069m
Cluster with 22 points: height = 0.082m
Cluster with 15 points: height = 0.076m
Cluster with 14 points: height = 0.074m
Cluster with 12 points: height = 0.085m
Cluster with 16 points: height = 0.076m
Cluster with 20 points: height = 0.079m
Cluster with 12 points: height = 0.075m
Cluster with 11 points: height = 0.079m
Cluster with 18 points: height = 0.075m
Cluster with 12 points: height = 0.082m
Cluster with 18 points: height = 0.087m
Cluster with 22 points: height = 0.087m
Cluster with 10 points: height = 0.065m
Cluster with 22 points: height = 0.081m
Cluster with 11 points: height = 0.081m
Cluster with 21 points: height = 0.078m
Cluster with 15 points: height = 0.073m
Cluster with 10 points: height = 0.076m
Cluster with 10 points: height = 0.075m
Cluster with 10 points: height = 0.074m
Final estimated crop height: 0.331m
I:app:计算得到作物高度: 0.331 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 6.01s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722141000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722141000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.86it/s]
Reading CSV chunks: 2it [00:00,  2.11it/s]
Reading CSV chunks: 2it [00:00,  2.07it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722141000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=52456.668
Y: min=-2147483.750, max=2147483.750, mean=52285.825
Z: min=-2147483.750, max=2147483.750, mean=52445.273
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3640 statistical outliers (z-score > 3.0)
Subsampled from 196360 to 106398 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 106398 points
Estimating sensor pose...
PCA eigenvalues: [3.14015602e+11 2.80267422e+11 2.65874657e+11]
Estimated ground plane normal: [0.43863155 0.88450909 0.15889   ]
Estimated rotation matrix:
[[ 0.83398111 -0.33478034 -0.43863155]
 [-0.33478034  0.32490889 -0.88450909]
 [ 0.43863155  0.88450909  0.15889   ]]
Estimated translation vector: [      0.               0.         2293582.19056634]
Fitting ground plane and transforming point cloud...
Identified 2 ground points out of 106398 total points
Ground plane equation: -0.1218x + -0.0258y + 0.9922z + -0.0000 = 0
Refined ground plane normal: [-0.12177048 -0.02575848  0.99222399]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 19359 个异常高度点 (高于 0.496m)
显示 23 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722141000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722141000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722141000.gif，用时: 72.54s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722141000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722141000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 80.72s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 722|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:42 2025] POST /v1/detect_pointcloud => generated 220 bytes in 80768 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 3/3] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:35:03 2025] POST /v1/state => generated 70 bytes in 9 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250722/WX001_01_20250722130000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250722/WX001_01_20250722130000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722130000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250722130000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250722130000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 722) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 804)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250722/WX001_01_20250722130000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250722/WX001_01_20250722130000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722130000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250722130000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250722130000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 804) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 815)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 815|app: 0|req: 6/4] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:19 2025] POST /v1/shutdown => generated 22 bytes in 53 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559fb05350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x559fb05350 pid: 536 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 536)
spawned uWSGI worker 1 (pid: 725, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 725|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:39 2025] POST /v1/state => generated 70 bytes in 40 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722142000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722142000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722142000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722142000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722142000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722142000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.50it/s]
Reading CSV chunks: 2it [00:01,  1.97it/s]
Reading CSV chunks: 2it [00:01,  1.88it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722142000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=56518.762
Y: min=-2147483.750, max=2147483.750, mean=56826.414
Z: min=-2147483.750, max=2147483.750, mean=55887.164
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 64582 个点
Preprocessing point cloud with 64582 points...
Removed 0 points with NaN values
Removed 467 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 64115 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [5.31344841e+07 1.28716159e-02 3.74480721e-03]
Estimated ground plane normal: [2.14689245e-02 9.99769516e-01 2.53697545e-06]
Estimated rotation matrix:
[[ 9.99539086e-01 -2.14639218e-02 -2.14689245e-02]
 [-2.14639218e-02  4.63450624e-04 -9.99769516e-01]
 [ 2.14689245e-02  9.99769516e-01  2.53707545e-06]]
Estimated translation vector: [0.        0.        0.5959109]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 59139 ground points out of 64115 total points
Ground plane equation: -0.0598x + 0.0013y + 0.9982z + -0.0026 = 0
Refined ground plane normal: [-0.05981424  0.00128506  0.9982087 ]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6457 points above ground threshold of 0.05m
DBSCAN found 31 clusters
Cluster with 4666 points: height = 0.331m
Cluster with 18 points: height = 0.076m
Cluster with 14 points: height = 0.078m
Cluster with 23 points: height = 0.070m
Cluster with 17 points: height = 0.068m
Cluster with 10 points: height = 0.077m
Cluster with 18 points: height = 0.082m
Cluster with 20 points: height = 0.078m
Cluster with 18 points: height = 0.076m
Cluster with 18 points: height = 0.080m
Cluster with 12 points: height = 0.076m
Cluster with 11 points: height = 0.087m
Cluster with 22 points: height = 0.078m
Cluster with 21 points: height = 0.073m
Cluster with 16 points: height = 0.078m
Cluster with 15 points: height = 0.080m
Cluster with 12 points: height = 0.071m
Cluster with 23 points: height = 0.082m
Cluster with 21 points: height = 0.081m
Cluster with 15 points: height = 0.083m
Cluster with 17 points: height = 0.082m
Cluster with 10 points: height = 0.077m
Cluster with 15 points: height = 0.079m
Cluster with 21 points: height = 0.073m
Cluster with 12 points: height = 0.073m
Cluster with 23 points: height = 0.077m
Cluster with 18 points: height = 0.072m
Cluster with 10 points: height = 0.074m
Cluster with 10 points: height = 0.085m
Cluster with 10 points: height = 0.085m
Cluster with 10 points: height = 0.073m
Final estimated crop height: 0.331m
I:app:计算得到作物高度: 0.331 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 5.85s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722142000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722142000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.07it/s]
Reading CSV chunks: 2it [00:00,  2.57it/s]
Reading CSV chunks: 2it [00:00,  2.48it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722142000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=56518.762
Y: min=-2147483.750, max=2147483.750, mean=56826.414
Z: min=-2147483.750, max=2147483.750, mean=55887.164
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3680 statistical outliers (z-score > 3.0)
Subsampled from 196320 to 107566 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 107566 points
Estimating sensor pose...
PCA eigenvalues: [3.10801999e+11 2.75437570e+11 2.62750693e+11]
Estimated ground plane normal: [0.3184653  0.94083874 0.11576837]
Estimated rotation matrix:
[[ 0.90910287 -0.26853646 -0.3184653 ]
 [-0.26853646  0.2066655  -0.94083874]
 [ 0.3184653   0.94083874  0.11576837]]
Estimated translation vector: [      0.               0.         2281469.09204376]
Fitting ground plane and transforming point cloud...
Identified 2 ground points out of 107566 total points
Ground plane equation: -0.2685x + 0.2067y + 0.9408z + -0.0000 = 0
Refined ground plane normal: [-0.26853646  0.2066655   0.94083874]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 15219 个异常高度点 (高于 0.496m)
显示 103 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722142000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722142000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722142000.gif，用时: 75.81s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722142000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722142000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 83.84s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 725|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:39 2025] POST /v1/detect_pointcloud => generated 220 bytes in 83886 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 725|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:03 2025] POST /v1/shutdown => generated 22 bytes in 20 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55b5a7f350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55b5a7f350 pid: 527 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 527)
spawned uWSGI worker 1 (pid: 721, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 721|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722143000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722143000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722143000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722143000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722143000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722143000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.26it/s]
Reading CSV chunks: 2it [00:01,  1.61it/s]
Reading CSV chunks: 2it [00:01,  1.55it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722143000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=57817.594
Y: min=-2147483.750, max=2147483.750, mean=56603.582
Z: min=-2147483.750, max=2147483.750, mean=57461.917
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 64185 个点
Preprocessing point cloud with 64185 points...
Removed 0 points with NaN values
Removed 500 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 63685 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [7.04406615e+07 1.28312259e-02 3.82465343e-03]
Estimated ground plane normal: [1.93858832e-02 9.99812076e-01 1.91018326e-06]
Estimated rotation matrix:
[[ 9.99624188e-01 -1.93822031e-02 -1.93858832e-02]
 [-1.93822031e-02  3.77722031e-04 -9.99812076e-01]
 [ 1.93858832e-02  9.99812076e-01  1.91028326e-06]]
Estimated translation vector: [0.         0.         0.41664006]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 58836 ground points out of 63685 total points
Ground plane equation: -0.0211x + 0.0004y + 0.9998z + -0.0028 = 0
Refined ground plane normal: [-2.11301847e-02  4.09764154e-04  9.99776649e-01]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6681 points above ground threshold of 0.05m
DBSCAN found 35 clusters
Cluster with 4903 points: height = 0.330m
Cluster with 26 points: height = 0.078m
Cluster with 22 points: height = 0.076m
Cluster with 12 points: height = 0.074m
Cluster with 14 points: height = 0.079m
Cluster with 18 points: height = 0.077m
Cluster with 15 points: height = 0.074m
Cluster with 22 points: height = 0.077m
Cluster with 12 points: height = 0.069m
Cluster with 13 points: height = 0.068m
Cluster with 13 points: height = 0.078m
Cluster with 22 points: height = 0.077m
Cluster with 23 points: height = 0.075m
Cluster with 11 points: height = 0.068m
Cluster with 15 points: height = 0.077m
Cluster with 13 points: height = 0.073m
Cluster with 17 points: height = 0.073m
Cluster with 21 points: height = 0.073m
Cluster with 13 points: height = 0.074m
Cluster with 12 points: height = 0.075m
Cluster with 12 points: height = 0.078m
Cluster with 23 points: height = 0.076m
Cluster with 10 points: height = 0.080m
Cluster with 12 points: height = 0.074m
Cluster with 13 points: height = 0.073m
Cluster with 17 points: height = 0.075m
Cluster with 16 points: height = 0.073m
Cluster with 11 points: height = 0.066m
Cluster with 15 points: height = 0.075m
Cluster with 16 points: height = 0.497m
Cluster with 16 points: height = 0.076m
Cluster with 12 points: height = 0.078m
Cluster with 10 points: height = 0.074m
Cluster with 10 points: height = 0.077m
Cluster with 11 points: height = 0.074m
Final estimated crop height: 0.497m
I:app:计算得到作物高度: 0.497 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 6.25s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722143000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722143000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.65it/s]
Reading CSV chunks: 2it [00:00,  2.88it/s]
Reading CSV chunks: 2it [00:00,  2.84it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722143000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=57817.594
Y: min=-2147483.750, max=2147483.750, mean=56603.582
Z: min=-2147483.750, max=2147483.750, mean=57461.917
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3734 statistical outliers (z-score > 3.0)
Subsampled from 196266 to 107542 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 107542 points
Estimating sensor pose...
PCA eigenvalues: [3.02091872e+11 2.68626911e+11 2.54961753e+11]
Estimated ground plane normal: [0.20524468 0.96481654 0.1643279 ]
Estimated rotation matrix:
[[ 0.96382    -0.17007534 -0.20524468]
 [-0.17007534  0.2005079  -0.96481654]
 [ 0.20524468  0.96481654  0.1643279 ]]
Estimated translation vector: [      0.               0.         2276833.25883707]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 107542 total points
Ground plane equation: 0.0345x + 0.1622y + 0.9862z + -0.0000 = 0
Refined ground plane normal: [0.03450262 0.16222271 0.98615078]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 15838 个异常高度点 (高于 0.745m)
显示 50 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722143000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722143000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722143000.gif，用时: 77.93s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722143000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722143000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 86.38s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 721|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 221 bytes in 86408 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 721|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:06 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55bcf96350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x55bcf96350 pid: 531 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 531)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:39 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722144000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722144000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722144000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722144000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722144000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722144000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.19it/s]
Reading CSV chunks: 2it [00:01,  1.64it/s]
Reading CSV chunks: 2it [00:01,  1.56it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722144000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=62293.303
Y: min=-2147483.750, max=2147483.750, mean=62453.487
Z: min=-2147483.750, max=2147483.750, mean=62919.334
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 58350 个点
Preprocessing point cloud with 58350 points...
Removed 0 points with NaN values
Removed 491 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 57859 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [7.81807173e+07 1.46472676e-02 4.57941295e-03]
Estimated ground plane normal: [4.13781755e-02 9.99143557e-01 1.95818325e-06]
Estimated rotation matrix:
[[ 9.98287850e-01 -4.13426565e-02 -4.13781755e-02]
 [-4.13426565e-02  1.71410834e-03 -9.99143557e-01]
 [ 4.13781755e-02  9.99143557e-01  1.95828325e-06]]
Estimated translation vector: [0.         0.         0.21955439]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 52922 ground points out of 57859 total points
Ground plane equation: -0.0488x + 0.0020y + 0.9988z + -0.0029 = 0
Refined ground plane normal: [-0.04882563  0.00202256  0.99880527]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6402 points above ground threshold of 0.05m
DBSCAN found 24 clusters
Cluster with 4888 points: height = 0.333m
Cluster with 19 points: height = 0.070m
Cluster with 14 points: height = 0.068m
Cluster with 19 points: height = 0.068m
Cluster with 21 points: height = 0.069m
Cluster with 20 points: height = 0.069m
Cluster with 17 points: height = 0.072m
Cluster with 16 points: height = 0.070m
Cluster with 21 points: height = 0.072m
Cluster with 15 points: height = 0.066m
Cluster with 17 points: height = 0.070m
Cluster with 13 points: height = 0.071m
Cluster with 17 points: height = 0.067m
Cluster with 14 points: height = 0.069m
Cluster with 10 points: height = 0.389m
Cluster with 12 points: height = 0.069m
Cluster with 14 points: height = 0.071m
Cluster with 14 points: height = 0.069m
Cluster with 10 points: height = 0.071m
Cluster with 10 points: height = 0.068m
Cluster with 13 points: height = 0.070m
Cluster with 11 points: height = 0.070m
Cluster with 10 points: height = 0.066m
Cluster with 11 points: height = 0.068m
Final estimated crop height: 0.389m
I:app:计算得到作物高度: 0.389 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.51s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722144000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722144000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.56it/s]
Reading CSV chunks: 2it [00:00,  2.85it/s]
Reading CSV chunks: 2it [00:00,  2.80it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722144000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=62293.303
Y: min=-2147483.750, max=2147483.750, mean=62453.487
Z: min=-2147483.750, max=2147483.750, mean=62919.334
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3935 statistical outliers (z-score > 3.0)
Subsampled from 196065 to 110363 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 110363 points
Estimating sensor pose...
PCA eigenvalues: [2.77687665e+11 2.46808529e+11 2.28274511e+11]
Estimated ground plane normal: [0.32980462 0.9241179  0.19296377]
Estimated rotation matrix:
[[ 0.90882281 -0.25547997 -0.32980462]
 [-0.25547997  0.28414096 -0.9241179 ]
 [ 0.32980462  0.9241179   0.19296377]]
Estimated translation vector: [      0.               0.         2191823.93372252]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 110363 total points
Ground plane equation: 0.5752x + -0.7166y + 0.3945z + -0.0001 = 0
Refined ground plane normal: [ 0.57521802 -0.71657197  0.39452355]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 19154 个异常高度点 (高于 0.583m)
显示 47 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722144000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722144000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722144000.gif，用时: 72.38s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722144000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722144000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 78.07s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 722|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:39 2025] POST /v1/detect_pointcloud => generated 221 bytes in 78105 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 722|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:39:57 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5567b8f350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x5567b8f350 pid: 529 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 529)
spawned uWSGI worker 1 (pid: 724, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 724|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:39 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722145000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722145000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722145000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.11it/s]
Reading CSV chunks: 2it [00:01,  1.43it/s]
Reading CSV chunks: 2it [00:01,  1.37it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=61717.319
Y: min=-2147483.750, max=2147483.750, mean=60814.981
Z: min=-2147483.750, max=2147483.750, mean=61111.701
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 61262 个点
Preprocessing point cloud with 61262 points...
Removed 0 points with NaN values
Removed 446 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 60816 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [5.83921290e+07 1.39619596e-02 4.24405084e-03]
Estimated ground plane normal: [5.05036006e-02 9.98723879e-01 2.34222329e-06]
Estimated rotation matrix:
[[ 9.97449392e-01 -5.04390338e-02 -5.05036006e-02]
 [-5.04390338e-02  2.55295003e-03 -9.98723879e-01]
 [ 5.05036006e-02  9.98723879e-01  2.34232329e-06]]
Estimated translation vector: [0.         0.         0.39297663]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 56045 ground points out of 60816 total points
Ground plane equation: -0.0648x + 0.0033y + 0.9979z + -0.0027 = 0
Refined ground plane normal: [-0.06478747  0.00327712  0.9978937 ]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6170 points above ground threshold of 0.05m
DBSCAN found 17 clusters
Cluster with 4763 points: height = 0.333m
Cluster with 17 points: height = 0.072m
Cluster with 25 points: height = 0.068m
Cluster with 18 points: height = 0.068m
Cluster with 19 points: height = 0.070m
Cluster with 18 points: height = 0.068m
Cluster with 10 points: height = 0.065m
Cluster with 20 points: height = 0.069m
Cluster with 11 points: height = 0.069m
Cluster with 13 points: height = 0.068m
Cluster with 16 points: height = 0.068m
Cluster with 16 points: height = 0.067m
Cluster with 18 points: height = 0.068m
Cluster with 10 points: height = 0.066m
Cluster with 12 points: height = 0.069m
Cluster with 10 points: height = 0.061m
Cluster with 15 points: height = 0.070m
Final estimated crop height: 0.333m
I:app:计算得到作物高度: 0.333 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.95s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722145000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.83it/s]
Reading CSV chunks: 2it [00:01,  1.89it/s]
Reading CSV chunks: 2it [00:01,  1.88it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=61717.319
Y: min=-2147483.750, max=2147483.750, mean=60814.981
Z: min=-2147483.750, max=2147483.750, mean=61111.701
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 4209 statistical outliers (z-score > 3.0)
Subsampled from 195791 to 107692 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 107692 points
Estimating sensor pose...
PCA eigenvalues: [2.64553796e+11 2.39410468e+11 2.19268048e+11]
Estimated ground plane normal: [0.21887495 0.96059501 0.1713213 ]
Estimated rotation matrix:
[[ 0.95910068 -0.1794983  -0.21887495]
 [-0.1794983   0.21222062 -0.96059501]
 [ 0.21887495  0.96059501  0.1713213 ]]
Estimated translation vector: [      0.               0.         2167377.87399225]
Fitting ground plane and transforming point cloud...
Identified 5 ground points out of 107692 total points
Ground plane equation: -0.2383x + 0.1373y + 0.9614z + -84091.4074 = 0
Refined ground plane normal: [-0.23825179  0.13732703  0.96144546]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 6176 个异常高度点 (高于 0.499m)
显示 1 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722145000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722145000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722145000.gif，用时: 86.69s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722145000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722145000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 92.88s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 724|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:39 2025] POST /v1/detect_pointcloud => generated 220 bytes in 92910 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722145000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722145000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722145000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.50it/s]
Reading CSV chunks: 2it [00:00,  2.14it/s]
Reading CSV chunks: 2it [00:00,  2.01it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=61717.319
Y: min=-2147483.750, max=2147483.750, mean=60814.981
Z: min=-2147483.750, max=2147483.750, mean=61111.701
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 61262 个点
Preprocessing point cloud with 61262 points...
Removed 0 points with NaN values
Removed 446 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 60816 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [5.83921290e+07 1.39619596e-02 4.24405084e-03]
Estimated ground plane normal: [5.05036006e-02 9.98723879e-01 2.34222329e-06]
Estimated rotation matrix:
[[ 9.97449392e-01 -5.04390338e-02 -5.05036006e-02]
 [-5.04390338e-02  2.55295003e-03 -9.98723879e-01]
 [ 5.05036006e-02  9.98723879e-01  2.34232329e-06]]
Estimated translation vector: [0.         0.         0.39297663]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 56045 ground points out of 60816 total points
Ground plane equation: -0.0648x + 0.0033y + 0.9979z + -0.0027 = 0
Refined ground plane normal: [-0.06478747  0.00327712  0.9978937 ]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6170 points above ground threshold of 0.05m
DBSCAN found 17 clusters
Cluster with 4763 points: height = 0.333m
Cluster with 17 points: height = 0.072m
Cluster with 25 points: height = 0.068m
Cluster with 18 points: height = 0.068m
Cluster with 19 points: height = 0.070m
Cluster with 18 points: height = 0.068m
Cluster with 10 points: height = 0.065m
Cluster with 20 points: height = 0.069m
Cluster with 11 points: height = 0.069m
Cluster with 13 points: height = 0.068m
Cluster with 16 points: height = 0.068m
Cluster with 16 points: height = 0.067m
Cluster with 18 points: height = 0.068m
Cluster with 10 points: height = 0.066m
Cluster with 12 points: height = 0.069m
Cluster with 10 points: height = 0.061m
Cluster with 15 points: height = 0.070m
Final estimated crop height: 0.333m
I:app:计算得到作物高度: 0.333 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.04s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722145000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.59it/s]
Reading CSV chunks: 2it [00:00,  2.79it/s]
Reading CSV chunks: 2it [00:00,  2.75it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722145000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=61717.319
Y: min=-2147483.750, max=2147483.750, mean=60814.981
Z: min=-2147483.750, max=2147483.750, mean=61111.701
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 4209 statistical outliers (z-score > 3.0)
Subsampled from 195791 to 107692 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 107692 points
Estimating sensor pose...
PCA eigenvalues: [2.64553796e+11 2.39410468e+11 2.19268048e+11]
Estimated ground plane normal: [0.21887495 0.96059501 0.1713213 ]
Estimated rotation matrix:
[[ 0.95910068 -0.1794983  -0.21887495]
 [-0.1794983   0.21222062 -0.96059501]
 [ 0.21887495  0.96059501  0.1713213 ]]
Estimated translation vector: [      0.               0.         2167377.87399225]
Fitting ground plane and transforming point cloud...
Identified 5 ground points out of 107692 total points
Ground plane equation: -0.2383x + 0.1373y + 0.9614z + -84091.4074 = 0
Refined ground plane normal: [-0.23825179  0.13732703  0.96144546]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 6176 个异常高度点 (高于 0.499m)
显示 1 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722145000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722145000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722145000.gif，用时: 73.38s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722145000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722145000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 78.56s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 724|app: 0|req: 3/3] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:35:12 2025] POST /v1/detect_pointcloud => generated 220 bytes in 78584 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 724|app: 0|req: 4/4] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:41:30 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55cccb2350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55cccb2350 pid: 532 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 532)
spawned uWSGI worker 1 (pid: 726, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 726|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:44 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722150000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722150000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722150000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722150000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722150000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722150000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.40it/s]
Reading CSV chunks: 2it [00:01,  1.88it/s]
Reading CSV chunks: 2it [00:01,  1.78it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722150000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63073.855
Y: min=-2147483.750, max=2147483.750, mean=61718.535
Z: min=-2147483.750, max=2147483.750, mean=61197.245
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 60772 个点
Preprocessing point cloud with 60772 points...
Removed 0 points with NaN values
Removed 416 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 60356 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [6.67433298e+07 1.39748623e-02 4.22194667e-03]
Estimated ground plane normal: [3.22160844e-02 9.99480927e-01 2.10683324e-06]
Estimated rotation matrix:
[[ 9.98962126e-01 -3.21992941e-02 -3.22160844e-02]
 [-3.21992941e-02  1.03998084e-03 -9.99480927e-01]
 [ 3.22160844e-02  9.99480927e-01  2.10693324e-06]]
Estimated translation vector: [0.         0.         0.25604063]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 55570 ground points out of 60356 total points
Ground plane equation: -0.0514x + 0.0017y + 0.9987z + -0.0028 = 0
Refined ground plane normal: [-0.05140899  0.00165759  0.99867631]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6249 points above ground threshold of 0.05m
DBSCAN found 28 clusters
Cluster with 4706 points: height = 0.332m
Cluster with 14 points: height = 0.078m
Cluster with 18 points: height = 0.072m
Cluster with 10 points: height = 0.422m
Cluster with 11 points: height = 0.071m
Cluster with 15 points: height = 0.067m
Cluster with 16 points: height = 0.071m
Cluster with 11 points: height = 0.069m
Cluster with 14 points: height = 0.072m
Cluster with 11 points: height = 0.067m
Cluster with 10 points: height = 0.077m
Cluster with 15 points: height = 0.076m
Cluster with 13 points: height = 0.067m
Cluster with 13 points: height = 0.070m
Cluster with 13 points: height = 0.073m
Cluster with 14 points: height = 0.073m
Cluster with 12 points: height = 0.071m
Cluster with 15 points: height = 0.068m
Cluster with 11 points: height = 0.077m
Cluster with 14 points: height = 0.075m
Cluster with 18 points: height = 0.071m
Cluster with 12 points: height = 0.067m
Cluster with 13 points: height = 0.070m
Cluster with 11 points: height = 0.074m
Cluster with 10 points: height = 0.071m
Cluster with 11 points: height = 0.068m
Cluster with 10 points: height = 0.070m
Cluster with 10 points: height = 0.070m
Final estimated crop height: 0.422m
I:app:计算得到作物高度: 0.422 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 5.91s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722150000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722150000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.86it/s]
Reading CSV chunks: 2it [00:00,  2.23it/s]
Reading CSV chunks: 2it [00:00,  2.16it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722150000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63073.855
Y: min=-2147483.750, max=2147483.750, mean=61718.535
Z: min=-2147483.750, max=2147483.750, mean=61197.245
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3969 statistical outliers (z-score > 3.0)
Subsampled from 196031 to 108984 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 108984 points
Estimating sensor pose...
PCA eigenvalues: [2.76315587e+11 2.45513062e+11 2.29816522e+11]
Estimated ground plane normal: [0.22863139 0.96192412 0.1497654 ]
Estimated rotation matrix:
[[ 0.95453654 -0.19127906 -0.22863139]
 [-0.19127906  0.19522886 -0.96192412]
 [ 0.22863139  0.96192412  0.1497654 ]]
Estimated translation vector: [      0.               0.         2176687.21439261]
Fitting ground plane and transforming point cloud...
Identified 2 ground points out of 108984 total points
Ground plane equation: -0.3082x + 0.2174y + 0.9262z + -0.0000 = 0
Refined ground plane normal: [-0.30816323  0.21743162  0.92615275]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 13468 个异常高度点 (高于 0.633m)
显示 444 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722150000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722150000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722150000.gif，用时: 78.87s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722150000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722150000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 86.97s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 726|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:44 2025] POST /v1/detect_pointcloud => generated 219 bytes in 86998 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 726|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:10 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x558a21d350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x558a21d350 pid: 529 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 529)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:38 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722151000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722151000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722151000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722151000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722151000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722151000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.37it/s]
Reading CSV chunks: 2it [00:01,  1.92it/s]
Reading CSV chunks: 2it [00:01,  1.81it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722151000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=58955.189
Y: min=-2147483.750, max=2147483.750, mean=58853.281
Z: min=-2147483.750, max=2147483.750, mean=59010.327
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 62471 个点
Preprocessing point cloud with 62471 points...
Removed 0 points with NaN values
Removed 461 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 62010 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [6.11360310e+07 1.34787561e-02 3.89913917e-03]
Estimated ground plane normal: [3.22814705e-02 9.99478818e-01 2.27943113e-06]
Estimated rotation matrix:
[[ 9.98957909e-01 -3.22645724e-02 -3.22814705e-02]
 [-3.22645724e-02  1.04437049e-03 -9.99478818e-01]
 [ 3.22814705e-02  9.99478818e-01  2.27953113e-06]]
Estimated translation vector: [0.         0.         0.25101055]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 57360 ground points out of 62010 total points
Ground plane equation: -0.0463x + 0.0015y + 0.9989z + -0.0028 = 0
Refined ground plane normal: [-0.04633648  0.00149711  0.99892477]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6495 points above ground threshold of 0.05m
DBSCAN found 28 clusters
Cluster with 13 points: height = 0.067m
Cluster with 4767 points: height = 0.330m
Cluster with 24 points: height = 0.078m
Cluster with 20 points: height = 0.075m
Cluster with 20 points: height = 0.076m
Cluster with 15 points: height = 0.071m
Cluster with 11 points: height = 0.076m
Cluster with 25 points: height = 0.076m
Cluster with 12 points: height = 0.075m
Cluster with 14 points: height = 0.073m
Cluster with 22 points: height = 0.079m
Cluster with 24 points: height = 0.076m
Cluster with 18 points: height = 0.075m
Cluster with 15 points: height = 0.069m
Cluster with 12 points: height = 0.076m
Cluster with 16 points: height = 0.076m
Cluster with 11 points: height = 0.076m
Cluster with 18 points: height = 0.075m
Cluster with 12 points: height = 0.073m
Cluster with 14 points: height = 0.077m
Cluster with 17 points: height = 0.075m
Cluster with 21 points: height = 0.073m
Cluster with 24 points: height = 0.072m
Cluster with 22 points: height = 0.072m
Cluster with 14 points: height = 0.075m
Cluster with 11 points: height = 0.070m
Cluster with 14 points: height = 0.073m
Cluster with 12 points: height = 0.069m
Final estimated crop height: 0.330m
I:app:计算得到作物高度: 0.330 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.30s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722151000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722151000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.71it/s]
Reading CSV chunks: 2it [00:00,  2.17it/s]
Reading CSV chunks: 2it [00:00,  2.08it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722151000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=58955.189
Y: min=-2147483.750, max=2147483.750, mean=58853.281
Z: min=-2147483.750, max=2147483.750, mean=59010.327
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3919 statistical outliers (z-score > 3.0)
Subsampled from 196081 to 107743 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 107743 points
Estimating sensor pose...
PCA eigenvalues: [2.83185033e+11 2.50862270e+11 2.36290678e+11]
Estimated ground plane normal: [0.24948656 0.95588441 0.15505306]
Estimated rotation matrix:
[[ 0.94611196 -0.20646697 -0.24948656]
 [-0.20646697  0.20894109 -0.95588441]
 [ 0.24948656  0.95588441  0.15505306]]
Estimated translation vector: [      0.               0.         2184792.59805837]
Fitting ground plane and transforming point cloud...
Identified 2 ground points out of 107743 total points
Ground plane equation: -0.0478x + 0.1732y + 0.9837z + -0.0000 = 0
Refined ground plane normal: [-0.04783449  0.17324097  0.98371715]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 15823 个异常高度点 (高于 0.495m)
显示 25 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722151000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722151000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722151000.gif，用时: 73.56s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722151000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722151000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 79.06s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 722|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:38 2025] POST /v1/detect_pointcloud => generated 220 bytes in 79105 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 3/3] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:35:40 2025] POST /v1/state => generated 70 bytes in 9 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250722/WX001_01_20250722150000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250722/WX001_01_20250722150000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722150000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250722150000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250722150000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 722) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 808)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250722/WX001_01_20250722150000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250722/WX001_01_20250722150000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722150000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250722150000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250722150000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 808) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 819)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 819|app: 0|req: 6/4] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:56 2025] POST /v1/shutdown => generated 22 bytes in 56 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55968f2350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55968f2350 pid: 531 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 531)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:39 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722152000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722152000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722152000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722152000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722152000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722152000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.44it/s]
Reading CSV chunks: 2it [00:01,  1.95it/s]
Reading CSV chunks: 2it [00:01,  1.85it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722152000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=61314.424
Y: min=-2147483.750, max=2147483.750, mean=60826.714
Z: min=-2147483.750, max=2147483.750, mean=60948.148
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 60470 个点
Preprocessing point cloud with 60470 points...
Removed 0 points with NaN values
Removed 440 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 60030 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [5.52012341e+07 1.38033697e-02 4.00434156e-03]
Estimated ground plane normal: [3.50548441e-02 9.99385390e-01 2.69874649e-06]
Estimated rotation matrix:
[[ 9.98771161e-01 -3.50332045e-02 -3.50548441e-02]
 [-3.50332045e-02  1.23153762e-03 -9.99385390e-01]
 [ 3.50548441e-02  9.99385390e-01  2.69884649e-06]]
Estimated translation vector: [0.         0.         0.45279129]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 55223 ground points out of 60030 total points
Ground plane equation: -0.0412x + 0.0014y + 0.9992z + -0.0031 = 0
Refined ground plane normal: [-0.04117337  0.00144519  0.99915097]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6381 points above ground threshold of 0.05m
DBSCAN found 19 clusters
Cluster with 4837 points: height = 0.331m
Cluster with 16 points: height = 0.076m
Cluster with 11 points: height = 0.074m
Cluster with 17 points: height = 0.067m
Cluster with 14 points: height = 0.075m
Cluster with 10 points: height = 0.072m
Cluster with 10 points: height = 0.078m
Cluster with 28 points: height = 0.077m
Cluster with 12 points: height = 0.075m
Cluster with 17 points: height = 0.078m
Cluster with 18 points: height = 0.075m
Cluster with 15 points: height = 0.072m
Cluster with 19 points: height = 0.071m
Cluster with 19 points: height = 0.075m
Cluster with 10 points: height = 0.074m
Cluster with 14 points: height = 0.072m
Cluster with 10 points: height = 0.074m
Cluster with 10 points: height = 0.078m
Cluster with 12 points: height = 0.075m
Final estimated crop height: 0.331m
I:app:计算得到作物高度: 0.331 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.64s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722152000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722152000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.86it/s]
Reading CSV chunks: 2it [00:01,  2.00it/s]
Reading CSV chunks: 2it [00:01,  1.98it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722152000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=61314.424
Y: min=-2147483.750, max=2147483.750, mean=60826.714
Z: min=-2147483.750, max=2147483.750, mean=60948.148
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3747 statistical outliers (z-score > 3.0)
Subsampled from 196253 to 109721 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 109721 points
Estimating sensor pose...
PCA eigenvalues: [2.92641030e+11 2.57316204e+11 2.41397012e+11]
Estimated ground plane normal: [0.36024808 0.91557702 0.17871778]
Estimated rotation matrix:
[[ 0.88989843 -0.27982514 -0.36024808]
 [-0.27982514  0.28881935 -0.91557702]
 [ 0.36024808  0.91557702  0.17871778]]
Estimated translation vector: [      0.               0.         2221581.24428026]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 109721 total points
Ground plane equation: 0.0654x + 0.1664y + 0.9839z + -0.0000 = 0
Refined ground plane normal: [0.06543664 0.16635241 0.98389274]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 16706 个异常高度点 (高于 0.496m)
显示 28 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722152000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722152000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722152000.gif，用时: 73.78s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722152000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722152000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 79.60s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 722|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 221 bytes in 79634 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 722|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:39:58 2025] POST /v1/shutdown => generated 22 bytes in 18 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5596741350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x5596741350 pid: 526 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 526)
spawned uWSGI worker 1 (pid: 724, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 724|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722153000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722153000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722153000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722153000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722153000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722153000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.52it/s]
Reading CSV chunks: 2it [00:01,  2.10it/s]
Reading CSV chunks: 2it [00:01,  1.98it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722153000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=59810.923
Y: min=-2147483.750, max=2147483.750, mean=58686.139
Z: min=-2147483.750, max=2147483.750, mean=58028.829
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 62735 个点
Preprocessing point cloud with 62735 points...
Removed 0 points with NaN values
Removed 439 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 62296 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [5.55702337e+07 1.34151738e-02 3.88338097e-03]
Estimated ground plane normal: [2.83688204e-02 9.99597524e-01 2.43823235e-06]
Estimated rotation matrix:
[[ 9.99195212e-01 -2.83573335e-02 -2.83688204e-02]
 [-2.83573335e-02  8.07226339e-04 -9.99597524e-01]
 [ 2.83688204e-02  9.99597524e-01  2.43833235e-06]]
Estimated translation vector: [0.         0.         0.24490983]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 57683 ground points out of 62296 total points
Ground plane equation: -0.0336x + 0.0010y + 0.9994z + -0.0025 = 0
Refined ground plane normal: [-3.36433539e-02  9.55580621e-04  9.99433445e-01]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6333 points above ground threshold of 0.05m
DBSCAN found 22 clusters
Cluster with 4798 points: height = 0.330m
Cluster with 11 points: height = 0.074m
Cluster with 12 points: height = 0.071m
Cluster with 15 points: height = 0.073m
Cluster with 21 points: height = 0.072m
Cluster with 17 points: height = 0.072m
Cluster with 15 points: height = 0.074m
Cluster with 18 points: height = 0.076m
Cluster with 16 points: height = 0.074m
Cluster with 21 points: height = 0.070m
Cluster with 13 points: height = 0.074m
Cluster with 12 points: height = 0.073m
Cluster with 20 points: height = 0.071m
Cluster with 16 points: height = 0.075m
Cluster with 16 points: height = 0.072m
Cluster with 13 points: height = 0.072m
Cluster with 14 points: height = 0.070m
Cluster with 15 points: height = 0.074m
Cluster with 12 points: height = 0.070m
Cluster with 18 points: height = 0.073m
Cluster with 11 points: height = 0.072m
Cluster with 13 points: height = 0.072m
Final estimated crop height: 0.330m
I:app:计算得到作物高度: 0.330 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.42s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722153000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722153000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.21it/s]
Reading CSV chunks: 2it [00:00,  2.36it/s]
Reading CSV chunks: 2it [00:00,  2.33it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722153000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=59810.923
Y: min=-2147483.750, max=2147483.750, mean=58686.139
Z: min=-2147483.750, max=2147483.750, mean=58028.829
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3887 statistical outliers (z-score > 3.0)
Subsampled from 196113 to 107358 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 107358 points
Estimating sensor pose...
PCA eigenvalues: [2.79071023e+11 2.50227368e+11 2.32508289e+11]
Estimated ground plane normal: [0.37024506 0.90882986 0.1922157 ]
Estimated rotation matrix:
[[ 0.88501963 -0.282239   -0.37024506]
 [-0.282239    0.30719607 -0.90882986]
 [ 0.37024506  0.90882986  0.1922157 ]]
Estimated translation vector: [      0.               0.         2182818.37428101]
Fitting ground plane and transforming point cloud...
E:app:群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I:app:点云识别完成，用时: 10.00s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 724|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 221 bytes in 10016 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 724|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:38:50 2025] POST /v1/shutdown => generated 22 bytes in 16 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5586cbd350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x5586cbd350 pid: 532 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 532)
spawned uWSGI worker 1 (pid: 724, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 724|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722154000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722154000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722154000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722154000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722154000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722154000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.54it/s]
Reading CSV chunks: 2it [00:01,  2.06it/s]
Reading CSV chunks: 2it [00:01,  1.96it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722154000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63168.713
Y: min=-2147483.750, max=2147483.750, mean=64662.607
Z: min=-2147483.750, max=2147483.750, mean=63505.881
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 61277 个点
Preprocessing point cloud with 61277 points...
Removed 0 points with NaN values
Removed 485 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 60792 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [7.07922626e+07 1.34271327e-02 4.03154147e-03]
Estimated ground plane normal: [1.04978399e-02 9.99944896e-01 2.00157164e-06]
Estimated rotation matrix:
[[ 9.99889796e-01 -1.04972405e-02 -1.04978399e-02]
 [-1.04972405e-02  1.12206094e-04 -9.99944896e-01]
 [ 1.04978399e-02  9.99944896e-01  2.00167164e-06]]
Estimated translation vector: [0.        0.        0.4701547]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 55814 ground points out of 60792 total points
Ground plane equation: -0.0484x + 0.0005y + 0.9988z + -0.0030 = 0
Refined ground plane normal: [-4.84169310e-02  5.08564901e-04  9.98827083e-01]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6366 points above ground threshold of 0.05m
DBSCAN found 26 clusters
Cluster with 4750 points: height = 0.331m
Cluster with 12 points: height = 0.072m
Cluster with 14 points: height = 0.074m
Cluster with 18 points: height = 0.075m
Cluster with 18 points: height = 0.074m
Cluster with 15 points: height = 0.073m
Cluster with 18 points: height = 0.068m
Cluster with 13 points: height = 0.078m
Cluster with 19 points: height = 0.074m
Cluster with 18 points: height = 0.069m
Cluster with 19 points: height = 0.069m
Cluster with 13 points: height = 0.074m
Cluster with 10 points: height = 0.078m
Cluster with 19 points: height = 0.075m
Cluster with 13 points: height = 0.067m
Cluster with 10 points: height = 0.072m
Cluster with 15 points: height = 0.076m
Cluster with 14 points: height = 0.076m
Cluster with 10 points: height = 0.080m
Cluster with 10 points: height = 0.076m
Cluster with 14 points: height = 0.082m
Cluster with 10 points: height = 0.080m
Cluster with 14 points: height = 0.076m
Cluster with 12 points: height = 0.072m
Cluster with 10 points: height = 0.466m
Cluster with 12 points: height = 0.071m
Final estimated crop height: 0.466m
I:app:计算得到作物高度: 0.466 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 6.13s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722154000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722154000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.46it/s]
Reading CSV chunks: 2it [00:00,  2.65it/s]
Reading CSV chunks: 2it [00:00,  2.61it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722154000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63168.713
Y: min=-2147483.750, max=2147483.750, mean=64662.607
Z: min=-2147483.750, max=2147483.750, mean=63505.881
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3724 statistical outliers (z-score > 3.0)
Subsampled from 196276 to 109444 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 109444 points
Estimating sensor pose...
PCA eigenvalues: [2.99254442e+11 2.63081663e+11 2.52717285e+11]
Estimated ground plane normal: [0.20242824 0.97206997 0.11875515]
Estimated rotation matrix:
[[ 0.96337251 -0.17588693 -0.20242824]
 [-0.17588693  0.15538264 -0.97206997]
 [ 0.20242824  0.97206997  0.11875515]]
Estimated translation vector: [      0.               0.         2239688.88582699]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 109444 total points
Ground plane equation: -0.0016x + 0.0389y + 0.9992z + -0.0000 = 0
Refined ground plane normal: [-0.00157441  0.03889486  0.99924207]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 19014 个异常高度点 (高于 0.699m)
显示 43 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722154000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722154000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722154000.gif，用时: 73.74s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722154000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722154000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 82.07s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 724|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 220 bytes in 82098 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 724|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:02 2025] POST /v1/shutdown => generated 22 bytes in 20 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x558bd0f350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x558bd0f350 pid: 526 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 526)
spawned uWSGI worker 1 (pid: 725, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 725|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722155000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722155000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722155000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722155000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722155000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722155000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.48it/s]
Reading CSV chunks: 2it [00:01,  2.00it/s]
Reading CSV chunks: 2it [00:01,  1.90it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722155000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63017.167
Y: min=-2147483.750, max=2147483.750, mean=62716.974
Z: min=-2147483.750, max=2147483.750, mean=62858.090
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 60092 个点
Preprocessing point cloud with 60092 points...
Removed 0 points with NaN values
Removed 391 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 59701 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [7.63217359e+07 1.42053190e-02 4.23037559e-03]
Estimated ground plane normal: [2.98084312e-02 9.99555630e-01 1.88157201e-06]
Estimated rotation matrix:
[[ 9.99111459e-01 -2.97951292e-02 -2.98084312e-02]
 [-2.97951292e-02  8.90422571e-04 -9.99555630e-01]
 [ 2.98084312e-02  9.99555630e-01  1.88167201e-06]]
Estimated translation vector: [0.         0.         0.47353826]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 54821 ground points out of 59701 total points
Ground plane equation: -0.0447x + 0.0013y + 0.9990z + -0.0029 = 0
Refined ground plane normal: [-0.04467653  0.0013326   0.99900062]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6447 points above ground threshold of 0.05m
DBSCAN found 25 clusters
Cluster with 4797 points: height = 0.332m
Cluster with 21 points: height = 0.071m
Cluster with 13 points: height = 0.068m
Cluster with 13 points: height = 0.074m
Cluster with 15 points: height = 0.432m
Cluster with 16 points: height = 0.075m
Cluster with 16 points: height = 0.073m
Cluster with 13 points: height = 0.071m
Cluster with 16 points: height = 0.070m
Cluster with 18 points: height = 0.075m
Cluster with 18 points: height = 0.071m
Cluster with 18 points: height = 0.072m
Cluster with 15 points: height = 0.074m
Cluster with 12 points: height = 0.076m
Cluster with 10 points: height = 0.072m
Cluster with 12 points: height = 0.067m
Cluster with 11 points: height = 0.073m
Cluster with 18 points: height = 0.070m
Cluster with 19 points: height = 0.072m
Cluster with 23 points: height = 0.074m
Cluster with 12 points: height = 0.074m
Cluster with 17 points: height = 0.078m
Cluster with 12 points: height = 0.067m
Cluster with 12 points: height = 0.071m
Cluster with 12 points: height = 0.069m
Final estimated crop height: 0.432m
I:app:计算得到作物高度: 0.432 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.25s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722155000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722155000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.23it/s]
Reading CSV chunks: 2it [00:00,  2.68it/s]
Reading CSV chunks: 2it [00:00,  2.60it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722155000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63017.167
Y: min=-2147483.750, max=2147483.750, mean=62716.974
Z: min=-2147483.750, max=2147483.750, mean=62858.090
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3772 statistical outliers (z-score > 3.0)
Subsampled from 196228 to 110262 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 110262 points
Estimating sensor pose...
PCA eigenvalues: [2.84272542e+11 2.51384063e+11 2.36456916e+11]
Estimated ground plane normal: [0.35164395 0.91663882 0.19005214]
Estimated rotation matrix:
[[ 0.89609408 -0.27085409 -0.35164395]
 [-0.27085409  0.29395806 -0.91663882]
 [ 0.35164395  0.91663882  0.19005214]]
Estimated translation vector: [      0.               0.         2212666.79119292]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 110262 total points
Ground plane equation: 0.0689x + 0.1798y + 0.9813z + -0.0000 = 0
Refined ground plane normal: [0.06890015 0.17984744 0.98127859]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 16538 个异常高度点 (高于 0.647m)
显示 19 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722155000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722155000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722155000.gif，用时: 72.66s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722155000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722155000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 78.09s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 725|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 220 bytes in 78125 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 725|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:39:58 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5595739350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x5595739350 pid: 526 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 526)
spawned uWSGI worker 1 (pid: 723, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 723|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:39 2025] POST /v1/state => generated 70 bytes in 39 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722160000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722160000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722160000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722160000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722160000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722160000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.09it/s]
Reading CSV chunks: 2it [00:01,  1.57it/s]
Reading CSV chunks: 2it [00:01,  1.47it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722160000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=62247.154
Y: min=-2147483.750, max=2147483.750, mean=63032.650
Z: min=-2147483.750, max=2147483.750, mean=62652.077
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 61219 个点
Preprocessing point cloud with 61219 points...
Removed 0 points with NaN values
Removed 427 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 60792 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [6.93485980e+07 1.39246725e-02 4.03014312e-03]
Estimated ground plane normal: [3.29049334e-02 9.99458486e-01 2.07814100e-06]
Estimated rotation matrix:
[[ 9.98917268e-01 -3.28870466e-02 -3.29049334e-02]
 [-3.28870466e-02  1.08481063e-03 -9.99458486e-01]
 [ 3.29049334e-02  9.99458486e-01  2.07824100e-06]]
Estimated translation vector: [0.         0.         0.23670659]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 56062 ground points out of 60792 total points
Ground plane equation: -0.0301x + 0.0010y + 0.9995z + -0.0026 = 0
Refined ground plane normal: [-3.00794506e-02  9.90753806e-04  9.99547020e-01]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6223 points above ground threshold of 0.05m
DBSCAN found 22 clusters
Cluster with 4709 points: height = 0.331m
Cluster with 17 points: height = 0.073m
Cluster with 15 points: height = 0.070m
Cluster with 19 points: height = 0.071m
Cluster with 13 points: height = 0.073m
Cluster with 16 points: height = 0.074m
Cluster with 10 points: height = 0.073m
Cluster with 13 points: height = 0.074m
Cluster with 10 points: height = 0.073m
Cluster with 16 points: height = 0.072m
Cluster with 12 points: height = 0.070m
Cluster with 10 points: height = 0.070m
Cluster with 11 points: height = 0.069m
Cluster with 15 points: height = 0.073m
Cluster with 16 points: height = 0.072m
Cluster with 19 points: height = 0.070m
Cluster with 17 points: height = 0.071m
Cluster with 12 points: height = 0.074m
Cluster with 14 points: height = 0.073m
Cluster with 10 points: height = 0.435m
Cluster with 10 points: height = 0.076m
Cluster with 10 points: height = 0.070m
Final estimated crop height: 0.435m
I:app:计算得到作物高度: 0.435 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 6.77s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722160000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722160000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.43it/s]
Reading CSV chunks: 2it [00:00,  2.62it/s]
Reading CSV chunks: 2it [00:00,  2.59it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722160000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=62247.154
Y: min=-2147483.750, max=2147483.750, mean=63032.650
Z: min=-2147483.750, max=2147483.750, mean=62652.077
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 4025 statistical outliers (z-score > 3.0)
Subsampled from 195975 to 108799 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 108799 points
Estimating sensor pose...
PCA eigenvalues: [2.78421016e+11 2.46908515e+11 2.31191347e+11]
Estimated ground plane normal: [0.35097335 0.91318174 0.20716375]
Estimated rotation matrix:
[[ 0.89795727 -0.26550039 -0.35097335]
 [-0.26550039  0.30920648 -0.91318174]
 [ 0.35097335  0.91318174  0.20716375]]
Estimated translation vector: [      0.               0.         2196994.99080391]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 108799 total points
Ground plane equation: 0.0743x + 0.1934y + 0.9783z + -0.0000 = 0
Refined ground plane normal: [0.07430678 0.19336474 0.97830904]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 18388 个异常高度点 (高于 0.653m)
显示 610 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722160000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722160000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722160000.gif，用时: 72.08s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722160000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722160000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 81.04s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 723|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 220 bytes in 81074 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 723|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:00 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55a77e3350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55a77e3350 pid: 528 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 528)
spawned uWSGI worker 1 (pid: 724, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 724|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:38 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722161000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722161000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722161000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722161000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722161000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722161000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.39it/s]
Reading CSV chunks: 2it [00:01,  1.99it/s]
Reading CSV chunks: 2it [00:01,  1.87it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722161000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63636.759
Y: min=-2147483.750, max=2147483.750, mean=62992.977
Z: min=-2147483.750, max=2147483.750, mean=63259.746
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 61417 个点
Preprocessing point cloud with 61417 points...
Removed 0 points with NaN values
Removed 407 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 61010 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [5.45221697e+07 1.34930135e-02 3.95354065e-03]
Estimated ground plane normal: [3.29638519e-02 9.99456545e-01 2.52609870e-06]
Estimated rotation matrix:
[[ 9.98913387e-01 -3.29458543e-02 -3.29638519e-02]
 [-3.29458543e-02  1.08913899e-03 -9.99456545e-01]
 [ 3.29638519e-02  9.99456545e-01  2.52619870e-06]]
Estimated translation vector: [0.         0.         0.46289725]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 56179 ground points out of 61010 total points
Ground plane equation: -0.0491x + 0.0016y + 0.9988z + -0.0029 = 0
Refined ground plane normal: [-0.04914807  0.0016219   0.99879019]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6230 points above ground threshold of 0.05m
DBSCAN found 23 clusters
Cluster with 4680 points: height = 0.332m
Cluster with 22 points: height = 0.072m
Cluster with 14 points: height = 0.079m
Cluster with 13 points: height = 0.076m
Cluster with 15 points: height = 0.071m
Cluster with 15 points: height = 0.076m
Cluster with 15 points: height = 0.074m
Cluster with 14 points: height = 0.070m
Cluster with 13 points: height = 0.070m
Cluster with 19 points: height = 0.074m
Cluster with 18 points: height = 0.076m
Cluster with 15 points: height = 0.067m
Cluster with 11 points: height = 0.069m
Cluster with 24 points: height = 0.073m
Cluster with 13 points: height = 0.070m
Cluster with 11 points: height = 0.076m
Cluster with 12 points: height = 0.071m
Cluster with 10 points: height = 0.070m
Cluster with 12 points: height = 0.070m
Cluster with 15 points: height = 0.067m
Cluster with 14 points: height = 0.075m
Cluster with 10 points: height = 0.076m
Cluster with 11 points: height = 0.072m
Final estimated crop height: 0.332m
I:app:计算得到作物高度: 0.332 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 5.76s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722161000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722161000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.88it/s]
Reading CSV chunks: 2it [00:00,  2.24it/s]
Reading CSV chunks: 2it [00:00,  2.18it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722161000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63636.759
Y: min=-2147483.750, max=2147483.750, mean=62992.977
Z: min=-2147483.750, max=2147483.750, mean=63259.746
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3948 statistical outliers (z-score > 3.0)
Subsampled from 196052 to 109333 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 109333 points
Estimating sensor pose...
PCA eigenvalues: [2.87703368e+11 2.53626531e+11 2.37444292e+11]
Estimated ground plane normal: [0.21017957 0.96824829 0.13535065]
Estimated rotation matrix:
[[ 0.96109092 -0.17924507 -0.21017957]
 [-0.17924507  0.17425973 -0.96824829]
 [ 0.21017957  0.96824829  0.13535065]]
Estimated translation vector: [      0.               0.         2183447.06287531]
Fitting ground plane and transforming point cloud...
Identified 2 ground points out of 109333 total points
Ground plane equation: 0.4851x + 0.0169y + 0.8743z + -0.0000 = 0
Refined ground plane normal: [0.48512954 0.01691297 0.87427872]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 18259 个异常高度点 (高于 0.498m)
显示 35 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722161000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722161000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722161000.gif，用时: 72.24s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722161000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722161000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 80.22s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 724|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:38 2025] POST /v1/detect_pointcloud => generated 219 bytes in 80247 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 724|app: 0|req: 3/3] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:35:45 2025] POST /v1/state => generated 70 bytes in 9 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250722/WX001_01_20250722160000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250722/WX001_01_20250722160000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722160000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250722160000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250722160000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 724) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 810)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250722/WX001_01_20250722160000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250722/WX001_01_20250722160000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722160000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250722160000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250722160000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 810) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 821)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 821|app: 0|req: 6/4] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:41:01 2025] POST /v1/shutdown => generated 22 bytes in 55 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559df7b350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x559df7b350 pid: 526 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 526)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722162000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722162000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722162000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722162000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722162000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722162000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.42it/s]
Reading CSV chunks: 2it [00:01,  1.85it/s]
Reading CSV chunks: 2it [00:01,  1.76it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722162000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=67904.988
Y: min=-2147483.750, max=2147483.750, mean=67124.260
Z: min=-2147483.750, max=2147483.750, mean=66722.114
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 58717 个点
Preprocessing point cloud with 58717 points...
Removed 0 points with NaN values
Removed 378 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 58339 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [5.90545555e+07 1.44150855e-02 4.36684918e-03]
Estimated ground plane normal: [4.83270301e-02 9.98831566e-01 2.69475093e-06]
Estimated rotation matrix:
[[ 9.97664504e-01 -4.82704331e-02 -4.83270301e-02]
 [-4.82704331e-02  2.33819039e-03 -9.98831566e-01]
 [ 4.83270301e-02  9.98831566e-01  2.69485093e-06]]
Estimated translation vector: [0.         0.         0.24415149]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 53380 ground points out of 58339 total points
Ground plane equation: -0.0678x + 0.0033y + 0.9977z + -0.0030 = 0
Refined ground plane normal: [-0.06780705  0.0032817   0.99769306]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6352 points above ground threshold of 0.05m
DBSCAN found 29 clusters
Cluster with 4689 points: height = 0.333m
Cluster with 11 points: height = 0.072m
Cluster with 17 points: height = 0.075m
Cluster with 17 points: height = 0.074m
Cluster with 21 points: height = 0.075m
Cluster with 13 points: height = 0.062m
Cluster with 10 points: height = 0.064m
Cluster with 11 points: height = 0.077m
Cluster with 11 points: height = 0.059m
Cluster with 16 points: height = 0.072m
Cluster with 16 points: height = 0.074m
Cluster with 25 points: height = 0.076m
Cluster with 13 points: height = 0.068m
Cluster with 17 points: height = 0.080m
Cluster with 17 points: height = 0.077m
Cluster with 16 points: height = 0.073m
Cluster with 18 points: height = 0.074m
Cluster with 11 points: height = 0.078m
Cluster with 22 points: height = 0.070m
Cluster with 14 points: height = 0.075m
Cluster with 14 points: height = 0.077m
Cluster with 13 points: height = 0.078m
Cluster with 13 points: height = 0.078m
Cluster with 14 points: height = 0.074m
Cluster with 11 points: height = 0.071m
Cluster with 15 points: height = 0.075m
Cluster with 18 points: height = 0.070m
Cluster with 10 points: height = 0.072m
Cluster with 10 points: height = 0.076m
Final estimated crop height: 0.333m
I:app:计算得到作物高度: 0.333 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.32s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722162000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722162000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.76it/s]
Reading CSV chunks: 2it [00:00,  2.06it/s]
Reading CSV chunks: 2it [00:00,  2.00it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722162000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=67904.988
Y: min=-2147483.750, max=2147483.750, mean=67124.260
Z: min=-2147483.750, max=2147483.750, mean=66722.114
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3871 statistical outliers (z-score > 3.0)
Subsampled from 196129 to 109864 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 109864 points
Estimating sensor pose...
PCA eigenvalues: [2.85635902e+11 2.50026812e+11 2.38569736e+11]
Estimated ground plane normal: [0.32987161 0.93055026 0.15893688]
Estimated rotation matrix:
[[ 0.90610768 -0.26486525 -0.32987161]
 [-0.26486525  0.2528292  -0.93055026]
 [ 0.32987161  0.93055026  0.15893688]]
Estimated translation vector: [      0.               0.         2219903.44277845]
Fitting ground plane and transforming point cloud...
E:app:群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I:app:点云识别完成，用时: 10.07s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 722|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 220 bytes in 10079 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 722|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:38:50 2025] POST /v1/shutdown => generated 22 bytes in 13 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5589524350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x5589524350 pid: 529 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 529)
spawned uWSGI worker 1 (pid: 725, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 725|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722163000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722163000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722163000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722163000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722163000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722163000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.64it/s]
Reading CSV chunks: 2it [00:01,  2.00it/s]
Reading CSV chunks: 2it [00:01,  1.94it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722163000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63163.493
Y: min=-2147483.750, max=2147483.750, mean=63464.519
Z: min=-2147483.750, max=2147483.750, mean=63154.559
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 61156 个点
Preprocessing point cloud with 61156 points...
Removed 0 points with NaN values
Removed 402 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 60754 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [5.84552523e+07 1.37371085e-02 4.06928868e-03]
Estimated ground plane normal: [4.08973826e-02 9.99163352e-01 2.43803944e-06]
Estimated rotation matrix:
[[ 9.98327408e-01 -4.08630662e-02 -4.08973826e-02]
 [-4.08630662e-02  1.67502996e-03 -9.99163352e-01]
 [ 4.08973826e-02  9.99163352e-01  2.43813944e-06]]
Estimated translation vector: [0.         0.         0.28633635]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 55891 ground points out of 60754 total points
Ground plane equation: -0.0465x + 0.0019y + 0.9989z + -0.0028 = 0
Refined ground plane normal: [-0.04653849  0.00190577  0.99891468]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6349 points above ground threshold of 0.05m
DBSCAN found 21 clusters
Cluster with 4826 points: height = 0.333m
Cluster with 12 points: height = 0.072m
Cluster with 24 points: height = 0.074m
Cluster with 15 points: height = 0.071m
Cluster with 13 points: height = 0.067m
Cluster with 18 points: height = 0.067m
Cluster with 15 points: height = 0.065m
Cluster with 14 points: height = 0.073m
Cluster with 19 points: height = 0.071m
Cluster with 18 points: height = 0.071m
Cluster with 13 points: height = 0.069m
Cluster with 14 points: height = 0.073m
Cluster with 15 points: height = 0.067m
Cluster with 15 points: height = 0.071m
Cluster with 13 points: height = 0.072m
Cluster with 21 points: height = 0.071m
Cluster with 10 points: height = 0.068m
Cluster with 12 points: height = 0.071m
Cluster with 12 points: height = 0.074m
Cluster with 11 points: height = 0.072m
Cluster with 15 points: height = 0.067m
Final estimated crop height: 0.333m
I:app:计算得到作物高度: 0.333 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.20s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722163000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722163000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.70it/s]
Reading CSV chunks: 2it [00:00,  2.60it/s]
Reading CSV chunks: 2it [00:00,  2.61it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722163000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63163.493
Y: min=-2147483.750, max=2147483.750, mean=63464.519
Z: min=-2147483.750, max=2147483.750, mean=63154.559
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3860 statistical outliers (z-score > 3.0)
Subsampled from 196140 to 109183 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 109183 points
Estimating sensor pose...
PCA eigenvalues: [2.84485532e+11 2.51056537e+11 2.38890101e+11]
Estimated ground plane normal: [0.13139038 0.98572252 0.10529801]
Estimated rotation matrix:
[[ 0.9843812  -0.11717605 -0.13139038]
 [-0.11717605  0.12091682 -0.98572252]
 [ 0.13139038  0.98572252  0.10529801]]
Estimated translation vector: [      0.               0.         2188342.69365204]
Fitting ground plane and transforming point cloud...
E:app:群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I:app:点云识别完成，用时: 9.88s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 725|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 219 bytes in 9890 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 725|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:38:50 2025] POST /v1/shutdown => generated 22 bytes in 13 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5587971350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x5587971350 pid: 527 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 527)
spawned uWSGI worker 1 (pid: 718, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 718|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722164000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722164000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722164000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722164000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722164000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722164000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.24it/s]
Reading CSV chunks: 2it [00:01,  1.68it/s]
Reading CSV chunks: 2it [00:01,  1.59it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722164000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63851.415
Y: min=-2147483.750, max=2147483.750, mean=63924.665
Z: min=-2147483.750, max=2147483.750, mean=63293.829
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 59116 个点
Preprocessing point cloud with 59116 points...
Removed 0 points with NaN values
Removed 395 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 58721 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [7.52383619e+07 1.47658989e-02 4.55305115e-03]
Estimated ground plane normal: [4.67470590e-02 9.98906759e-01 1.97386989e-06]
Estimated rotation matrix:
[[ 9.97814717e-01 -4.66958610e-02 -4.67470590e-02]
 [-4.66958610e-02  2.18725718e-03 -9.98906759e-01]
 [ 4.67470590e-02  9.98906759e-01  1.97396989e-06]]
Estimated translation vector: [0.         0.         0.21776643]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 53829 ground points out of 58721 total points
Ground plane equation: -0.0527x + 0.0025y + 0.9986z + -0.0028 = 0
Refined ground plane normal: [-0.05272613  0.00246807  0.99860596]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6284 points above ground threshold of 0.05m
DBSCAN found 22 clusters
Cluster with 4864 points: height = 0.334m
Cluster with 16 points: height = 0.065m
Cluster with 15 points: height = 0.070m
Cluster with 18 points: height = 0.069m
Cluster with 12 points: height = 0.064m
Cluster with 14 points: height = 0.373m
Cluster with 16 points: height = 0.069m
Cluster with 12 points: height = 0.066m
Cluster with 12 points: height = 0.069m
Cluster with 14 points: height = 0.068m
Cluster with 18 points: height = 0.070m
Cluster with 15 points: height = 0.067m
Cluster with 18 points: height = 0.065m
Cluster with 12 points: height = 0.074m
Cluster with 12 points: height = 0.069m
Cluster with 10 points: height = 0.069m
Cluster with 13 points: height = 0.066m
Cluster with 11 points: height = 0.067m
Cluster with 11 points: height = 0.066m
Cluster with 10 points: height = 0.066m
Cluster with 10 points: height = 0.061m
Cluster with 10 points: height = 0.068m
Final estimated crop height: 0.373m
I:app:计算得到作物高度: 0.373 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.59s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722164000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722164000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.84it/s]
Reading CSV chunks: 2it [00:00,  2.21it/s]
Reading CSV chunks: 2it [00:00,  2.14it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722164000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63851.415
Y: min=-2147483.750, max=2147483.750, mean=63924.665
Z: min=-2147483.750, max=2147483.750, mean=63293.829
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 4011 statistical outliers (z-score > 3.0)
Subsampled from 195989 to 110191 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 110191 points
Estimating sensor pose...
PCA eigenvalues: [2.69546536e+11 2.43492096e+11 2.23310981e+11]
Estimated ground plane normal: [0.27621611 0.93587228 0.21874126]
Estimated rotation matrix:
[[ 0.93739825 -0.21210655 -0.27621611]
 [-0.21210655  0.28134301 -0.93587228]
 [ 0.27621611  0.93587228  0.21874126]]
Estimated translation vector: [      0.               0.         2138001.04196779]
Fitting ground plane and transforming point cloud...
Identified 2 ground points out of 110191 total points
Ground plane equation: 0.3928x + 0.0978y + 0.9144z + -0.0000 = 0
Refined ground plane normal: [0.39275194 0.09780447 0.91442889]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 19228 个异常高度点 (高于 0.560m)
显示 481 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722164000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722164000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722164000.gif，用时: 72.59s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722164000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722164000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 78.38s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 718|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:41 2025] POST /v1/detect_pointcloud => generated 220 bytes in 78423 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 718|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:39:58 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x558e834350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x558e834350 pid: 522 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 522)
spawned uWSGI worker 1 (pid: 724, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 724|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:41 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722165000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722165000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722165000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722165000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722165000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722165000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.52it/s]
Reading CSV chunks: 2it [00:01,  2.05it/s]
Reading CSV chunks: 2it [00:01,  1.95it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722165000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63965.239
Y: min=-2147483.750, max=2147483.750, mean=63966.844
Z: min=-2147483.750, max=2147483.750, mean=62747.386
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 60960 个点
Preprocessing point cloud with 60960 points...
Removed 0 points with NaN values
Removed 460 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 60500 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [7.11190173e+07 1.36568259e-02 4.10494731e-03]
Estimated ground plane normal: [2.26061501e-02 9.99744448e-01 2.04111419e-06]
Estimated rotation matrix:
[[ 9.99488963e-01 -2.26003269e-02 -2.26061501e-02]
 [-2.26003269e-02  5.13078192e-04 -9.99744448e-01]
 [ 2.26061501e-02  9.99744448e-01  2.04121419e-06]]
Estimated translation vector: [0.         0.         0.27396714]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 55644 ground points out of 60500 total points
Ground plane equation: -0.0351x + 0.0008y + 0.9994z + -0.0029 = 0
Refined ground plane normal: [-3.51314437e-02  7.94772633e-04  9.99382384e-01]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6430 points above ground threshold of 0.05m
DBSCAN found 21 clusters
Cluster with 4873 points: height = 0.331m
Cluster with 10 points: height = 0.445m
Cluster with 19 points: height = 0.076m
Cluster with 20 points: height = 0.070m
Cluster with 16 points: height = 0.072m
Cluster with 19 points: height = 0.074m
Cluster with 20 points: height = 0.075m
Cluster with 17 points: height = 0.071m
Cluster with 10 points: height = 0.073m
Cluster with 16 points: height = 0.070m
Cluster with 14 points: height = 0.074m
Cluster with 12 points: height = 0.073m
Cluster with 19 points: height = 0.069m
Cluster with 18 points: height = 0.078m
Cluster with 14 points: height = 0.076m
Cluster with 12 points: height = 0.070m
Cluster with 18 points: height = 0.075m
Cluster with 10 points: height = 0.077m
Cluster with 10 points: height = 0.076m
Cluster with 10 points: height = 0.062m
Cluster with 15 points: height = 0.073m
Final estimated crop height: 0.445m
I:app:计算得到作物高度: 0.445 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 5.76s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722165000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722165000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.85it/s]
Reading CSV chunks: 2it [00:01,  1.86it/s]
Reading CSV chunks: 2it [00:01,  1.85it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722165000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=63965.239
Y: min=-2147483.750, max=2147483.750, mean=63966.844
Z: min=-2147483.750, max=2147483.750, mean=62747.386
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3825 statistical outliers (z-score > 3.0)
Subsampled from 196175 to 109257 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 109257 points
Estimating sensor pose...
PCA eigenvalues: [2.89118431e+11 2.57130715e+11 2.42402103e+11]
Estimated ground plane normal: [0.30192876 0.94153681 0.14949066]
Estimated rotation matrix:
[[ 0.92069446 -0.24730696 -0.30192876]
 [-0.24730696  0.2287962  -0.94153681]
 [ 0.30192876  0.94153681  0.14949066]]
Estimated translation vector: [      0.               0.         2220848.27137088]
Fitting ground plane and transforming point cloud...
Identified 2 ground points out of 109257 total points
Ground plane equation: 0.0005x + 0.1567y + 0.9877z + -0.0000 = 0
Refined ground plane normal: [4.84353721e-04 1.56652139e-01 9.87653721e-01]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 17408 个异常高度点 (高于 0.667m)
显示 426 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722165000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722165000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722165000.gif，用时: 73.67s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722165000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722165000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 81.64s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 724|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:41 2025] POST /v1/detect_pointcloud => generated 220 bytes in 81678 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 724|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:03 2025] POST /v1/shutdown => generated 22 bytes in 19 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55c8b82350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x55c8b82350 pid: 530 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 530)
spawned uWSGI worker 1 (pid: 726, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 726|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722170000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722170000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722170000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722170000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722170000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722170000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.54it/s]
Reading CSV chunks: 2it [00:01,  2.06it/s]
Reading CSV chunks: 2it [00:01,  1.96it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722170000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=67033.119
Y: min=-2147483.750, max=2147483.750, mean=66729.799
Z: min=-2147483.750, max=2147483.750, mean=67531.427
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 60498 个点
Preprocessing point cloud with 60498 points...
Removed 0 points with NaN values
Removed 448 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 60050 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [5.42735263e+07 1.36744364e-02 4.03209010e-03]
Estimated ground plane normal: [3.22466930e-02 9.99479940e-01 2.81997564e-06]
Estimated rotation matrix:
[[ 9.98960154e-01 -3.22298319e-02 -3.22466930e-02]
 [-3.22298319e-02  1.04266635e-03 -9.99479940e-01]
 [ 3.22466930e-02  9.99479940e-01  2.82007564e-06]]
Estimated translation vector: [0.         0.         0.66238227]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 54902 ground points out of 60050 total points
Ground plane equation: -0.0690x + 0.0022y + 0.9976z + -0.0030 = 0
Refined ground plane normal: [-0.06903891  0.00222845  0.99761148]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6394 points above ground threshold of 0.05m
DBSCAN found 25 clusters
Cluster with 4688 points: height = 0.332m
Cluster with 11 points: height = 0.074m
Cluster with 19 points: height = 0.080m
Cluster with 18 points: height = 0.079m
Cluster with 14 points: height = 0.080m
Cluster with 18 points: height = 0.072m
Cluster with 15 points: height = 0.078m
Cluster with 11 points: height = 0.083m
Cluster with 10 points: height = 0.074m
Cluster with 14 points: height = 0.076m
Cluster with 13 points: height = 0.074m
Cluster with 19 points: height = 0.074m
Cluster with 14 points: height = 0.073m
Cluster with 17 points: height = 0.077m
Cluster with 10 points: height = 0.069m
Cluster with 13 points: height = 0.072m
Cluster with 13 points: height = 0.073m
Cluster with 10 points: height = 0.074m
Cluster with 11 points: height = 0.071m
Cluster with 10 points: height = 0.079m
Cluster with 21 points: height = 0.077m
Cluster with 13 points: height = 0.077m
Cluster with 18 points: height = 0.071m
Cluster with 13 points: height = 0.070m
Cluster with 13 points: height = 0.082m
Final estimated crop height: 0.332m
I:app:计算得到作物高度: 0.332 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 3.44s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722170000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722170000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.52it/s]
Reading CSV chunks: 2it [00:00,  2.79it/s]
Reading CSV chunks: 2it [00:00,  2.75it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722170000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=67033.119
Y: min=-2147483.750, max=2147483.750, mean=66729.799
Z: min=-2147483.750, max=2147483.750, mean=67531.427
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3571 statistical outliers (z-score > 3.0)
Subsampled from 196429 to 110125 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 110125 points
Estimating sensor pose...
PCA eigenvalues: [3.13020537e+11 2.69956215e+11 2.64736589e+11]
Estimated ground plane normal: [-0.11913749  0.99161124  0.05013392]
Estimated rotation matrix:
[[ 0.98648387  0.11249811  0.11913749]
 [ 0.11249811  0.06365005 -0.99161124]
 [-0.11913749  0.99161124  0.05013392]]
Estimated translation vector: [      0.               0.         2231289.66707875]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 110125 total points
Ground plane equation: 0.0174x + -0.0395y + 0.9991z + 0.0000 = 0
Refined ground plane normal: [ 0.01740948 -0.03946011  0.99906947]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 15212 个异常高度点 (高于 0.498m)
显示 32 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722170000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722170000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722170000.gif，用时: 76.13s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722170000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722170000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 81.78s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 726|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:40 2025] POST /v1/detect_pointcloud => generated 221 bytes in 81850 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 726|app: 0|req: 3/3] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:40:01 2025] POST /v1/shutdown => generated 22 bytes in 18 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55a0b29350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 21 seconds on interpreter 0x55a0b29350 pid: 531 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 531)
spawned uWSGI worker 1 (pid: 724, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 724|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:37 2025] POST /v1/state => generated 70 bytes in 42 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722171000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722171000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722171000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722171000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722171000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722171000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.22it/s]
Reading CSV chunks: 2it [00:01,  1.61it/s]
Reading CSV chunks: 2it [00:01,  1.54it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722171000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=68057.554
Y: min=-2147483.750, max=2147483.750, mean=68793.300
Z: min=-2147483.750, max=2147483.750, mean=68714.918
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 59350 个点
Preprocessing point cloud with 59350 points...
Removed 0 points with NaN values
Removed 421 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 58929 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [7.06665949e+07 1.41038427e-02 4.32827008e-03]
Estimated ground plane normal: [3.30197869e-02 9.99454698e-01 2.11833700e-06]
Estimated rotation matrix:
[[ 9.98909696e-01 -3.30017112e-02 -3.30197869e-02]
 [-3.30017112e-02  1.09242245e-03 -9.99454698e-01]
 [ 3.30197869e-02  9.99454698e-01  2.11843700e-06]]
Estimated translation vector: [0.         0.         0.42649771]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 53926 ground points out of 58929 total points
Ground plane equation: -0.0647x + 0.0021y + 0.9979z + -0.0029 = 0
Refined ground plane normal: [-0.0646902   0.00213778  0.99790311]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6185 points above ground threshold of 0.05m
DBSCAN found 28 clusters
Cluster with 4659 points: height = 0.331m
Cluster with 11 points: height = 0.418m
Cluster with 10 points: height = 0.071m
Cluster with 11 points: height = 0.077m
Cluster with 21 points: height = 0.071m
Cluster with 15 points: height = 0.075m
Cluster with 18 points: height = 0.071m
Cluster with 21 points: height = 0.070m
Cluster with 10 points: height = 0.070m
Cluster with 14 points: height = 0.073m
Cluster with 11 points: height = 0.073m
Cluster with 16 points: height = 0.071m
Cluster with 16 points: height = 0.073m
Cluster with 11 points: height = 0.070m
Cluster with 12 points: height = 0.069m
Cluster with 14 points: height = 0.073m
Cluster with 15 points: height = 0.070m
Cluster with 10 points: height = 0.073m
Cluster with 19 points: height = 0.076m
Cluster with 11 points: height = 0.065m
Cluster with 18 points: height = 0.071m
Cluster with 12 points: height = 0.071m
Cluster with 18 points: height = 0.072m
Cluster with 11 points: height = 0.063m
Cluster with 11 points: height = 0.069m
Cluster with 18 points: height = 0.071m
Cluster with 12 points: height = 0.076m
Cluster with 10 points: height = 0.077m
Final estimated crop height: 0.418m
I:app:计算得到作物高度: 0.418 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 5.94s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722171000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722171000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.79it/s]
Reading CSV chunks: 2it [00:01,  1.79it/s]
Reading CSV chunks: 2it [00:01,  1.79it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722171000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=68057.554
Y: min=-2147483.750, max=2147483.750, mean=68793.300
Z: min=-2147483.750, max=2147483.750, mean=68714.918
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3993 statistical outliers (z-score > 3.0)
Subsampled from 196007 to 109812 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 109812 points
Estimating sensor pose...
PCA eigenvalues: [2.84644460e+11 2.50466575e+11 2.37439676e+11]
Estimated ground plane normal: [0.09647235 0.9940392  0.05078537]
Estimated rotation matrix:
[[ 0.9911429  -0.0912625  -0.09647235]
 [-0.0912625   0.05964248 -0.9940392 ]
 [ 0.09647235  0.9940392   0.05078537]]
Estimated translation vector: [      0.               0.         2166505.07032482]
Fitting ground plane and transforming point cloud...
Identified 1 ground points out of 109812 total points
Ground plane equation: 0.0037x + 0.1093y + 0.9940z + -0.0000 = 0
Refined ground plane normal: [0.00367655 0.10933145 0.99399855]
Creating 6 frame GIF with 360-degree rotation using Plotly...
过滤掉 15241 个异常高度点 (高于 0.626m)
显示 60 个合理范围内的作物点
  Generated frame 1/6 (angle: 0.0°)
  Generated frame 2/6 (angle: 60.0°)
  Generated frame 3/6 (angle: 120.0°)
  Generated frame 4/6 (angle: 180.0°)
  Generated frame 5/6 (angle: 240.0°)
  Generated frame 6/6 (angle: 300.0°)
Assembling GIF animation...
GIF animation saved to app/static/ftp/pc/pointcloudImage_20250722171000.gif
Animation: 6 frames, 360° rotation, 1s per frame
GIF size: 1200x900 (consistent with JPG)
I:app:成功生成GIF动画: app/static/ftp/pc/pointcloudImage_20250722171000.gif
I:app:三维点云图像已生成: app/static/ftp/pc/pointcloudImage_20250722171000.gif，用时: 80.44s
I:app:开始上传群落图
I:app:上传路径: pointCloudPicture/202507/20250722/ALC_WX001_20250722171000_TIMING.gif
FTP初始化成功!
220 (vsFTPd 3.0.3)
pointCloudPicture/202507/20250722/ALC_WX001_20250722171000_TIMING.gif
I:app:群落图上传成功
I:app:点云识别完成，用时: 88.77s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 724|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:37 2025] POST /v1/detect_pointcloud => generated 221 bytes in 88792 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 724|app: 0|req: 3/3] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:35:44 2025] POST /v1/state => generated 70 bytes in 9 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250722/WX001_01_20250722170000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250722/WX001_01_20250722170000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722170000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250722170000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250722170000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 724) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 809)
I:app:收到音频识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
audio/202507/20250722/WX001_01_20250722170000_TIMING.wav
I:app:音频文件下载成功: audio/202507/20250722/WX001_01_20250722170000_TIMING.wav -> app/static/ftp/sound/WX001_01_20250722170000_TIMING.wav
I:app:找到有效音频文件: WX001_01_20250722170000_TIMING.wav
I:app:开始处理音频文件: app/static/ftp/sound/WX001_01_20250722170000_TIMING.wav
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I NPUTransfer: Starting NPU Transfer Client, Transfer version 2.1.0 (b5861e7@2020-11-23T11:50:51)
D RKNNAPI: ==============================================
D RKNNAPI: RKNN VERSION:
D RKNNAPI:   API: 1.7.1 (566a9b6 build: 2021-10-28 14:56:17)
D RKNNAPI:   DRV: 1.6.0 (159d2d3 build: 2021-01-12 15:23:09)
D RKNNAPI: ==============================================
I:app:音频文件清理完成
I:app:临时文件清理完成
DAMN ! worker 1 (pid: 809) died :( trying respawn ...
Respawned uWSGI worker 1 (new pid: 820)
I:app:************* /v1/shutdown
I:app:************* /v1/shutdown 已返回响应
[pid: 820|app: 0|req: 6/4] ************* () {34 vars in 444 bytes} [Fri Jul 18 03:41:00 2025] POST /v1/shutdown => generated 22 bytes in 55 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:I will shutdown the systemc
sudo: effective uid is not 0, is /usr/bin/sudo on a file system with the 'nosuid' option set or an NFS file system without root privileges?
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55bc58d350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55bc58d350 pid: 529 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 529)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
I:app:************* /v1/state {'statusCode': '600', 'statusMsg': 'Identification service is ready!'}
[pid: 722|app: 0|req: 1/1] ************* () {34 vars in 438 bytes} [Fri Jul 18 03:33:41 2025] POST /v1/state => generated 70 bytes in 43 msecs (HTTP/1.1 200) 2 headers in 79 bytes (1 switches on core 0)
I:app:收到点云识别请求
FTP初始化成功!
220 (vsFTPd 3.0.3)
开始下载文件！
pointCloudCSV/202507/20250722/ALC_WX001_20250722172000_TIMING.csv
I:app:点云文件下载成功: pointCloudCSV/202507/20250722/ALC_WX001_20250722172000_TIMING.csv -> app/static/ftp/pc/ALC_WX001_20250722172000_TIMING.csv
I:app:找到有效点云文件: ALC_WX001_20250722172000_TIMING.csv
I:app:开始处理点云文件: app/static/ftp/pc/ALC_WX001_20250722172000_TIMING.csv
I:app:使用point_cloud先进算法进行高度识别
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722172000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  1.49it/s]
Reading CSV chunks: 2it [00:01,  2.00it/s]
Reading CSV chunks: 2it [00:01,  1.90it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722172000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=64689.157
Y: min=-2147483.750, max=2147483.750, mean=64116.822
Z: min=-2147483.750, max=2147483.750, mean=63871.544
I:app:加载点云数据: 200000 个点
I:app:裁剪到 1.0x1.0 米区域，剩余 60041 个点
Preprocessing point cloud with 60041 points...
Removed 0 points with NaN values
Removed 390 statistical outliers (z-score > 3.0)
Final preprocessed point cloud has 59651 points
I:app:完成点云预处理
Estimating sensor pose...
PCA eigenvalues: [6.03072591e+07 1.42021421e-02 4.26027548e-03]
Estimated ground plane normal: [4.12146794e-02 9.99150314e-01 2.45232070e-06]
Estimated rotation matrix:
[[ 9.98301354e-01 -4.11795589e-02 -4.12146794e-02]
 [-4.11795589e-02  1.70109805e-03 -9.99150314e-01]
 [ 4.12146794e-02  9.99150314e-01  2.45242070e-06]]
Estimated translation vector: [0.         0.         0.49373751]
I:app:完成传感器位姿估计
Fitting ground plane and transforming point cloud...
Identified 54734 ground points out of 59651 total points
Ground plane equation: -0.0265x + 0.0011y + 0.9996z + -0.0029 = 0
Refined ground plane normal: [-0.02646839  0.00109267  0.99964905]
I:app:完成地面拟合和坐标变换
Calculating crop height...
Found 6704 points above ground threshold of 0.05m
DBSCAN found 30 clusters
Cluster with 5059 points: height = 0.332m
Cluster with 23 points: height = 0.068m
Cluster with 16 points: height = 0.072m
Cluster with 14 points: height = 0.072m
Cluster with 14 points: height = 0.068m
Cluster with 17 points: height = 0.070m
Cluster with 13 points: height = 0.072m
Cluster with 18 points: height = 0.073m
Cluster with 11 points: height = 0.073m
Cluster with 17 points: height = 0.074m
Cluster with 22 points: height = 0.072m
Cluster with 13 points: height = 0.071m
Cluster with 15 points: height = 0.073m
Cluster with 13 points: height = 0.053m
Cluster with 10 points: height = 0.074m
Cluster with 16 points: height = 0.072m
Cluster with 12 points: height = 0.068m
Cluster with 19 points: height = 0.073m
Cluster with 15 points: height = 0.074m
Cluster with 12 points: height = 0.071m
Cluster with 12 points: height = 0.074m
Cluster with 13 points: height = 0.067m
Cluster with 14 points: height = 0.073m
Cluster with 20 points: height = 0.074m
Cluster with 13 points: height = 0.070m
Cluster with 11 points: height = 0.068m
Cluster with 14 points: height = 0.072m
Cluster with 11 points: height = 0.070m
Cluster with 10 points: height = 0.070m
Cluster with 11 points: height = 0.070m
Final estimated crop height: 0.332m
I:app:计算得到作物高度: 0.332 米
I:app:使用先进算法完成高度识别
I:app:高度计算完成，用时: 6.02s
I:app:开始生成群落图
I:app:动图设置: 3
I:app:输出文件路径: app/static/ftp/pc/pointcloudImage_20250722172000.gif
I:app:使用point_cloud先进算法生成可视化图像
Loading point cloud data from app/static/ftp/pc/ALC_WX001_20250722172000_TIMING.csv...

Reading CSV chunks: 0it [00:00, ?it/s]
Reading CSV chunks: 1it [00:00,  2.53it/s]
Reading CSV chunks: 2it [00:00,  2.76it/s]
Reading CSV chunks: 2it [00:00,  2.72it/s]
Loaded 200000 points from app/static/ftp/pc/ALC_WX001_20250722172000_TIMING.csv
Columns: ['Version', 'Slot ID', 'LiDAR Index', 'Rsvd', 'Error Code', 'Timestamp Type', 'Data Type', 'Timestamp', 'X', 'Y', 'Z', 'Reflectivity', 'Tag', 'Ori_x', 'Ori_y', 'Ori_z', 'Ori_radius', 'Ori_theta', 'Ori_phi']

Point cloud statistics:
X: min=-2147483.750, max=2147483.750, mean=64689.157
Y: min=-2147483.750, max=2147483.750, mean=64116.822
Z: min=-2147483.750, max=2147483.750, mean=63871.544
Preprocessing point cloud with 200000 points...
Removed 0 points with NaN values
Removed 3779 statistical outliers (z-score > 3.0)
Subsampled from 196221 to 110543 points
Performed spatial subsampling to reduce point density
Final preprocessed point cloud has 110543 points
Estimating sensor pose...
PCA eigenvalues: [2.85047660e+11 2.56041309e+11 2.37025181e+11]
Estimated ground plane normal: [0.30683583 0.92338839 0.2306635 ]
Estimated rotation matrix:
[[ 0.92349799 -0.2302243  -0.30683583]
 [-0.2302243   0.3071655  -0.92338839]
 [ 0.30683583  0.92338839  0.2306635 ]]
Estimated translation vector: [      0.               0.         2219801.15330027]
Fitting ground plane and transforming point cloud...
E:app:群落图生成失败: RANSAC could not find a valid consensus set. All `max_trials` iterations were skipped because each randomly chosen sub-sample failed the passing criteria. See estimator attributes for diagnostics (n_skips*).
I:app:点云识别完成，用时: 12.61s，成功处理 1 个文件
I:app:点云文件清理完成
[pid: 722|app: 0|req: 2/2] ************* () {34 vars in 464 bytes} [Fri Jul 18 03:33:41 2025] POST /v1/detect_pointcloud => generated 221 bytes in 12617 msecs (HTTP/1.1 200) 2 headers in 80 bytes (1 switches on core 0)
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55864a2350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x55864a2350 pid: 525 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 525)
spawned uWSGI worker 1 (pid: 724, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55a1446350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x55a1446350 pid: 533 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 533)
spawned uWSGI worker 1 (pid: 726, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x559a5f0350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x559a5f0350 pid: 525 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 525)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x5587847350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 20 seconds on interpreter 0x5587847350 pid: 529 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 529)
spawned uWSGI worker 1 (pid: 722, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
*** Starting uWSGI 2.0.21 (64bit) on [Fri Jul 18 03:32:58 2025] ***
compiled with version: 8.3.0 on 30 November 2022 02:54:50
os: Linux-4.4.194 #22 SMP Wed Oct 5 21:16:11 CST 2022
nodename: newsky
machine: aarch64
clock source: unix
pcre jit disabled
detected number of CPU cores: 6
current working directory: /home/<USER>/apps/web_server/versions/v1.15.0
writing pidfile to /home/<USER>/apps/web_server/current/uwsgi/uwsgi.pid
detected binary path: /home/<USER>/.local/bin/uwsgi
chdir() to /home/<USER>/apps/web_server/current
your processes number limit is 15060
your memory page size is 4096 bytes
detected max file descriptor number: 1024
lock engine: pthread robust mutexes
thunder lock: disabled (you can enable it with --thunder-lock)
uwsgi socket 0 bound to TCP address 127.0.0.1:5003 fd 3
Python version: 3.7.3 (default, Oct 31 2022, 14:04:00)  [GCC 8.3.0]
Python main interpreter initialized at 0x55aac9a350
python threads support enabled
your server socket listen backlog is limited to 128 connections
your mercy for graceful operations on workers is 60 seconds
mapped 145840 bytes (142 KB) for 1 cores
*** Operational MODE: single process ***
INFO:uwsgi_file__home_newsky_apps_web_server_current_manage:Starting application with config: development
/home/<USER>/.local/lib/python3.7/site-packages/rarfile.py:71: CryptographyDeprecationWarning: Python 3.7 is no longer supported by the Python core team and support for it is deprecated in cryptography. The next release of cryptography will remove support for Python 3.7.
  from cryptography.hazmat.backends import default_backend
/home/<USER>/.local/lib/python3.7/site-packages/torchvision/io/image.py:13: UserWarning: Failed to load image Python extension: 
  warn(f"Failed to load image Python extension: {e}")
Rga built version:7bf9abf 
I:uwsgi_file__home_newsky_apps_web_server_current_manage:Application will run on 127.0.0.1:5003
WSGI app 0 (mountpoint='') ready in 19 seconds on interpreter 0x55aac9a350 pid: 528 (default app)
*** uWSGI is running in multiple interpreter mode ***
spawned uWSGI master process (pid: 528)
spawned uWSGI worker 1 (pid: 723, cores: 1)
*** Stats server enabled on /home/<USER>/apps/web_server/current/uwsgi/uwsgi.status fd: 10 ***
