#!/bin/bash
# Web Server 启动脚本
# 适用于 current 软链接目录

set -e

# 获取脚本所在目录（current目录）
CURRENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_NAME="web_server"

# 日志函数
log_info() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_error() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] [ERROR] $1" >&2
}

# 检查是否已经运行
check_running() {
    local pid_file="$CURRENT_DIR/uwsgi/uwsgi.pid"
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "$APP_NAME 已经在运行中 (PID: $pid)"
            return 0
        else
            log_info "发现过期的PID文件，清理中..."
            rm -f "$pid_file"
        fi
    fi
    return 1
}

# 启动应用
start_app() {
    log_info "启动 $APP_NAME..."

    # 切换到应用目录
    cd "$CURRENT_DIR"

    # 检查是否已有进程在运行
    if pgrep -f "uwsgi.*$CURRENT_DIR" > /dev/null; then
        log_info "检测到已有uWSGI进程在运行，先停止..."
        pkill -f "uwsgi.*$CURRENT_DIR" || true
        sleep 2
    fi

    # 启动 uWSGI，使用绝对路径
    log_info "启动 uWSGI 服务..."
    log_info "配置文件: $CURRENT_DIR/uwsgi.ini"
    log_info "工作目录: $CURRENT_DIR"

    # 创建日志目录
    mkdir -p "$CURRENT_DIR/uwsgi"

    # 使用uwsgi命令启动，指定工作目录
    uwsgi --ini "$CURRENT_DIR/uwsgi.ini" \
          --chdir "$CURRENT_DIR" \
          --pidfile "$CURRENT_DIR/uwsgi/uwsgi.pid" \
          --stats "$CURRENT_DIR/uwsgi/uwsgi.status" \
          --daemonize "$CURRENT_DIR/uwsgi/uwsgi.log"

    # 等待启动完成
    sleep 5

    # 验证启动状态 - 检查多种方式
    local pid_file="$CURRENT_DIR/uwsgi/uwsgi.pid"
    local success=0

    # 方法1：检查PID文件
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "$APP_NAME 启动成功 (PID: $pid)"
            success=1
        fi
    fi

    # 方法2：检查进程是否存在
    if [ $success -eq 0 ]; then
        if pgrep -f "uwsgi.*$CURRENT_DIR" > /dev/null; then
            local pid=$(pgrep -f "uwsgi.*$CURRENT_DIR" | head -1)
            log_info "$APP_NAME 启动成功 (检测到进程 PID: $pid)"
            success=1
        fi
    fi

    # 方法3：检查端口监听
    if [ $success -eq 0 ]; then
        if netstat -tln | grep ":5003" > /dev/null; then
            log_info "$APP_NAME 启动成功 (端口5003正在监听)"
            success=1
        fi
    fi

    if [ $success -eq 1 ]; then
        return 0
    fi

    # 如果启动失败，显示日志
    log_error "$APP_NAME 启动失败"
    if [ -f "$CURRENT_DIR/uwsgi/uwsgi.log" ]; then
        log_error "错误日志："
        tail -10 "$CURRENT_DIR/uwsgi/uwsgi.log" >&2
    fi
    return 1
}

# 主逻辑
main() {
    log_info "开始启动 $APP_NAME 服务..."
    
    if check_running; then
        exit 0
    fi
    
    if start_app; then
        log_info "$APP_NAME 服务启动完成"
        exit 0
    else
        log_error "$APP_NAME 服务启动失败"
        exit 1
    fi
}

# 执行主逻辑
main "$@"
