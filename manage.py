# coding:utf-8
"""
Enhanced application management script with improved error handling and configuration
"""

import os
import sys
import logging
from app import create_app

# Setup basic logging for startup
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """Main application entry point"""
    try:
        # Get configuration from environment
        config_name = os.environ.get('FLASK_ENV', 'development')

        # Create application
        logger.info(f"Starting application with config: {config_name}")
        app = create_app(config_name)

        # Get host and port from config
        host = app.config.get('HOST', '127.0.0.1')
        port = app.config.get('PORT11111', 5003)
        debug = app.config.get('DEBUG', False)

        logger.info(f"Application will run on {host}:{port}")

        # Run application
        if __name__ == '__main__':
            app.run(host=host, port=port, debug=debug)

        return app

    except Exception as e:
        logger.error(f"Failed to start application: {e}")
        sys.exit(1)


# Create application instance for uWSGI
app = main()
