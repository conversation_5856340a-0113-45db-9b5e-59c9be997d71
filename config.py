
# coding:utf-8
"""
项目配置文件
"""

class Config(object):
    """基础配置类"""
    CACHE_TYPE = 'filesystem'
    CACHE_DIR = "app/tmp"


class DevelopmentConfig(Config):
    """开发环境配置"""
    DEBUG = True


class ProductionConfig(Config):
    """生产环境配置"""
    DEBUG = False


# 配置映射
config_map = {
    "develop": DevelopmentConfig,
    "product": ProductionConfig,
    "development": DevelopmentConfig,
    "production": ProductionConfig
}