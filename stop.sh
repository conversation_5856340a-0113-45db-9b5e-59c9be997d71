#!/bin/bash
# Web Server 停止脚本
# 适用于 current 软链接目录

set -e

# 获取脚本所在目录（current目录）
CURRENT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
APP_NAME="web_server"

# 日志函数
log_info() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] [INFO] $1"
}

log_error() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] [ERROR] $1" >&2
}

# 停止应用
stop_app() {
    log_info "停止 $APP_NAME..."
    
    local pid_file="$CURRENT_DIR/uwsgi/uwsgi.pid"
    
    # 检查PID文件是否存在
    if [ ! -f "$pid_file" ]; then
        log_info "$APP_NAME 未运行或PID文件不存在"
        return 0
    fi
    
    local pid=$(cat "$pid_file")
    
    # 检查进程是否存在
    if ! kill -0 "$pid" 2>/dev/null; then
        log_info "$APP_NAME 进程不存在，清理PID文件"
        rm -f "$pid_file"
        return 0
    fi
    
    # 尝试优雅停止
    log_info "发送TERM信号停止进程 (PID: $pid)..."
    kill -TERM "$pid"
    
    # 等待进程停止
    local count=0
    while kill -0 "$pid" 2>/dev/null && [ $count -lt 30 ]; do
        sleep 1
        count=$((count + 1))
    done
    
    # 检查是否成功停止
    if kill -0 "$pid" 2>/dev/null; then
        log_info "优雅停止失败，强制终止进程..."
        kill -KILL "$pid"
        sleep 2
    fi
    
    # 清理PID文件
    rm -f "$pid_file"
    
    log_info "$APP_NAME 停止成功"
}

# 主逻辑
main() {
    log_info "开始停止 $APP_NAME 服务..."
    
    stop_app
    
    log_info "$APP_NAME 服务停止完成"
}

# 执行主逻辑
main "$@"
